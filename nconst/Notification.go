package nconst

const (
	OK                  = 0
	CANCELED            = 1
	UNKNOWN             = 2
	INVALID_ARGUMENT    = 3
	DEADLINE_EXCEEDED   = 4
	NOT_FOUND           = 5
	ALREADY_EXISTS      = 6
	PERMISSION_DENIED   = 7
	RESOURCE_EXHAUSTED  = 8
	FAILED_PRECONDITION = 9
	ABORTED             = 10
	OUT_OF_RANGE        = 11
	UNIMPLEMENTED       = 12
	INTERNAL            = 13
	UNAVAILABLE         = 14
	DATA_LOSS           = 15
	UNAUTHENTICATED     = 16
)

// var (
// 	errBadInput           = runtime.NewError("input contained invalid data", INVALID_ARGUMENT)
// 	errInternalError      = runtime.NewError("internal server error", INTERNAL)
// 	errGuildAlreadyExists = runtime.NewError("guild name is in use", ALREADY_EXISTS)
// 	errFullGuild          = runtime.NewError("guild is full", RESOURCE_EXHAUSTED)
// 	errNotAllowed         = runtime.NewError("operation not allowed", PERMISSION_DENIED)
// 	errNoGuildFound       = runtime.NewError("guild not found", NOT_FOUND)
// )

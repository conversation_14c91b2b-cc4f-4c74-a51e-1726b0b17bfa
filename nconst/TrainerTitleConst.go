package nconst

import "go-nakama-poke/proto/MainServer"

func GetInitTeamTitleBy(team MainServer.TrainerTeam) MainServer.TrainerTitleType {
	switch team {
	case MainServer.TrainerTeam_TRAINER_TEAM_Rocket:
		return MainServer.TrainerTitleType_TrainerTitleTeamRocket
	case MainServer.TrainerTeam_TRAINER_TEAM_Magma:
		return MainServer.TrainerTitleType_TrainerTitleTeamMagma
	case MainServer.TrainerTeam_TRAINER_TEAM_Aqua:
		return MainServer.TrainerTitleType_TrainerTitleTeamAqua
	case MainServer.TrainerTeam_TRAINER_TEAM_Galactic:
		return MainServer.TrainerTitleType_TrainerTitleTeamGalactic
	case MainServer.TrainerTeam_TRAINER_TEAM_Plasma:
		return MainServer.TrainerTitleType_TrainerTitleTeamPlasma
	case MainServer.TrainerTeam_TRAINER_TEAM_Flare:
		return MainServer.TrainerTitleType_TrainerTitleTeamFlare
	case MainServer.TrainerTeam_TRAINER_TEAM_Skull:
		return MainServer.TrainerTitleType_TrainerTitleTeamSkull
	case MainServer.TrainerTeam_TRAINER_TEAM_Yell:
		return MainServer.TrainerTitleType_TrainerTitleTeamYell
	case MainServer.TrainerTeam_TRAINER_TEAM_Star:
		return MainServer.TrainerTitleType_TrainerTitleTeamStar
	}
	return MainServer.TrainerTitleType_TrainerTitleNone
}

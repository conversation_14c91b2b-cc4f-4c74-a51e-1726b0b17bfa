package nconst

import "go-nakama-poke/proto/MainServer"

const (
	Cloth_Id_Init_01 = "01"
	Cloth_Id_Init_02 = "02"
	Cloth_Id_Init_03 = "03"

	Cloth_Id_Team_Rocket_01 = "Rocket_01" //火箭队
	Cloth_Id_Team_Rocket_02 = "Rocket_02"
	Cloth_Id_Team_Rocket_03 = "Rocket_03"

	Cloth_Id_Team_Magma_01 = "Magma_01" //熔岩队
	Cloth_Id_Team_Magma_02 = "Magma_02"
	Cloth_Id_Team_Magma_03 = "Magma_03"

	Cloth_Id_Team_Aqua_01 = "Aqua_01" //海洋队
	Cloth_Id_Team_Aqua_02 = "Aqua_02"
	Cloth_Id_Team_Aqua_03 = "Aqua_03"

	Cloth_Id_Team_Galactic_01 = "Galactic_01" //银河队
	Cloth_Id_Team_Galactic_02 = "Galactic_02"
	Cloth_Id_Team_Galactic_03 = "Galactic_03"

	Cloth_Id_Team_Plasma_01 = "Plasma_01" //等离子队
	Cloth_Id_Team_Plasma_02 = "Plasma_02"
	Cloth_Id_Team_Plasma_03 = "Plasma_03"

	Cloth_Id_Team_Flare_01 = "Flare_01" //闪焰队
	Cloth_Id_Team_Flare_02 = "Flare_02"
	Cloth_Id_Team_Flare_03 = "Flare_03"

	Cloth_Id_Team_Skull_01 = "Skull_01" //骷髅队
	Cloth_Id_Team_Skull_02 = "Skull_02"
	Cloth_Id_Team_Skull_03 = "Skull_03"

	Cloth_Id_Team_Yell_01 = "Yell_01" //呐喊队
	Cloth_Id_Team_Yell_02 = "Yell_02"
	Cloth_Id_Team_Yell_03 = "Yell_03"

	Cloth_Id_Team_Star_01 = "Star_01" //天星队
	Cloth_Id_Team_Star_02 = "Star_02"
	Cloth_Id_Team_Star_03 = "Star_03"
)

func GetInitTeamClothIdBy(team MainServer.TrainerTeam) string {
	switch team {
	case MainServer.TrainerTeam_TRAINER_TEAM_Rocket:
		return Cloth_Id_Team_Rocket_01
	case MainServer.TrainerTeam_TRAINER_TEAM_Magma:
		return Cloth_Id_Team_Magma_01
	case MainServer.TrainerTeam_TRAINER_TEAM_Aqua:
		return Cloth_Id_Team_Aqua_01
	case MainServer.TrainerTeam_TRAINER_TEAM_Galactic:
		return Cloth_Id_Team_Galactic_01
	case MainServer.TrainerTeam_TRAINER_TEAM_Plasma:
		return Cloth_Id_Team_Plasma_01
	case MainServer.TrainerTeam_TRAINER_TEAM_Flare:
		return Cloth_Id_Team_Flare_01
	case MainServer.TrainerTeam_TRAINER_TEAM_Skull:
		return Cloth_Id_Team_Skull_01
	case MainServer.TrainerTeam_TRAINER_TEAM_Yell:
		return Cloth_Id_Team_Yell_01
	case MainServer.TrainerTeam_TRAINER_TEAM_Star:
		return Cloth_Id_Team_Star_01
	}
	return Cloth_Id_Init_01
}
func CheckAddTeam(team MainServer.TrainerTeam) bool {
	switch team {
	case MainServer.TrainerTeam_TRAINER_TEAM_NONE:
		return false
	}
	return true
}

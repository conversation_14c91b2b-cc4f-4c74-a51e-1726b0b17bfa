package item

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"

	// "go-nakama-poke/user"

	// "go-nakama-poke/user"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableItems = "items"
const MaximumStack int32 = 99999

func itemsToBase64(items []*MainServer.Item, logger runtime.Logger) (string, error) {
	content, err := json.Marshal(items)
	return string(content), err
	// messages := make([]proto.Message, len(items))
	// for i, item := range items {
	// 	messages[i] = item // poke 已经实现了 proto.Message 接口
	// }
	// return tool.ProtosToJson(messages)
	// return tool.ProtosToBase64(messages, logger)
}

func InitItems(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createItemsTableIfNotExists(ctx, logger, db, TableItems)
	LoadLocalItems()
}
func createItemsTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB, tableName string) {
	createTableSQL := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,              -- 自增长 ID
            tid BIGINT NOT NULL,              -- 用户 ID
            name VARCHAR(255) NOT NULL,            -- 物品名称
            count INT NOT NULL,                    -- 物品数量
            price DOUBLE PRECISION NOT NULL,       -- 物品价格
            sale_count INT NOT NULL,               -- 销售数量
            extra JSONB NOT NULL DEFAULT '{}'::jsonb,  -- 额外信息（JSON 格式）
            create_ts BIGINT NOT NULL,             -- 创建时间戳
            update_ts BIGINT NOT NULL              -- 更新时间戳
        );`, tableName)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("Failed to create table %s: %v", tableName, err)
	} else {
		logger.Info("Successfully created table %s", tableName)

		// 创建索引
		createIndexSQL := fmt.Sprintf(`
            CREATE INDEX IF NOT EXISTS idx_%[1]s_tid ON %[1]s (tid);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_name ON %[1]s (name);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_price ON %[1]s (price);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_count ON %[1]s (count);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_sale_count ON %[1]s (sale_count);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_update_ts ON %[1]s (update_ts);
        `, tableName)

		_, err = db.ExecContext(ctx, createIndexSQL)
		if err != nil {
			logger.Error("Failed to create indexes on table %s: %v", tableName, err)
		} else {
			logger.Info("Successfully created indexes on table %s", tableName)
		}
	}
}
func SaveItems(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid string, itemName string, count int32) (string, int32, int32, error) {
	var itemID string
	var currentCount int32
	var saleCount int32
	var updateTs int64

	// 查询现有物品记录
	query := fmt.Sprintf(`
		SELECT id, count, sale_count, update_ts 
		FROM %s 
		WHERE tid = $1 AND name = $2
	`, TableItems)

	err := tx.QueryRowContext(ctx, query, tid, itemName).Scan(&itemID, &currentCount, &saleCount, &updateTs)
	newCount := count // 默认 newCount 是传入的数量
	if err != nil {
		if err == sql.ErrNoRows {
			if newCount > MaximumStack {
				newCount = int32(MaximumStack)
			}
			// 如果没有记录，插入新物品
			insertSQL := fmt.Sprintf(`
				INSERT INTO %s (tid, name, count, price, sale_count, extra, create_ts, update_ts)
				VALUES ($1, $2, $3, 0, 0, '{}', $4, $4)
				RETURNING id
			`, TableItems)

			nowTs := time.Now().UnixMilli()
			err = tx.QueryRowContext(ctx, insertSQL, tid, itemName, newCount, nowTs).Scan(&itemID)
			if err != nil {
				logger.Error("创建物品记录失败: %v", err)
				return "", 0, 0, runtime.NewError("创建物品记录失败", 500)
			}

			logger.Info("成功创建新物品 %s，数量 %d", itemName, newCount)
			return itemID, newCount, saleCount, nil
		}

		// 如果查询时发生其他错误
		logger.Error("查询物品记录失败: %v", err)
		return "", 0, 0, runtime.NewError("查询物品记录失败", 500)
	}

	// 如果查询到了记录，更新物品
	newCount += currentCount // 新数量为现有数量加上传入数量
	if newCount > MaximumStack {
		newCount = int32(MaximumStack)
	}
	nowTs := time.Now().UnixMilli()
	updateSQL := fmt.Sprintf(`
		UPDATE %s
		SET count = $1, update_ts = $2
		WHERE id = $3 AND update_ts = $4
		RETURNING id
	`, TableItems)

	err = tx.QueryRowContext(ctx, updateSQL, newCount, nowTs, itemID, updateTs).Scan(&itemID)
	if err != nil {
		logger.Error("更新物品失败: %v", err)
		return "", 0, 0, runtime.NewError("更新物品失败", 500)
	}

	logger.Info("成功更新物品 %s，数量 %d", itemName, newCount)
	return itemID, newCount, saleCount, nil
}

func ChangeItemSaleCount(ctx context.Context, logger runtime.Logger, db *sql.DB, tid string, itemName string, saleCount int32) (string, error) {
	// 查询当前用户的 item 信息
	var currentCount int32
	var currentSaleCount int32
	var updateTs int64
	err := db.QueryRowContext(ctx, fmt.Sprintf(`
		SELECT count, sale_count, update_ts FROM %s WHERE tid = $1 AND name = $2
	`, TableItems), tid, itemName).Scan(&currentCount, &currentSaleCount, &updateTs)

	if err != nil {
		if err == sql.ErrNoRows {
			return "", runtime.NewError("物品不存在", 404)
		}
		logger.Error("查询物品失败: %v", err)
		return "", runtime.NewError("查询物品失败", 500)
	}

	// 计算总数
	totalCount := currentCount + currentSaleCount

	// 检查是否超过总数
	if saleCount > totalCount {
		return "", runtime.NewError("销售数量超过持有总数", 400)
	}

	// 计算新的 count 和 sale_count
	newCount := totalCount - saleCount
	nowTs := time.Now().UnixMilli()

	// 更新 item
	result, err := db.ExecContext(ctx, fmt.Sprintf(`
		UPDATE %s
		SET count = $1, sale_count = $2, update_ts = $3
		WHERE tid = $4 AND name = $5 AND update_ts = $6
	`, TableItems), newCount, saleCount, nowTs, tid, itemName, updateTs)

	if err != nil {
		logger.Error("更新物品销售数量失败: %v", err)
		return "", runtime.NewError("更新物品销售数量失败", 500)
	}

	// 检查更新是否成功（乐观锁保证）
	rowsAffected, err := result.RowsAffected()
	if err != nil || rowsAffected == 0 {
		return "", runtime.NewError("物品更新失败，请稍后重试", 409) // 乐观锁冲突
	}

	logger.Info("成功更新用户 %s 的物品 %s 销售数量至 %d", tid, itemName, saleCount)
	return "物品销售数量更新成功", nil
}

func queryItems(ctx context.Context, db *sql.DB, logger runtime.Logger, tid string, filter *MainServer.ItemFilter) ([]*MainServer.Item, error) {
	// 构建基础查询语句
	query := fmt.Sprintf(`
        SELECT id, tid, name, count, price, sale_count, extra, create_ts, update_ts
        FROM %s
        WHERE 1=1
    `, TableItems)

	// 参数数组
	var args []interface{}
	argIndex := 1

	// 根据名称筛选
	if len(filter.Names) > 0 {
		query += " AND name = ANY($1)"
		args = append(args, filter.Names)
		argIndex++
	}

	if tid != "" {
		query += fmt.Sprintf(" AND tid >= $%d", argIndex)
		args = append(args, tid)
		argIndex++
	}

	// 根据价格范围筛选
	if filter.MinPrice > 0 {
		query += fmt.Sprintf(" AND price >= $%d", argIndex)
		args = append(args, filter.MinPrice)
		argIndex++
	}
	if filter.MaxPrice > 0 {
		query += fmt.Sprintf(" AND price <= $%d", argIndex)
		args = append(args, filter.MaxPrice)
		argIndex++
	}

	// 根据销售状态筛选
	if filter.Sale {
		query += " AND sale_count > 0"
	}

	// 按价格排序
	if filter.Sort == MainServer.ItemFilterSort_price_i {
		query += " ORDER BY price"
	}

	// 添加分页支持
	if filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
		args = append(args, filter.PageSize, offset)
	}

	// 执行查询
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		logger.Error("Failed to query items: %v", err)
		return nil, err
	}
	defer rows.Close()

	// 构建结果
	var items []*MainServer.Item
	for rows.Next() {
		var item MainServer.Item
		var extra []byte
		if err := rows.Scan(&item.Id, &item.Tid, &item.Name, &item.Count, &item.Price, &item.SaleCount, &extra, &item.CreateTs, &item.UpdateTs); err != nil {
			logger.Error("Failed to scan item: %v", err)
			return nil, err
		}

		// 反序列化 extra 字段
		if err := json.Unmarshal(extra, &item.Extra); err != nil {
			logger.Warn("Failed to unmarshal extra field: %v", err)
		}

		items = append(items, &item)
	}

	return items, nil
}
func UseItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, itemName string, quantity int32) error {
	// 从 Items 表中读取道具数量和更新时间戳
	var currentQuantity int32
	var updateTs int64
	var itemId string

	query := fmt.Sprintf("SELECT id, count, update_ts FROM %s WHERE tid = $1 AND name = $2", TableItems)
	err := tx.QueryRowContext(ctx, query, tid, itemName).Scan(&itemId, &currentQuantity, &updateTs)
	if err != nil {
		if err == sql.ErrNoRows {
			logger.Warn("未找到用户 %d 的道具 %s", tid, itemName)
			return runtime.NewError("未找到指定道具", 404)
		}
		logger.Error("查询道具失败: %v", err)
		return fmt.Errorf("failed to retrieve item: %w", err)
	}

	// 检查数量是否足够
	if currentQuantity < quantity {
		logger.Warn("用户 %d 的道具 %s 数量不足: 当前数量 %d, 需要 %d", tid, itemName, currentQuantity, quantity)
		return runtime.NewError("道具数量不足", 400)
	}

	// 扣减数量
	newQuantity := currentQuantity - quantity
	nowTs := time.Now().UnixMilli()

	if newQuantity == 0 {
		// 如果数量为 0，直接删除记录
		deleteQuery := fmt.Sprintf("DELETE FROM %s WHERE id = $1", TableItems)
		_, err = tx.ExecContext(ctx, deleteQuery, itemId)
		if err != nil {
			logger.Error("删除道具失败: %v", err)
			return fmt.Errorf("failed to delete item: %w", err)
		}
		logger.Info("用户 %d 的道具 %s 已删除", tid, itemName)
	} else {
		// 更新记录
		updateQuery := fmt.Sprintf(`
			UPDATE %s 
			SET count = $1, update_ts = $2 
			WHERE id = $3 AND update_ts = $4`, TableItems)

		res, err := tx.ExecContext(ctx, updateQuery, newQuantity, nowTs, itemId, updateTs)
		if err != nil {
			logger.Error("更新道具失败: %v", err)
			return fmt.Errorf("failed to update item: %w", err)
		}

		// 检查更新结果，确保乐观锁未发生冲突
		rowsAffected, err := res.RowsAffected()
		if err != nil {
			logger.Error("获取更新行数失败: %v", err)
			return fmt.Errorf("failed to check update result: %w", err)
		}
		if rowsAffected == 0 {
			logger.Warn("更新用户 %d 的道具数据时发生版本冲突（update_ts 不匹配）", tid)
			return runtime.NewError("数据冲突，请重试", 409)
		}
		logger.Info("用户 %d 成功更新道具 %s 的数量至 %d", tid, itemName, newQuantity)
	}

	return nil
}

// useItem 负责检查并扣除用户道具
// func UseItem(ctx context.Context, logger runtime.Logger, db *sql.DB, tid int64, useItemInfo *MainServer.UseItemInfo) (string, error) {
// 	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	// tid := user.GetUserActiveTid(userID)
// 	tableName := TableItems

// 	// 从数据库读取用户的道具数据和更新时间戳（作为乐观锁）
// 	query := fmt.Sprintf("SELECT count, update_ts FROM %s WHERE tid = $1 AND name = $2", tableName)
// 	var currentQuantity int32
// 	var updateTs int64

// 	err := db.QueryRowContext(ctx, query, tid, useItemInfo.ItemName).Scan(&currentQuantity, &updateTs)
// 	if err != nil {
// 		logger.Error("未找到用户 %s 的道具 %s 数据: %v", tid, useItemInfo.ItemName, err)
// 		return "", runtime.NewError("未找到用户的道具信息", 404)
// 	}

// 	// 检查用户是否有足够的道具
// 	if currentQuantity < useItemInfo.Quantity {
// 		logger.Warn("用户 %s 的道具 %s 数量不足: 当前数量 %d, 需要 %d", tid, useItemInfo.ItemName, currentQuantity, useItemInfo.Quantity)
// 		return "", runtime.NewError("道具数量不足", 400)
// 	}

// 	// 扣除道具数量，并使用乐观锁字段 update_ts 进行更新
// 	newQuantity := currentQuantity - useItemInfo.Quantity
// 	newUpdateTs := time.Now().Unix() // 获取当前时间戳作为新的 update_ts

// 	// 使用 update_ts 进行乐观锁检查和更新
// 	updateQuery := fmt.Sprintf(`
// 		UPDATE %s
// 		SET count = $1, update_ts = $2
// 		WHERE tid = $3 AND name = $4 AND update_ts = $5`, tableName)

// 	res, _ := db.ExecContext(ctx, updateQuery, newQuantity, newUpdateTs, tid, useItemInfo.ItemName, updateTs)

// 	// 检查更新结果，确保乐观锁未发生冲突
// 	rowsAffected, err := res.RowsAffected()
// 	if err != nil {
// 		logger.Error("更新用户 %s 的道具 %s 数据失败: %v", tid, useItemInfo.ItemName, err)
// 		return "", runtime.NewError("道具数据保存失败", 500)
// 	}
// 	if rowsAffected == 0 {
// 		logger.Warn("更新用户 %s 的道具数据时发生版本冲突（update_ts 不匹配）", tid)
// 		return "", runtime.NewError("数据冲突，请重试", 409)
// 	}

// 	logger.Info("用户 %s 成功使用了 %d 个道具 %s", tid, useItemInfo.Quantity, useItemInfo.ItemName)
// 	return fmt.Sprintf("成功使用了 %d 个道具 %s", useItemInfo.Quantity, useItemInfo.ItemName), nil
// }

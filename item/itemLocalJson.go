package item

import (
	"go-nakama-poke/proto/MainServer"
	"log"
	"os"

	"google.golang.org/protobuf/encoding/protojson"
)

// 定义 localItems 的数据结构
// var localItems = make(map[int]map[string]*MainServer.LocalItem)
var localItems = &MainServer.LocalItemInfos{}

// Item 结构体，表示一个物品的属性
// type LocalItem struct {
// 	Name     string `json:"name"` // 物品名称
// 	Cost     int32  `json:"cost"` // 物品成本
// 	Type     int32  `json:"type"`
// 	Duration int32  `json:"duration"` // 物品持续时间
// }

func IsPokeBall(item *MainServer.LocalItem) bool {
	return item.InventoryType == MainServer.InventoryType_inventory_ball
}

// LoadLocalItems 负责加载和解析 items.json 数据
// func LoadLocalItems() {
// 	// 从文件读取 JSON 数据
// 	jsonFile, err := os.ReadFile("/nakama/data/items.json")
// 	if err != nil {
// 		log.Fatalf("Failed to read file: %v", err)
// 	}

// 	// 解析 JSON 数据并存储到全局变量中
// 	err = parse(string(jsonFile))
// 	if err != nil {
// 		log.Fatalf("Error parsing JSON: %v", err)
// 	}

//		fmt.Printf("Loaded items for %d categories\n", len(localItems))
//	}
func LoadLocalItems() {
	// 读取 JSON 文件
	data, err := os.ReadFile("/nakama/data/items.json")
	if err != nil {
		log.Fatalf("Failed to read pokedex.json file: %v", err)
	}
	// configs := &MainServer.PokeTeamConfigList{}
	// 解析 JSON 数据直接到全局变量
	if err := protojson.Unmarshal(data, localItems); err != nil {
		log.Fatalf("Failed to parse pokedex.json: %v", err)
	}
	// localItems = configs.Configs
	log.Printf("Successfully loaded localItems.")
}

// parse 函数负责解析 JSON 数据
// func parse(data string) error {
// 	// 定义临时数据结构，用于解析 items.json
// 	var rawItems map[string]map[string]*MainServer.LocalItem
// 	if err := json.Unmarshal([]byte(data), &rawItems); err != nil {
// 		return fmt.Errorf("failed to unmarshal JSON: %w", err)
// 	}
// 	// localItems = rawItems
// 	// 将解析后的数据转换为 localItems 的结构
// 	for category, items := range rawItems {
// 		// 初始化 category map
// 		categoryInt, _ := strconv.ParseInt(category, 10, 32)
// 		localItems[int(categoryInt)] = make(map[string]*MainServer.LocalItem)

// 		for itemName, item := range items {
// 			localItems[int(categoryInt)][itemName] = item
// 		}
// 	}

// 	return nil
// }

// 获取指定分类下的所有物品
func GetItemsByType(category MainServer.InventoryType) (map[string]*MainServer.LocalItem, bool) {
	items, exists := localItems.TypeItemList[int32(category)]
	return items.Items, exists
}

// 根据物品名称全局查找物品
func GetItemByName(itemName string) (*MainServer.LocalItem, bool) {
	item, exists := localItems.Items[itemName]
	// 首先遍历除了 "7" 键的所有项 //这边后续可以改成用carry类型进行判断
	// for key, items := range localItems {
	// 	if key != int(MainServer.InventoryType_inventory_carry) {
	// 		if item, exists := items[itemName]; exists {
	// 			return item, true
	// 		}
	// 	}
	// }

	// // 如果没有找到，再检查 "7" 键的项
	// if items, exists := localItems[int(MainServer.InventoryType_inventory_carry)]; exists {
	// 	if item, exists := items[itemName]; exists {
	// 		return item, true
	// 	}
	// }

	// 如果没有找到，返回 nil 和 false
	return item, exists
}

// 根据分类和物品名称获取物品
func GetItemByKeyAndName(category MainServer.InventoryType, itemName string) (*MainServer.LocalItem, bool) {
	items, exists := GetItemsByType(category)
	if !exists {
		return nil, false
	}

	item, exists := items[itemName]
	if !exists {
		return nil, false
	}

	return item, true
}

package item

import (
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strings"

	"github.com/heroiclabs/nakama-common/runtime"
)

func QueryItemsByNames(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)

	// 将字符串 payload 转换为 []int64
	names := strings.Split(payload, ",")
	var filter = &MainServer.ItemFilter{
		Names: names,
	}
	// 调用查询函数
	items, err := queryItems(ctx, db, logger, userID, filter)
	if err != nil {
		logger.Error("Failed to query pokes: %v", err)
		return "", err
	}
	return itemsToBase64(items, logger)
}

func QueryItemsByFilter(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	var filter = &MainServer.ItemFilter{}
	if err := tool.Base64ToProto(payload, filter); err != nil {
		logger.Error("解析查询 filter 失败: %v", err) // 记录解析失败
		return "", runtime.NewError("无效的请求数据", 400)
	}
	userID := ""
	if filter.Owner || !filter.Sale {
		userID = ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	}
	pokes, err := queryItems(ctx, db, logger, userID, filter)
	if err != nil {
		return "", err
	}
	return itemsToBase64(pokes, logger)
}

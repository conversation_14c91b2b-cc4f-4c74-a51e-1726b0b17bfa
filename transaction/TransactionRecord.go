package transaction

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 表名常量
const TableTransactionRecords = "transaction_records"

// 初始化交易记录表
func InitTransactionRecords(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTransactionRecordTableIfNotExists(ctx, logger, db)
}

// 创建交易记录表
func createTransactionRecordTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	createTableSQL := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id BIGSERIAL PRIMARY KEY,                    -- 自增长 ID
			tid BIGINT NOT NULL,                         -- 训练师ID
			transaction_type INT NOT NULL,               -- 交易类型 (购买Pokemon/道具/使用道具等)
			operation_type INT NOT NULL,                 -- 操作类型 (上架/下架/购买/使用等)
			item_type INT NOT NULL,                      -- 物品类型 (Pokemon/道具)
			item_id VARCHAR(255) NOT NULL,               -- 物品ID (Pokemon名称或道具ID)
			item_name VARCHAR(255) NOT NULL DEFAULT '',  -- 物品名称
			quantity INT NOT NULL DEFAULT 1,             -- 数量
			price BIGINT NOT NULL DEFAULT 0,             -- 价格 (金币)
			special_coin BIGINT NOT NULL DEFAULT 0,      -- 特殊货币
			seller_tid BIGINT NOT NULL DEFAULT 0,        -- 卖家训练师ID (如果是购买)
			buyer_tid BIGINT NOT NULL DEFAULT 0,         -- 买家训练师ID (如果是出售)
			market_id VARCHAR(255) NOT NULL DEFAULT '',  -- 市场交易ID
			extra_info JSONB NOT NULL DEFAULT '{}',      -- 额外信息 (JSON格式)
			transaction_ts BIGINT NOT NULL,              -- 交易时间戳
			create_ts BIGINT NOT NULL,                   -- 创建时间戳
			update_ts BIGINT NOT NULL                    -- 更新时间戳
		);
		
		-- 创建索引
		CREATE INDEX IF NOT EXISTS idx_%[1]s_tid ON %[1]s (tid);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_transaction_type ON %[1]s (transaction_type);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_operation_type ON %[1]s (operation_type);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_item_type ON %[1]s (item_type);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_item_id ON %[1]s (item_id);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_seller_tid ON %[1]s (seller_tid);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_buyer_tid ON %[1]s (buyer_tid);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_transaction_ts ON %[1]s (transaction_ts);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_create_ts ON %[1]s (create_ts);
		CREATE INDEX IF NOT EXISTS idx_%[1]s_composite ON %[1]s (tid, transaction_type, transaction_ts);
	`, TableTransactionRecords)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("Error creating table %s: %v", TableTransactionRecords, err)
		return err
	}

	logger.Info("Table %s created or already exists", TableTransactionRecords)
	return nil
}

// 使用MainServer中定义的类型
// 所有数据结构都直接使用proto定义：
// - MainServer.TransactionType
// - MainServer.TransactionOperationType
// - MainServer.TransactionItemType
// - MainServer.TransactionRecordInfo
// - MainServer.TransactionStatistics
// - MainServer.TransactionTypeStatistics

// 不再定义golang结构体，直接使用proto定义的 MainServer.TransactionRecordInfo

// AddTransactionRecord 添加交易记录
func AddTransactionRecord(ctx context.Context, logger runtime.Logger, tx *sql.Tx, record *MainServer.TransactionRecordInfo) error {
	if record == nil {
		return fmt.Errorf("transaction record is nil")
	}

	nowTs := time.Now().UnixMilli()
	if record.TransactionTs == 0 {
		record.TransactionTs = nowTs
	}
	if record.CreateTs == 0 {
		record.CreateTs = nowTs
	}
	record.UpdateTs = nowTs

	insertSQL := fmt.Sprintf(`
		INSERT INTO %s (tid, transaction_type, operation_type, item_type, item_id, item_name, 
		                quantity, price, special_coin, seller_tid, buyer_tid, market_id, 
		                extra_info, transaction_ts, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
		RETURNING id
	`, TableTransactionRecords)

	err := tx.QueryRowContext(ctx, insertSQL,
		record.Tid,
		int(record.TransactionType),
		int(record.OperationType),
		int(record.ItemType),
		record.ItemId,
		record.ItemName,
		record.Quantity,
		record.Price,
		record.SpecialCoin,
		record.SellerTid,
		record.BuyerTid,
		record.MarketId,
		record.ExtraInfo,
		record.TransactionTs,
		record.CreateTs,
		record.UpdateTs,
	).Scan(&record.Id)

	if err != nil {
		logger.Error("Failed to add transaction record: %v", err)
		return err
	}

	logger.Info("Added transaction record: id=%d, tid=%d, type=%v, operation=%v",
		record.Id, record.Tid, record.TransactionType, record.OperationType)
	return nil
}

// GetTransactionRecords 获取交易记录列表
func GetTransactionRecords(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64,
	transactionType *MainServer.TransactionType, operationType *MainServer.TransactionOperationType,
	itemType *MainServer.TransactionItemType, limit int32, offset int32) ([]*MainServer.TransactionRecordInfo, error) {

	whereConditions := []string{"tid = $1"}
	args := []interface{}{tid}
	argIndex := 2

	if transactionType != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("transaction_type = $%d", argIndex))
		args = append(args, int(*transactionType))
		argIndex++
	}

	if operationType != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("operation_type = $%d", argIndex))
		args = append(args, int(*operationType))
		argIndex++
	}

	if itemType != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("item_type = $%d", argIndex))
		args = append(args, int(*itemType))
		argIndex++
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + fmt.Sprintf("%s", whereConditions[0])
		for i := 1; i < len(whereConditions); i++ {
			whereClause += " AND " + whereConditions[i]
		}
	}

	limitClause := ""
	if limit > 0 {
		limitClause = fmt.Sprintf(" LIMIT $%d", argIndex)
		args = append(args, limit)
		argIndex++

		if offset > 0 {
			limitClause += fmt.Sprintf(" OFFSET $%d", argIndex)
			args = append(args, offset)
		}
	}

	query := fmt.Sprintf(`
		SELECT id, tid, transaction_type, operation_type, item_type, item_id, item_name,
		       quantity, price, special_coin, seller_tid, buyer_tid, market_id,
		       extra_info, transaction_ts, create_ts, update_ts
		FROM %s
		%s
		ORDER BY transaction_ts DESC, id DESC
		%s
	`, TableTransactionRecords, whereClause, limitClause)

	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		logger.Error("Failed to query transaction records: %v", err)
		return nil, err
	}
	defer rows.Close()

	var records []*MainServer.TransactionRecordInfo
	for rows.Next() {
		record := &MainServer.TransactionRecordInfo{}
		var transactionType, operationType, itemType int

		err := rows.Scan(
			&record.Id,
			&record.Tid,
			&transactionType,
			&operationType,
			&itemType,
			&record.ItemId,
			&record.ItemName,
			&record.Quantity,
			&record.Price,
			&record.SpecialCoin,
			&record.SellerTid,
			&record.BuyerTid,
			&record.MarketId,
			&record.ExtraInfo,
			&record.TransactionTs,
			&record.CreateTs,
			&record.UpdateTs,
		)
		if err != nil {
			logger.Error("Failed to scan transaction record: %v", err)
			return nil, err
		}

		record.TransactionType = MainServer.TransactionType(transactionType)
		record.OperationType = MainServer.TransactionOperationType(operationType)
		record.ItemType = MainServer.TransactionItemType(itemType)

		records = append(records, record)
	}

	logger.Info("Found %d transaction records for tid %d", len(records), tid)
	return records, nil
}

// UpdateTransactionRecord 更新交易记录
func UpdateTransactionRecord(ctx context.Context, logger runtime.Logger, tx *sql.Tx, recordId int64, updates map[string]interface{}) error {
	if len(updates) == 0 {
		return fmt.Errorf("no updates provided")
	}

	// 构建更新语句
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	for field, value := range updates {
		setParts = append(setParts, fmt.Sprintf("%s = $%d", field, argIndex))
		args = append(args, value)
		argIndex++
	}

	// 添加更新时间戳
	setParts = append(setParts, fmt.Sprintf("update_ts = $%d", argIndex))
	args = append(args, time.Now().UnixMilli())
	argIndex++

	// 添加WHERE条件
	args = append(args, recordId)

	// 构建完整的SET子句
	setClause := ""
	for i, part := range setParts {
		if i > 0 {
			setClause += ", "
		}
		setClause += part
	}

	updateSQL := fmt.Sprintf(`
		UPDATE %s
		SET %s
		WHERE id = $%d
	`, TableTransactionRecords, setClause, argIndex)

	result, err := tx.ExecContext(ctx, updateSQL, args...)
	if err != nil {
		logger.Error("Failed to update transaction record: %v", err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("Failed to get rows affected: %v", err)
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("transaction record with id %d not found", recordId)
	}

	logger.Info("Updated transaction record: id=%d, affected_rows=%d", recordId, rowsAffected)
	return nil
}

// DeleteTransactionRecord 删除交易记录
func DeleteTransactionRecord(ctx context.Context, logger runtime.Logger, tx *sql.Tx, recordId int64) error {
	deleteSQL := fmt.Sprintf(`
		DELETE FROM %s WHERE id = $1
	`, TableTransactionRecords)

	result, err := tx.ExecContext(ctx, deleteSQL, recordId)
	if err != nil {
		logger.Error("Failed to delete transaction record: %v", err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("Failed to get rows affected: %v", err)
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("transaction record with id %d not found", recordId)
	}

	logger.Info("Deleted transaction record: id=%d", recordId)
	return nil
}

// GetTransactionRecordById 根据ID获取交易记录
func GetTransactionRecordById(ctx context.Context, logger runtime.Logger, tx *sql.Tx, recordId int64) (*MainServer.TransactionRecordInfo, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, transaction_type, operation_type, item_type, item_id, item_name,
		       quantity, price, special_coin, seller_tid, buyer_tid, market_id,
		       extra_info, transaction_ts, create_ts, update_ts
		FROM %s
		WHERE id = $1
	`, TableTransactionRecords)

	record := &MainServer.TransactionRecordInfo{}
	var transactionType, operationType, itemType int

	err := tx.QueryRowContext(ctx, query, recordId).Scan(
		&record.Id,
		&record.Tid,
		&transactionType,
		&operationType,
		&itemType,
		&record.ItemId,
		&record.ItemName,
		&record.Quantity,
		&record.Price,
		&record.SpecialCoin,
		&record.SellerTid,
		&record.BuyerTid,
		&record.MarketId,
		&record.ExtraInfo,
		&record.TransactionTs,
		&record.CreateTs,
		&record.UpdateTs,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("transaction record with id %d not found", recordId)
		}
		logger.Error("Failed to get transaction record by id: %v", err)
		return nil, err
	}

	record.TransactionType = MainServer.TransactionType(transactionType)
	record.OperationType = MainServer.TransactionOperationType(operationType)
	record.ItemType = MainServer.TransactionItemType(itemType)

	return record, nil
}

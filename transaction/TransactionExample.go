package transaction

import (
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// ExampleUsage 展示交易记录系统的使用方法
func ExampleUsage(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	// 1. 初始化交易记录表
	InitTransactionRecords(ctx, logger, db)

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 示例训练师ID
	trainerTid := int64(12345)
	sellerTid := int64(67890)

	// 2. 记录购买宝可梦
	logger.Info("=== 记录购买宝可梦 ===")
	err = RecordPokemonPurchase(ctx, logger, tx,
		trainerTid,    // 买家ID
		sellerTid,     // 卖家ID
		"pikachu_001", // 宝可梦ID
		"皮卡丘",         // 宝可梦名称
		1000,          // 价格
		0,             // 特殊货币
		"market_001",  // 市场交易ID
	)
	if err != nil {
		logger.Error("记录购买宝可梦失败: %v", err)
		return err
	}

	// 3. 记录购买道具
	logger.Info("=== 记录购买道具 ===")
	err = RecordItemPurchase(ctx, logger, tx,
		trainerTid, // 买家ID
		0,          // 卖家ID (系统商店)
		"potion",   // 道具ID
		"治疗药水",     // 道具名称
		5,          // 数量
		250,        // 价格
		0,          // 特殊货币
		"shop_001", // 商店交易ID
	)
	if err != nil {
		logger.Error("记录购买道具失败: %v", err)
		return err
	}

	// 4. 记录使用道具
	logger.Info("=== 记录使用道具 ===")
	extraInfo := map[string]interface{}{
		"target_pokemon": "pikachu_001",
		"hp_restored":    50,
		"location":       "battle_field_01",
	}
	err = RecordItemUsage(ctx, logger, tx,
		trainerTid, // 使用者ID
		"potion",   // 道具ID
		"治疗药水",     // 道具名称
		1,          // 使用数量
		extraInfo,  // 额外信息
	)
	if err != nil {
		logger.Error("记录使用道具失败: %v", err)
		return err
	}

	// 5. 记录市场上架
	logger.Info("=== 记录市场上架 ===")
	err = RecordMarketListing(ctx, logger, tx,
		trainerTid, // 卖家ID
		MainServer.TransactionItemType_TransactionItemType_Pokemon, // 物品类型
		"charmander_002", // 物品ID
		"小火龙",            // 物品名称
		1,                // 数量
		1500,             // 价格
		0,                // 特殊货币
		"market_002",     // 市场ID
	)
	if err != nil {
		logger.Error("记录市场上架失败: %v", err)
		return err
	}

	// 6. 记录市场下架
	logger.Info("=== 记录市场下架 ===")
	err = RecordMarketDelisting(ctx, logger, tx,
		trainerTid, // 卖家ID
		MainServer.TransactionItemType_TransactionItemType_Pokemon, // 物品类型
		"charmander_002", // 物品ID
		"小火龙",            // 物品名称
		1,                // 数量
		"market_002",     // 市场ID
	)
	if err != nil {
		logger.Error("记录市场下架失败: %v", err)
		return err
	}

	// 7. 获取购买历史
	logger.Info("=== 获取购买历史 ===")
	purchaseHistory, err := GetPurchaseHistory(ctx, logger, tx, trainerTid, nil, 10, 0)
	if err != nil {
		logger.Error("获取购买历史失败: %v", err)
		return err
	}
	logger.Info("购买历史记录数: %d", len(purchaseHistory))
	for i, record := range purchaseHistory {
		logger.Info("  [%d] %s: %s x%d, 价格: %d",
			i+1, record.ItemType, record.ItemName, record.Quantity, record.Price)
	}

	// 8. 获取使用历史
	logger.Info("=== 获取使用历史 ===")
	usageHistory, err := GetUsageHistory(ctx, logger, tx, trainerTid, 10, 0)
	if err != nil {
		logger.Error("获取使用历史失败: %v", err)
		return err
	}
	logger.Info("使用历史记录数: %d", len(usageHistory))
	for i, record := range usageHistory {
		logger.Info("  [%d] 使用 %s x%d, 额外信息: %s",
			i+1, record.ItemName, record.Quantity, record.ExtraInfo)
	}

	// 9. 获取市场操作历史
	logger.Info("=== 获取市场操作历史 ===")
	marketHistory, err := GetMarketHistory(ctx, logger, tx, trainerTid, nil, 10, 0)
	if err != nil {
		logger.Error("获取市场操作历史失败: %v", err)
		return err
	}
	logger.Info("市场操作历史记录数: %d", len(marketHistory))
	for i, record := range marketHistory {
		logger.Info("  [%d] %s: %s %s x%d",
			i+1, record.OperationType, record.ItemType, record.ItemName, record.Quantity)
	}

	// 10. 获取交易统计
	logger.Info("=== 获取交易统计 ===")
	statistics, err := GetTransactionStatistics(ctx, logger, tx, trainerTid, 7)
	if err != nil {
		logger.Error("获取交易统计失败: %v", err)
		return err
	}
	logger.Info("交易统计: %+v", statistics)

	// 11. 获取最近交易
	logger.Info("=== 获取最近交易 ===")
	recentTransactions, err := GetRecentTransactions(ctx, logger, tx, trainerTid, 5)
	if err != nil {
		logger.Error("获取最近交易失败: %v", err)
		return err
	}
	logger.Info("最近交易记录数: %d", len(recentTransactions))
	for i, record := range recentTransactions {
		logger.Info("  [%d] %s %s: %s x%d",
			i+1, record.TransactionType, record.OperationType, record.ItemName, record.Quantity)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		logger.Error("提交事务失败: %v", err)
		return err
	}

	logger.Info("=== 交易记录系统示例完成 ===")
	return nil
}

// ExampleQueryOperations 展示查询操作的示例
func ExampleQueryOperations(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	trainerTid := int64(12345)

	// 1. 查询特定类型的交易
	logger.Info("=== 查询特定类型的交易 ===")

	// 查询购买宝可梦的记录
	transactionType := MainServer.TransactionType_TransactionType_Purchase
	operationType := MainServer.TransactionOperationType_TransactionOperationType_Buy
	itemType := MainServer.TransactionItemType_TransactionItemType_Pokemon

	pokemonPurchases, err := GetTransactionRecords(ctx, logger, tx, trainerTid,
		&transactionType, &operationType, &itemType, 10, 0)
	if err != nil {
		logger.Error("查询宝可梦购买记录失败: %v", err)
		return err
	}
	logger.Info("宝可梦购买记录数: %d", len(pokemonPurchases))

	// 查询道具使用记录
	transactionType = MainServer.TransactionType_TransactionType_Use
	operationType = MainServer.TransactionOperationType_TransactionOperationType_Use
	itemType = MainServer.TransactionItemType_TransactionItemType_Item

	itemUsages, err := GetTransactionRecords(ctx, logger, tx, trainerTid,
		&transactionType, &operationType, &itemType, 10, 0)
	if err != nil {
		logger.Error("查询道具使用记录失败: %v", err)
		return err
	}
	logger.Info("道具使用记录数: %d", len(itemUsages))

	// 2. 更新交易记录示例
	logger.Info("=== 更新交易记录示例 ===")
	if len(pokemonPurchases) > 0 {
		recordId := pokemonPurchases[0].Id
		updates := map[string]interface{}{
			"extra_info": `{"updated": true, "note": "记录已更新"}`,
		}

		err = UpdateTransactionRecord(ctx, logger, tx, recordId, updates)
		if err != nil {
			logger.Error("更新交易记录失败: %v", err)
			return err
		}
		logger.Info("成功更新交易记录 ID: %d", recordId)
	}

	// 3. 根据ID获取交易记录
	logger.Info("=== 根据ID获取交易记录 ===")
	if len(pokemonPurchases) > 0 {
		recordId := pokemonPurchases[0].Id
		record, err := GetTransactionRecordById(ctx, logger, tx, recordId)
		if err != nil {
			logger.Error("根据ID获取交易记录失败: %v", err)
			return err
		}
		logger.Info("获取到交易记录: ID=%d, 类型=%v, 物品=%s",
			record.Id, record.TransactionType, record.ItemName)
	}

	err = tx.Commit()
	if err != nil {
		logger.Error("提交事务失败: %v", err)
		return err
	}

	logger.Info("=== 查询操作示例完成 ===")
	return nil
}

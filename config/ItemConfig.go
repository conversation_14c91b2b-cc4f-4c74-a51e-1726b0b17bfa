package config

import (
	"context"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

var _itemConfig = map[MainServer.TrainerActionType]map[string]bool{
	MainServer.TrainerActionType_idle: {
		"item1": true,
		"item2": false,
	},
	MainServer.TrainerActionType_battle: {
		"item1": false,
		"item2": true,
	},
}

func GetItemConfig(ctx context.Context, nk runtime.NakamaModule, itemID string, actionType MainServer.TrainerActionType) (bool, error) {
	return true, nil
	// if actionConfig, exists := _itemConfig[actionType][itemID]; exists {
	// 	return actionConfig, nil
	// }
	// action := actionType != MainServer.TrainerActionType_battle
	// return action, runtime.NewError("未找到道具配置", 500)
}

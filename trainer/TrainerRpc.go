package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/config"
	"go-nakama-poke/email"
	"go-nakama-poke/inventory"
	"go-nakama-poke/nconst"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strconv"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

func Initiation(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	initTrainers(ctx, logger, db)
	initPokeBoxs(ctx, logger, db)
	initTrainerQuestTable(ctx, logger, db)
}

// 随身
func RpcGetTrainerAroundPokes(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("Not found tid", 404)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	singlePokeBox, err := GetSinglePokeBox(ctx, logger, tx, trainer, MainServer.PokeBoxType_around, 0, 0)
	tool.AddOrUpdateTrainerAroundPokes(trainer.Id, singlePokeBox.Pokes)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	trainer.SessionInfo.BattlePokes = singlePokeBox.Pokes
	result := &MainServer.PokesResult{
		Pokes: singlePokeBox.Pokes,
	}
	return tool.ProtoToBase64(result)
}

func RpcGetTrainerBoxAndPokesByBoxInfo(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("Not found tid", 404)
	}
	var boxInfo = &MainServer.SinglePokeBoxParam{}
	err := tool.Base64ToProto(payload, boxInfo)
	if err != nil {
		return "", runtime.NewError("data err", 400)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	singlePokeBox, err := GetSinglePokeBox(ctx, logger, tx, trainer, boxInfo.BoxType, boxInfo.Index, boxInfo.UpdateTs)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return tool.ProtoToBase64(singlePokeBox)
}

//	func RpcGetTrainerPokesByBox(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
//		trainer := tool.GetActiveTrainerByCtx(ctx)
//		if trainer == nil {
//			return "", runtime.NewError("Not found tid", 404)
//		}
//		// Step 1: 开始事务
//		tx, err := db.BeginTx(ctx, nil)
//		if err != nil {
//			logger.Error("failed to begin transaction: %v", err)
//			return "", fmt.Errorf("failed to begin transaction: %w", err)
//		}
//		defer tx.Rollback()
//		singlePokeBox, err := GetSinglePokeBox(ctx, logger, tx, trainer, MainServer.PokeBoxType_around, 0, 0)
//		if err != nil {
//			return "", err
//		}
//		// 提交事务
//		if err := tx.Commit(); err != nil {
//			logger.Error("事务提交失败: %v", err)
//			return "", fmt.Errorf("failed to commit transaction: %w", err)
//		}
//		result := &MainServer.PokesResult{
//			Pokes: singlePokeBox.Pokes,
//		}
//		return tool.ProtoToBase64(result)
//	}

// 需要确定是否需要加入最后的战斗或者最后的party
func RpcSelectTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	tid, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}

	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer, err := SelectTrainerProto(ctx, db, tid)
	if err != nil || trainer.Uid != userID {
		return "", runtime.NewError("not found tid in uid", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// 检查并扩容盒子
	if err := checkAndExpandBoxes(ctx, logger, tx, trainer); err != nil {
		return "", err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	trainer.SessionInfo = &MainServer.TrainerSessionInfo{}

	nowTs := time.Now().UnixMilli()
	if nowTs-trainer.SessionInfo.SessionEndTs < config.ReconnectTime {
		//return 旧的 party
	}

	trainer.SessionInfo.SessionEndTs = 0
	tool.SetUserActiveTrainer(userID, trainer)
	trainerSelectResult := &MainServer.TrainerSelectResult{
		Trainer: trainer,
		Config: &MainServer.GameConfig{
			OneUserMaxBoxCount:        oneUserMaxBoxCount,
			OneUserSpecialMaxBoxCount: oneUserSpecialMaxBoxCount,
		},
	}
	return tool.ProtoToBase64(trainerSelectResult)
}
func RpcGetTrainerInfo(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// 查询训练师数据
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("tid found", 400)
	}
	// 返回训练师信息
	return tool.ProtoToBase64(trainer)
}

func RpcAllTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// 查询训练师数据
	trainers, err := SelectTrainersByUID(ctx, db, userID)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve trainers: %v", err)
	}
	// 将 trainers 转换为 JSON
	// trainersJSON, err := json.Marshal(trainers)
	// if err != nil {
	// 	return "", fmt.Errorf("failed to convert trainers to JSON: %v", err)
	// }
	result := &MainServer.TrainersResult{
		Trainers:  trainers,
		CurrentTs: time.Now().Unix(),
	}
	// trainersJSON, err = tool.ProtoToBase64(result)

	// 返回 JSON 字符串
	return tool.ProtoToBase64(result)
}

func RpcTrainerNameValid(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	unique, err := isNameValid(ctx, db, payload)
	if err != nil {
		return "", fmt.Errorf("failed to begin createPoke: %v", err)
	}
	if !unique {
		return "", fmt.Errorf("trainer Name err")
	}
	return "", nil
}

// 更新自己的位置，同时获取周围的训练家信息和自己同一个party的训练家信息
func RpcUpdateUserLoc(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	trainerLocInfo := &MainServer.TrainerLocInfo{}
	err := tool.Base64ToProto(payload, trainerLocInfo)
	if err != nil {
		return "", runtime.NewError("data err", 400)
	}
	if trainerLocInfo.Loc == nil {
		return "", nil
	}
	point := fmt.Sprintf("[%.2f %.2f %.2f]",
		trainerLocInfo.Loc.X,
		trainerLocInfo.Loc.Y,
		trainerLocInfo.Loc.Z)
	// loc := fmt.Sprintf("%s|[%.2f %.2f %.2f]",
	// 	trainerLocInfo.Loc.ReginId,
	// 	trainerLocInfo.Loc.X,
	// 	trainerLocInfo.Loc.Y,
	// 	trainerLocInfo.Loc.Z)
	// data := strings.Split(loc, "|") // region|[0,1]
	// if len(data) < 2 {
	// 	return "", runtime.NewError("data err", 400)
	// }
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("tid found", 400)
	}
	if trainerLocInfo.Loc.MainLandType != MainServer.MainLandType_MainLand_None {
		trainer.ActionInfo.LastMainLand.Loc = trainerLocInfo.Loc
		if trainerLocInfo.PcName != "" {
			trainer.ActionInfo.LastMainLand.LastPcName = trainerLocInfo.PcName
		}
	}
	trainer.SessionInfo.LocInfo = trainerLocInfo
	// loc, err := tool.ProtoToBase64(&MainServer.TrainerLocInfo{
	// 	Loc: &MainServer.TrainerLoc{
	// 		ReginId: trainerLocInfo.Loc.ReginId,
	// 		X:       trainerLocInfo.Loc.X,
	// 		Y:       trainerLocInfo.Loc.Y,
	// 		Z:       trainerLocInfo.Loc.Z,
	// 	},
	// })
	if err != nil {
		return "", err
	}
	trainer.ActionInfo.Loc = trainerLocInfo.Loc
	result, err := updateUserLoc(ctx, logger, trainer, trainerLocInfo.Loc.ReginId, point)
	if err != nil {
		return "", err
	}
	partyInfo, exists := tool.GetGlobalPartyMap().Get(trainer.Id)
	if exists {
		partyTrainers := make([]*MainServer.Trainer, len(partyInfo.Trainers))
		for _, v := range partyInfo.Trainers {
			partyTrainers = append(partyTrainers, tool.CopySafeTrainer((v)))
		}
		result.PartyTrainers = partyTrainers
	}

	nowTime := time.Now().UnixMilli()
	if nowTime-trainer.UpdateTs < 3000 { //距离最后一次更新如果大于3000就不更新了
		trainer.OnlineTime = nowTime - trainer.UpdateTs
	}
	// trainer.OnlineTime = trainer.OnlineTime + 30 //看看这个多久被调用一次
	defer func() {
		// 	partyInfo, exists := tool.GetGlobalPartyMap().Get(userId)
		// if !exists {
		// 	partyInfo = &tool.PartyInfo{
		// 		Trainers: make(map[int64]*MainServer.Trainer),
		// 	}
		// }
		//300000 毫秒 约等于 300秒 = 5分钟 test 0.5分钟
		if trainer.UpdateTs+config.TrainerUpdateInterval < nowTime {
			//更新一下
			uptrainer, err := UpdateTrainer(ctx, logger, db, trainer)
			if err != nil {
				trainer.UpdateTs = uptrainer.UpdateTs
			}
		}
	}()
	return tool.ProtoToBase64(result)
}

func RpcCreateTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	count := userTrainerCount(db, userID, 0, true)
	if count >= 6 {
		return "", runtime.NewError("Trainer count max: "+strconv.Itoa(count), 400)
		// return "", fmt.Errorf("Trainer count max: %d", count)
	}
	trainer := &MainServer.Trainer{}
	err := tool.Base64ToProto(payload, trainer)
	if err != nil {
		// 创建 Trainer 对象
		trainer = &MainServer.Trainer{
			Uid:    userID,
			Name:   "Ash Ketchum",
			Gender: MainServer.Gender_M,
			ActionInfo: &MainServer.TrainerActionInfo{
				Action: MainServer.TrainerActionType_battle,
			},
			PokeIds:    []string{"poke_001", "poke_002", "poke_003"},
			Items:      map[string]*MainServer.TrainerItemInfo{},
			Badges:     &MainServer.TrainerBadges{},
			Team:       0,
			GroupId:    "group_101",
			Cloth:      &MainServer.TrainerCloth{},
			StrictInfo: &MainServer.TrainerStrictInfo{},
			CreateTs:   time.Now().UnixMilli(),
			UpdateTs:   time.Now().UnixMilli(),
		}
	}
	trainer.Uid = userID
	return createTrainer(ctx, logger, db, nk, trainer)
}
func RpcTraienrAllPokeBox(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	boxs, err := GetPokeBoxsByTs(ctx, logger, db, MainServer.PokeBoxType_normal, 0)
	if err != nil {
		return "", err
	}
	result := &MainServer.PokeBoxResult{
		Boxs: boxs,
		Ts:   time.Now().UnixMilli(),
	}
	return tool.ProtoToBase64(result)
}

func RpcBatchExchangePokeBox(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	exchange := &MainServer.PokeBatchBoxExchange{}
	err := tool.Base64ToProto(payload, exchange) //MainServer.PokeBoxExchange
	if err != nil {
		return "", err
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	updateTs, err := BatchExchangePokeBox(ctx, logger, tx, exchange.Exchanges)
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return strconv.FormatInt(updateTs, 10), err
}

func RpcRecoverAroundPokes(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	pokes, exists := tool.GetTrainerAroundPokes(trainer.Id)
	if !exists {
		return "", runtime.NewError("未找到周围宝可梦", 404)
	}
	// 遍历所有宝可梦，恢复它们的状态
	for _, poke := range pokes {
		poke.HpSub = 0
	}
	return "", nil
}
func RpcUpdateFollowPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	param := &MainServer.FollowPokeParam{}
	err := tool.Base64ToProto(payload, param)
	// pokeId, err := strconv.ParseInt(payload, 10, 64)
	// pokeId, ex := tool.GetIntFromPayload("pid", payload)
	if err != nil {
		return "", fmt.Errorf("not found pokeid")
	}
	// remove, _ := tool.GetBoolFromPayload("remove", payload)
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	_, err = UpdateFollowPoke(ctx, logger, tx, param)
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return "", err
}

func RpcBuyPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	pokeId, ex := tool.GetIntFromPayload("pid", payload)
	if !ex {
		return "", fmt.Errorf("not found pokeid")
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	pokeinfo, err := BuyPoke(ctx, logger, tx, pokeId)
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return pokeinfo, err
}

func RpcUnsalePokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	pid, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	pokemon, err := UnsalePokemon(ctx, logger, tx, trainer.Id, pid, MainServer.TransactionType_TransactionType_Market_Sale)
	if err != nil {
		return "", fmt.Errorf("failed to unsale pokemon: %v", err)
	}
	_, err = SavePokeToNormalBox(ctx, logger, trainer, tx, pokemon)
	if err != nil {
		return "", fmt.Errorf("failed to save pokemon: %v", err)
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return "", nil
}

func RpcSalePokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	param := &MainServer.RpcSalePokemonRequest{}
	err := tool.Base64ToProto(payload, param)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	pokeId, err := SalePokemon(ctx, tx, trainer, param.BoxType, param.BoxIndex, param.Pid, param.Price)
	if err != nil {
		return "", fmt.Errorf("failed to sale pokemon: %v", err)
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return strconv.FormatInt(pokeId, 10), err
}

func RpcBorrowPoke(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	pokeId, ex := tool.GetIntFromPayload("pid", payload)
	if !ex {
		return "", fmt.Errorf("not found pokeid")
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	pokeinfo, err := BuyPoke(ctx, logger, tx, pokeId)
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}

	return pokeinfo, err
}

// 出租
func RpcRentPokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	param := &MainServer.RpcSalePokemonRequest{}
	err := tool.Base64ToProto(payload, param)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	pokeId, err := RentPokemon(ctx, tx, trainer, param.BoxType, param.BoxIndex, param.Pid, param.Price, param.RentDay, param.CanRenewed)
	if err != nil {
		return "", fmt.Errorf("failed to sale pokemon: %v", err)
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return strconv.FormatInt(pokeId, 10), err
}

func RpcUnrentPokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	pid, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	// Step 1: 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("failed to begin transaction: %v", err)
		return "", fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	pokemon, err := UnsalePokemon(ctx, logger, tx, trainer.Id, pid, MainServer.TransactionType_TransactionType_Market_Rent)
	if err != nil {
		return "", fmt.Errorf("failed to unsale pokemon: %v", err)
	}
	_, err = SavePokeToNormalBox(ctx, logger, trainer, tx, pokemon)
	if err != nil {
		return "", fmt.Errorf("failed to save pokemon: %v", err)
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("事务提交失败: %v", err)
		return "", fmt.Errorf("failed to commit transaction: %w", err)
	}
	return "", nil
}

// func RpcUpdateParty(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	//
// }

// RpcBreedPokemon 宝可梦繁殖RPC函数
// 输入参数: 父母宝可梦的ID，繁殖道具信息，繁殖次数
// 返回: 新生宝可梦信息，父母是否死亡的信息
func RpcBreedPokemon(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 定义请求结构
	var request MainServer.BreedPokemonRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("开始事务失败: "+err.Error(), 500)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	if !BoxContainsPoke(ctx, tx, trainer.Id, request.PokeInfo_1.PokeBox, request.PokeInfo_1.PokeBoxLoc, request.PokeInfo_1.PokeId, request.PokeInfo_1.PokeBoxType) {
		return "", runtime.NewError("宝可梦不在盒子中", 400)
	}
	if !BoxContainsPoke(ctx, tx, trainer.Id, request.PokeInfo_2.PokeBox, request.PokeInfo_1.PokeBoxLoc, request.PokeInfo_2.PokeId, request.PokeInfo_2.PokeBoxType) {
		return "", runtime.NewError("宝可梦不在盒子中", 400)
	}

	// 获取父母宝可梦
	poke1 := poke.QueryPokeById(ctx, tx, trainer.Id, request.PokeInfo_1.PokeId)
	if poke1 == nil {
		return "", runtime.NewError("获取父方宝可梦失败: "+err.Error(), 404)
	}

	poke2 := poke.QueryPokeById(ctx, tx, trainer.Id, request.PokeInfo_2.PokeId)
	if poke2 == nil {
		return "", runtime.NewError("获取母方宝可梦失败: "+err.Error(), 404)
	}

	// 检查是否可以繁殖，并获取正确的父方和母方
	fatherPoke, motherPoke, canBreed := poke.CanBreed(poke1, poke2)
	if !canBreed {
		return "", runtime.NewError("这对宝可梦不能繁殖", 400)
	}
	fatherItems := &MainServer.BreedingItems{}
	motherItems := &MainServer.BreedingItems{}
	fatherBreedPokeInfo := &MainServer.SimpleBoxPokeInfo{}
	motherBreedPokeInfo := &MainServer.SimpleBoxPokeInfo{}
	if fatherPoke.Id == poke1.Id {
		fatherItems = request.Items_1
		motherItems = request.Items_2
		fatherBreedPokeInfo = request.PokeInfo_1
		motherBreedPokeInfo = request.PokeInfo_2
	} else {
		fatherItems = request.Items_2
		motherItems = request.Items_1
		fatherBreedPokeInfo = request.PokeInfo_2
		motherBreedPokeInfo = request.PokeInfo_1
	}
	// 检查和应用繁殖道具
	cleanFatherItem, cleanMotherItem, err := CheckBreedingItems(ctx, tx, trainer.Id, fatherPoke.ItemName, motherPoke.ItemName, fatherItems, motherItems)
	if err != nil {
		return "", err
	}

	// 执行繁殖
	baby, fatherDied, motherDied, err := poke.BreedPokemon(ctx, tx, fatherPoke, motherPoke, fatherItems, motherItems)
	if err != nil {
		return "", runtime.NewError("繁殖失败: "+err.Error(), 400)
	}

	// 处理死亡状态
	if fatherDied {
		fatherPoke.Status.LiveStatus = MainServer.PokeLiveStatus_PokeLiveStatus_Dead
		_, err = ReleasePoke(ctx, logger, tx, trainer, fatherBreedPokeInfo.PokeBoxType, int32(fatherBreedPokeInfo.PokeBox), int32(fatherBreedPokeInfo.PokeBoxLoc))
		// fatherExchange := &MainServer.PokeBoxExchange{
		// 	SourceBox:     int32(fatherBreedPokeInfo.PokeBox),
		// 	SourceBoxType: fatherBreedPokeInfo.PokeBoxType,
		// 	SourceLoc:     int32(fatherBreedPokeInfo.PokeBoxLoc),
		// 	IsDelete:      true,
		// }
		// _, err = ExchangePokeBox(ctx, logger, tx, fatherExchange)
		if err != nil {
			return "", runtime.NewError("删除父方宝可梦失败: "+err.Error(), 500)
		}
		fatherPoke.Release = true
	}
	fatherPoke.BreedCount++
	if cleanFatherItem {
		fatherPoke.ItemName = ""
	}
	err = poke.UpdatePokeData(ctx, tx, fatherPoke)
	if err != nil {
		return "", runtime.NewError("更新父方宝可梦状态失败: "+err.Error(), 500)
	}

	if motherDied {
		motherPoke.Status.LiveStatus = MainServer.PokeLiveStatus_PokeLiveStatus_Dead
		_, err = ReleasePoke(ctx, logger, tx, trainer, motherBreedPokeInfo.PokeBoxType, int32(motherBreedPokeInfo.PokeBox), int32(motherBreedPokeInfo.PokeBoxLoc))
		if err != nil {
			return "", runtime.NewError("删除母方宝可梦失败: "+err.Error(), 500)
		}
		motherPoke.Release = true
		// motherExchange := &MainServer.PokeBoxExchange{
		// 	SourceBox:     int32(motherBreedPokeInfo.PokeBox),
		// 	SourceBoxType: motherBreedPokeInfo.PokeBoxType,
		// 	SourceLoc:     int32(motherBreedPokeInfo.PokeBoxLoc),
		// 	IsDelete:      true,
		// }
		// _, err = ExchangePokeBox(ctx, logger, tx, motherExchange)
		// if err != nil {
		// 	return "", runtime.NewError("删除母方宝可梦失败: "+err.Error(), 500)
		// }
	}
	motherPoke.BreedCount++
	if cleanMotherItem {
		motherPoke.ItemName = ""
	}
	err = poke.UpdatePokeData(ctx, tx, motherPoke)
	if err != nil {
		return "", runtime.NewError("更新母方宝可梦状态失败: "+err.Error(), 500)
	}

	// 设置新宝可梦所属训练师ID
	baby.Tid = trainer.Id
	err = poke.UpdatePokeData(ctx, tx, baby)
	if err != nil {
		return "", runtime.NewError("更新新生宝可梦状态失败: "+err.Error(), 500)
	}
	// 将新宝可梦添加到训练师的盒子中
	_, err = SaveNewPokeToNormalBoxPriorityBoxType(ctx, logger, trainer, MainServer.PokeBoxType_hatch, tx, baby)
	// _, err = SavePokeToNormalBox(ctx, logger, trainer, tx, baby)
	if err != nil {
		return "", runtime.NewError("保存新宝可梦到盒子失败: "+err.Error(), 500)
	}

	// 构建响应结果
	result := &MainServer.BreedPokemonResponse{
		Baby:       baby,
		FatherDied: fatherDied,
		MotherDied: motherDied,
		FatherId:   fatherPoke.Id,
		MotherId:   motherPoke.Id,
	}

	return tool.ProtoToBase64(result)
}

// Check and apply breeding items for both parents, and verify inventory sufficiency
func CheckBreedingItems(ctx context.Context, tx *sql.Tx, trainerId int64, fatherItemName, motherItemName string, fatherItems, motherItems *MainServer.BreedingItems) (cleanFatherItem bool, cleanMotherItem bool, err error) {
	type itemInfo struct {
		name  string
		count int
	}
	cleanFatherItem = false
	cleanMotherItem = false
	addStatItemsCount := func(count1 int32, count2 int32) int {
		count := 0
		if count1 > 0 {
			count = int(count) + int(count1)
		}
		if count2 > 0 {
			count = int(count) + int(count2)
		}
		return count
	}
	// Helper to apply stat item effect
	checkItems := []itemInfo{
		{"powerweight", addStatItemsCount(fatherItems.StatItems.Hp, motherItems.StatItems.Hp)},
		{"powerbracer", addStatItemsCount(fatherItems.StatItems.Atk, motherItems.StatItems.Atk)},
		{"powerbelt", addStatItemsCount(fatherItems.StatItems.Def, motherItems.StatItems.Def)},
		{"powerlens", addStatItemsCount(fatherItems.StatItems.Spa, motherItems.StatItems.Spa)},
		{"powerband", addStatItemsCount(fatherItems.StatItems.Spd, motherItems.StatItems.Spd)},
		{"poweranklet", addStatItemsCount(fatherItems.StatItems.Spe, motherItems.StatItems.Spe)},
		{"destinyknot", func() int {
			count := 0
			if fatherItems.RedString {
				count++
			}
			if motherItems.RedString {
				count++
			}
			return count
		}()},
	}
	for _, item := range checkItems {
		if item.count > 0 {
			sufficient, err := inventory.CheckItemQuantity(ctx, tx, trainerId, item.name, item.count)
			if err != nil {
				return false, false, err
			}
			if !sufficient {
				return false, false, runtime.NewError("insufficient items", 403)
			}
		}
	}
	//携带的道具不需要进行判断
	applyStatItem := func(itemName string, items *MainServer.BreedingItems) bool {
		switch itemName {
		case "powerweight":
			items.StatItems.Hp = 1
		case "powerbracer":
			items.StatItems.Atk = 1
		case "powerbelt":
			items.StatItems.Def = 1
		case "powerlens":
			items.StatItems.Spa = 1
		case "powerband":
			items.StatItems.Spd = 1
		case "poweranklet":
			items.StatItems.Spe = 1
		case "destinyknot":
			items.RedString = true
		default:
			return false
		}
		return true
	}
	cleanFatherItem = applyStatItem(fatherItemName, fatherItems)
	cleanMotherItem = applyStatItem(motherItemName, motherItems)

	return cleanFatherItem, cleanMotherItem, nil
}
func RpcHatchEgg(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 定义请求结构
	var request MainServer.HatchEggRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("开始事务失败: "+err.Error(), 500)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	hatchPoke(ctx, logger, tx, trainer, &request)
	// 获取宝可梦
	// pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, request.PokeId)
	// if pokemon == nil {
	// 	return "", runtime.NewError("获取宝可梦失败: "+err.Error(), 404)
	// }
	// if !pokemon.Egg {
	// 	return "", runtime.NewError("不是蛋", 400)
	// }
	// pokemon.Egg = false
	return "", nil
}

// RpcEvolveByLevel 宝可梦等级进化RPC函数
// 输入参数: 宝可梦ID和目标进化形态
// 返回: 进化后的宝可梦信息
func RpcEvolveByLevel(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 定义请求结构
	var request MainServer.EvolveByLevelRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError("开始事务失败: "+err.Error(), 500)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	// 获取宝可梦
	pokemon := poke.QueryPokeById(ctx, tx, trainer.Id, request.PokeId)
	if pokemon == nil {
		return "", runtime.NewError("获取宝可梦失败: "+err.Error(), 404)
	}

	// 检查等级要求
	// evoData, exists := poke.GetPokemonEvolutionInfo(poke.Name, request.TargetEvolution)
	// if !exists {
	// 	return "", runtime.NewError("无效的进化路径", 400)
	// }

	// if evoData.Method != "level-up" || poke.Level < evoData.Level {
	// 	return "", runtime.NewError("不满足进化条件", 400)
	// }

	// 执行进化
	evolvedPoke, err := poke.EvolveByLevel(ctx, tx, pokemon, request.TargetEvolution)
	if err != nil {
		return "", runtime.NewError("进化失败: "+err.Error(), 400)
	}

	// 构建响应结果
	// result := &MainServer.EvolveByLevelResponse{
	// 	Poke: evolvedPoke,
	// }

	return tool.ProtoToBase64(evolvedPoke)
}

// RPC处理函数

// RpcGetEmailList 获取邮件列表RPC处理函数
func RpcGetEmailList(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 404)
	}
	// 解析请求
	var request MainServer.EmailListRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	emails, err := email.GetEmailList(ctx, logger, db, trainer.Id, request.Page, request.PageSize, !request.IsFilterAll)
	if err != nil {
		return "", err
	}
	response := &MainServer.EmailListResponse{
		Emails: emails,
	}
	return tool.ProtoToBase64(response)
}

// RpcEmailOp 邮件操作RPC处理函数
func RpcEmailOperation(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户ID
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 404)
	}

	// 解析请求
	var request MainServer.EmailOpRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}

	// 根据操作类型处理
	switch request.Op {
	case MainServer.EmailOp_EMAIL_OP_READ:
		// 批量标记为已读
		for _, emailId := range request.EmailIds {
			if err := email.ReadEmail(ctx, logger, db, emailId, trainer.Id); err != nil {
				logger.Error("标记邮件已读失败: %v", err)
				// 继续处理其他邮件
				continue
			}
		}
	case MainServer.EmailOp_EMAIL_OP_DELETE:
		// 批量删除
		if err := email.DeleteEmails(ctx, logger, db, request.EmailIds, trainer.Id); err != nil {
			return "", err
		}
	case MainServer.EmailOp_EMAIL_OP_RECEIVE_ATTACHMENT:
		for _, emailId := range request.EmailIds {
			if err := ReceiveEmailAttachments(ctx, logger, db, emailId, trainer); err != nil {
				return "", err
			}
		}
	default:
		return "", runtime.NewError("不支持的操作类型", 400)
	}

	return "{}", nil
}

func ReceiveEmailAttachments(ctx context.Context, logger runtime.Logger, db *sql.DB, emailId int64, trainer *MainServer.Trainer) error {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()

	gemail, err := email.GetEmailById(ctx, logger, tx, emailId, trainer.Id)
	if err != nil {
		return err
	}
	if time.Now().UnixMilli()-gemail.SendTime > 1000*60*60*24*15 {
		return fmt.Errorf("邮件已过期")
	}
	if gemail.Status == MainServer.EmailStatus_EMAIL_STATUS_RECEIVED_ATTACHMENT {
		return fmt.Errorf("邮件已领取")
	}
	if gemail.Status == MainServer.EmailStatus_EMAIL_STATUS_DELETED {
		return fmt.Errorf("邮件已删除")
	}
	if gemail.Attachments == nil {
		return fmt.Errorf("邮件没有附件")
	}
	err = email.ReceiveEmailAttachmentsWithTx(ctx, logger, tx, emailId, trainer.Id)
	if err != nil {
		return err
	}

	for _, item := range gemail.Attachments.Items {
		if err := inventory.AddItem(ctx, tx, trainer.Id, item.ItemName, item.Count); err != nil {
			return err
		}
	}
	if len(gemail.Attachments.Pokemons) > 0 {
		err = SavePokesToNormalBox(ctx, logger, trainer, tx, gemail.Attachments.Pokemons)
		if err != nil {
			return err
		}
	}
	for _, cloth := range gemail.Attachments.Clothes {
		err = AddTrainerCloth(ctx, logger, tx, trainer.Id, cloth.Type, cloth.Name)
		if err != nil {
			return err
		}
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}
	return nil
}

func RpcAddTraienrTeam(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户ID
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 404)
	}
	if trainer.Team != MainServer.TrainerTeam_TRAINER_TEAM_NONE {
		return "", runtime.NewError("已经加入组织", 400)
	}
	// 解析请求
	var request MainServer.RpcAddTrainerTeamRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	checked := nconst.CheckAddTeam(request.Type)
	if !checked {
		return "", runtime.NewError("无法加入该组织", 400)
	}
	clothId := nconst.GetInitTeamClothIdBy(request.Type)
	// 添加校服服装
	err = AddTrainerCloth(ctx, logger, tx, trainer.Id, MainServer.TrainerClothType_CLOTH_TYPE_OLD_NIN, clothId)
	if err != nil {
		return "", err
	}
	titleType := nconst.GetInitTeamTitleBy(request.Type)
	// 添加称号
	err = AddTrainerTitle(ctx, logger, tx, trainer.Id, titleType, 0, -1)
	if err != nil {
		return "", err
	}
	trainer.Team = request.Type
	_, err = UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.Team = MainServer.TrainerTeam_TRAINER_TEAM_NONE
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		trainer.Team = MainServer.TrainerTeam_TRAINER_TEAM_NONE
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return "", nil
}

func RpcLevelUpLearnMove(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户ID
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 404)
	}
	// 解析请求
	var request MainServer.LevelUpLearnMoveRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	pokemon, err := levelUpLearnMove(ctx, logger, tx, trainer, &request)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(pokemon)
}
func RpcTryUpTeamLevel(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	err = tryUpTeamLevel(ctx, logger, tx, trainer)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(trainer)
}
func RpcDonateTeamByCoin(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	amount, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	err = tryDonateTeamByCoin(ctx, logger, tx, trainer, amount)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(trainer)
}
func RpcConsumeTeamContributionToExp(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	amount, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", err
	}
	// 获取当前训练师
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到有效的训练师", 404)
	}
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	err = tryConsumeTeamContributionToExp(ctx, logger, tx, trainer, amount)
	if err != nil {
		return "", err
	}
	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}
	return tool.ProtoToBase64(trainer)
}

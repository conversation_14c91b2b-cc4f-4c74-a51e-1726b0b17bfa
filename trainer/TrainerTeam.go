package trainer

import (
	"context"
	"database/sql"
	"go-nakama-poke/item"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// 升级团队的等级
func tryUpTeamLevel(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer) error {
	// trainer.TeamInfo.Contribution += amount
	// _, err := UpsertTrainer(ctx, logger, tx, trainer)
	// if err != nil {
	// 	trainer.TeamInfo.Contribution -= amount // 恢复原来的团队贡献值
	// 	logger.Error("addTeamContribution failed to upsert trainer: %v", err)
	// 	return err
	// }
	return nil
}

// 捐赠Coin到团队贡献值
func tryDonateTeamByCoin(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.Coin < amount {
		return runtime.NewError("金币不足", 400)
	}
	trainer.Coin -= amount
	trainer.TeamInfo.Contribution += amount //先1:1兑换
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution -= amount // 恢复原来的团队贡献值
		logger.Error("addTeamContribution failed to upsert trainer: %v", err)
		return err
	}
	return nil
}

// 尝试消耗team贡献值兑换经验
func tryConsumeTeamContributionToExp(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.TeamInfo.Contribution < amount {
		return runtime.NewError("贡献值不足", 400)
	}
	trainer.TeamInfo.Contribution -= amount
	trainer.TeamInfo.Exp += int32(amount)
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution += amount // 恢复原来的团队贡献值
		trainer.TeamInfo.Exp -= int32(amount)
		logger.Error("addTeamContribution failed to upsert trainer: %v", err)
		return err
	}
	return nil
}

func buyStoreItem(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, itemNameId string, inventoryType MainServer.InventoryType, count int64, buySiteType MainServer.BuySiteType) error {
	localItem, exists := item.GetItemByName(itemNameId)
	if !exists {
		return runtime.NewError("物品不存在", 400)
	}
	cost := int64(localItem.TeamCost) * count
	if trainer.TeamInfo.Contribution < cost {
		return runtime.NewError("贡献值不足", 400)
	}
	trainer.TeamInfo.Contribution -= cost
	err := addItemToInventory(ctx, logger, tx, trainer, itemNameId, inventoryType, 1)
	if err != nil {
		trainer.TeamInfo.Contribution += cost // 恢复原来的团队贡献值
		logger.Error("buyStoreItem failed to addItemToInventory: %v", err)
		return err
	}
	_, err = UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution += cost // 恢复原来的团队贡献值
		logger.Error("buyStoreItem failed to upsert trainer: %v", err)
		return err
	}
	return nil
}

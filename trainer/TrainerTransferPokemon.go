package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strconv"
	"strings"

	"github.com/heroiclabs/nakama-common/runtime"
)

// TransferPokemon 将单个宝可梦从一个训练师转移到另一个训练师
// 这个函数是 BatchTransferPokemon 的包装器，用于处理单个宝可梦的转移
// 注意：logger 参数可以为 nil，函数会正常工作
func TransferPokemon(ctx context.Context, logger runtime.Logger, tx *sql.Tx, pokeId int64, fromTid int64, toTid int64) error {
	return BatchTransferPokemon(ctx, logger, tx, []int64{pokeId}, fromTid, toTid)
}

// BatchTransferPokemon 批量将宝可梦从一个训练师转移到另一个训练师
// 这个函数会：
// 1. 批量查找宝可梦在源训练师盒子中的位置
// 2. 批量从源训练师的盒子中移除宝可梦
// 3. 批量更新宝可梦的所有者信息
// 4. 批量将宝可梦添加到目标训练师的盒子中
// 注意：logger 参数可以为 nil，函数会正常工作
func BatchTransferPokemon(ctx context.Context, logger runtime.Logger, tx *sql.Tx, pokeIds []int64, fromTid int64, toTid int64) error {
	if len(pokeIds) == 0 {
		return nil // 没有宝可梦需要转移
	}

	// 1. 批量验证宝可梦所有权
	pokemons, err := poke.BatchQueryPokesByIds(ctx, tx, fromTid, pokeIds)
	if err != nil {
		return fmt.Errorf("批量查询宝可梦失败: %w", err)
	}

	if len(pokemons) != len(pokeIds) {
		return runtime.NewError("部分宝可梦不存在或不属于源训练师", 404)
	}

	// 2. 批量查找宝可梦在源训练师盒子中的位置
	sourceBoxInfos, err := batchFindPokemonBoxLocation(ctx, tx, fromTid, pokeIds)
	if err != nil {
		return fmt.Errorf("批量查找宝可梦位置失败: %w", err)
	}

	// 3. 从源训练师的盒子中移除宝可梦
	var aroundPokeIds []string
	var normalBoxRemoves = make(map[MainServer.PokeBoxType]map[int32][]int64)

	for pokeId, boxInfo := range sourceBoxInfos {
		if boxInfo.boxType == MainServer.PokeBoxType_around {
			aroundPokeIds = append(aroundPokeIds, strconv.FormatInt(pokeId, 10))
		} else {
			if normalBoxRemoves[boxInfo.boxType] == nil {
				normalBoxRemoves[boxInfo.boxType] = make(map[int32][]int64)
			}
			normalBoxRemoves[boxInfo.boxType][boxInfo.boxIndex] = append(normalBoxRemoves[boxInfo.boxType][boxInfo.boxIndex], pokeId)
		}
	}

	fromTrainer := tool.GetActiveTrainerByTid(fromTid)
	if fromTrainer == nil {
		return fmt.Errorf("训练师 %d 不存在", fromTid)
	}
	newPokeIds := make([]string, 0, len(fromTrainer.PokeIds))
	changed := false
	// 处理随身盒子中的宝可梦
	if len(aroundPokeIds) > 0 {
		for _, id := range fromTrainer.PokeIds {
			shouldKeep := true
			for _, removeId := range aroundPokeIds {
				if id == removeId {
					shouldKeep = false
					break
				}
			}
			if shouldKeep {
				newPokeIds = append(newPokeIds, id)
			}
		}
		if len(newPokeIds) != len(fromTrainer.PokeIds) {
			changed = true
		}
	} else {
		newPokeIds = append(newPokeIds, fromTrainer.PokeIds...)
	}

	// 处理普通盒子中的宝可梦
	for boxType, boxIndices := range normalBoxRemoves {
		for boxIndex, ids := range boxIndices {
			for _, pokeId := range ids {
				_, _, err = RemovePokeFromBox(ctx, tx, fromTid, boxIndex, pokeId, boxType)
				if err != nil {
					return fmt.Errorf("从盒子中移除宝可梦 %d 失败: %w", pokeId, err)
				}
			}
		}
	}

	// 4. 批量更新宝可梦的所有者信息
	for _, pokemon := range pokemons {
		pokemon.Tid = toTid
		err = poke.UpdatePokeData(ctx, tx, pokemon)
		if err != nil {
			return fmt.Errorf("更新宝可梦 %d 所有者信息失败: %w", pokemon.Id, err)
		}
	}

	// 5. 批量将宝可梦添加到目标训练师的盒子中
	toTrainer := tool.GetActiveTrainerByTid(toTid)
	if toTrainer == nil {
		return fmt.Errorf("目标训练师 %d 不存在", toTid)
	}
	newToPokeIds := make([]string, len(toTrainer.PokeIds))
	copy(newToPokeIds, toTrainer.PokeIds)
	toChanged := false

	for _, pokemon := range pokemons {
		_, err = SavePokeToRecordArroundOrNormalBox(ctx, logger, toTrainer, &newToPokeIds, tx, pokemon)
		if err != nil {
			return fmt.Errorf("将宝可梦 %d 添加到目标训练师盒子失败: %w", pokemon.Id, err)
		}
		// SavePokeToNormalBox 可能会修改 toTrainer.PokeIds
		newToPokeIds = toTrainer.PokeIds
		toChanged = true
	}

	// 最后统一保存双方的poke列表
	if changed {
		if err := UpdateTrainerPokeIds(ctx, logger, tx, fromTrainer, newPokeIds); err != nil {
			return fmt.Errorf("failed to upsert trainer: %w", err)
		}
	}
	if toChanged {
		if err := UpdateTrainerPokeIds(ctx, logger, tx, toTrainer, newToPokeIds); err != nil {
			return fmt.Errorf("failed to upsert trainer: %w", err)
		}
	}

	return nil
}

// BatchTransferPokemonsBetweenTrainers 支持双方互换宝可梦，先移除双方宝可梦，再分别添加到对方
// fromTid 给 toTid fromPokeIds，toTid 给 fromTid toPokeIds
func BatchTransferPokemonsBetweenTrainers(ctx context.Context, logger runtime.Logger, tx *sql.Tx, fromTid int64, fromPokeIds []int64, toTid int64, toPokeIds []int64) error {
	// 1. 批量验证双方宝可梦所有权
	fromPokemons, err := poke.BatchQueryPokesByIds(ctx, tx, fromTid, fromPokeIds)
	if err != nil {
		return fmt.Errorf("批量查询fromTid宝可梦失败: %w", err)
	}
	if len(fromPokemons) != len(fromPokeIds) {
		return runtime.NewError("fromTid部分宝可梦不存在或不属于源训练师", 404)
	}
	toPokemons, err := poke.BatchQueryPokesByIds(ctx, tx, toTid, toPokeIds)
	if err != nil {
		return fmt.Errorf("批量查询toTid宝可梦失败: %w", err)
	}
	if len(toPokemons) != len(toPokeIds) {
		return runtime.NewError("toTid部分宝可梦不存在或不属于源训练师", 404)
	}

	// 2. 批量查找双方宝可梦在各自训练师盒子中的位置
	fromBoxInfos, err := batchFindPokemonBoxLocation(ctx, tx, fromTid, fromPokeIds)
	if err != nil {
		return fmt.Errorf("批量查找fromTid宝可梦位置失败: %w", err)
	}
	toBoxInfos, err := batchFindPokemonBoxLocation(ctx, tx, toTid, toPokeIds)
	if err != nil {
		return fmt.Errorf("批量查找toTid宝可梦位置失败: %w", err)
	}

	toTrainer := tool.GetActiveTrainerByTid(toTid)
	if toTrainer == nil {
		return fmt.Errorf("目标训练师 %d 不存在", toTid)
	}
	fromTrainer := tool.GetActiveTrainerByTid(fromTid)
	if fromTrainer == nil {
		return fmt.Errorf("目标训练师 %d 不存在", fromTid)
	}
	newToPokeIds := make([]string, len(toTrainer.PokeIds))
	copy(newToPokeIds, toTrainer.PokeIds)
	newFromPokeIds := make([]string, len(fromTrainer.PokeIds))
	copy(newFromPokeIds, fromTrainer.PokeIds)
	toAroundChanged := false
	fromAroundChanged := false

	removeFromTrainer := func(trainer *MainServer.Trainer, tid int64, boxInfos map[int64]*BoxLocationInfo, pokeIds []int64, newPokeIds *[]string, changed *bool) error {
		var aroundPokeIds []string
		var normalBoxRemoves = make(map[MainServer.PokeBoxType]map[int32][]int64)
		for _, pokeId := range pokeIds {
			boxInfo := boxInfos[pokeId]
			if boxInfo.boxType == MainServer.PokeBoxType_around {
				aroundPokeIds = append(aroundPokeIds, strconv.FormatInt(pokeId, 10))
			} else {
				if normalBoxRemoves[boxInfo.boxType] == nil {
					normalBoxRemoves[boxInfo.boxType] = make(map[int32][]int64)
				}
				normalBoxRemoves[boxInfo.boxType][boxInfo.boxIndex] = append(normalBoxRemoves[boxInfo.boxType][boxInfo.boxIndex], pokeId)
			}
		}
		if len(aroundPokeIds) > 0 {
			newList := make([]string, 0, len(trainer.PokeIds))
			for _, id := range trainer.PokeIds {
				shouldKeep := true
				for _, removeId := range aroundPokeIds {
					if id == removeId {
						shouldKeep = false
						break
					}
				}
				if shouldKeep {
					newList = append(newList, id)
				}
			}
			if len(newList) != len(trainer.PokeIds) {
				*changed = true
			}
			*newPokeIds = newList
		}
		for boxType, boxIndices := range normalBoxRemoves {
			for boxIndex, ids := range boxIndices {
				for _, pokeId := range ids {
					_, _, err := RemovePokeFromBox(ctx, tx, tid, boxIndex, pokeId, boxType)
					if err != nil {
						return fmt.Errorf("从盒子中移除宝可梦 %d 失败: %w", pokeId, err)
					}
				}
			}
		}
		return nil
	}
	if err := removeFromTrainer(fromTrainer, fromTid, fromBoxInfos, fromPokeIds, &newFromPokeIds, &fromAroundChanged); err != nil {
		return err
	}
	if err := removeFromTrainer(toTrainer, toTid, toBoxInfos, toPokeIds, &newToPokeIds, &toAroundChanged); err != nil {
		return err
	}

	// 4. 更新宝可梦所有者信息
	for _, pokemon := range fromPokemons {
		pokemon.Tid = toTid
		err = poke.UpdatePokeData(ctx, tx, pokemon)
		if err != nil {
			return fmt.Errorf("更新宝可梦 %d 所有者信息失败: %w", pokemon.Id, err)
		}
	}
	for _, pokemon := range toPokemons {
		pokemon.Tid = fromTid
		err = poke.UpdatePokeData(ctx, tx, pokemon)
		if err != nil {
			return fmt.Errorf("更新宝可梦 %d 所有者信息失败: %w", pokemon.Id, err)
		}
	}

	// 5. 添加宝可梦到目标训练师
	for _, pokemon := range fromPokemons {
		pokeInfo, err := SavePokeToRecordArroundOrNormalBox(ctx, logger, toTrainer, &newToPokeIds, tx, pokemon)
		if err != nil {
			return fmt.Errorf("将宝可梦 %d 添加到目标训练师盒子失败: %w", pokemon.Id, err)
		}
		if pokeInfo == nil {
			fromAroundChanged = true
		}
	}
	for _, pokemon := range toPokemons {
		pokeInfo, err := SavePokeToRecordArroundOrNormalBox(ctx, logger, fromTrainer, &newFromPokeIds, tx, pokemon)
		if err != nil {
			return fmt.Errorf("将宝可梦 %d 添加到目标训练师盒子失败: %w", pokemon.Id, err)
		}
		if pokeInfo == nil {
			toAroundChanged = true
		}
	}

	// 最后统一保存双方的poke列表
	if fromAroundChanged {
		if err := UpdateTrainerPokeIds(ctx, logger, tx, fromTrainer, newFromPokeIds); err != nil {
			return fmt.Errorf("failed to upsert trainer: %w", err)
		}
	}
	if toAroundChanged {
		if err := UpdateTrainerPokeIds(ctx, logger, tx, toTrainer, newToPokeIds); err != nil {
			return fmt.Errorf("failed to upsert trainer: %w", err)
		}
	}

	return nil
}

// BoxLocationInfo 存储宝可梦在盒子中的位置信息
type BoxLocationInfo struct {
	boxType  MainServer.PokeBoxType
	boxIndex int32
	location string
}

// findPokemonBoxLocation 查找单个宝可梦在训练师盒子中的位置
// 这个函数是 batchFindPokemonBoxLocation 的包装器，用于处理单个宝可梦
func findPokemonBoxLocation(ctx context.Context, tx *sql.Tx, tid int64, pokeId int64) (*BoxLocationInfo, error) {
	boxInfos, err := batchFindPokemonBoxLocation(ctx, tx, tid, []int64{pokeId})
	if err != nil {
		return nil, err
	}

	boxInfo, ok := boxInfos[pokeId]
	if !ok {
		return nil, fmt.Errorf("宝可梦 ID %d 不在训练师 %d 的任何盒子中", pokeId, tid)
	}

	return boxInfo, nil
}

// batchFindPokemonBoxLocation 批量查找宝可梦在训练师盒子中的位置
// 返回一个 map，key 是宝可梦 ID，value 是位置信息
func batchFindPokemonBoxLocation(ctx context.Context, tx *sql.Tx, tid int64, pokeIds []int64) (map[int64]*BoxLocationInfo, error) {
	if len(pokeIds) == 0 {
		return make(map[int64]*BoxLocationInfo), nil
	}

	result := make(map[int64]*BoxLocationInfo)

	// 1. 首先检查宝可梦是否在随身盒子中
	// 使用 tool.GetActiveTrainerByTid 获取训练师信息
	trainer := tool.GetActiveTrainerByTid(tid)
	if trainer == nil {
		return nil, fmt.Errorf("训练师 %d 不存在", tid)
	}

	// 创建一个 map 用于快速查找
	pokeIdMap := make(map[string]int64)
	for _, pokeId := range pokeIds {
		pokeIdMap[strconv.FormatInt(pokeId, 10)] = pokeId
	}

	// 检查随身盒子
	for _, id := range trainer.PokeIds {
		if pokeId, ok := pokeIdMap[id]; ok {
			result[pokeId] = &BoxLocationInfo{
				boxType:  MainServer.PokeBoxType_around,
				boxIndex: 0,
				location: id,
			}
			// 从 map 中删除已找到的宝可梦，减少后续查询的数量
			delete(pokeIdMap, id)
		}
	}

	// 如果所有宝可梦都在随身盒子中找到了，直接返回
	if len(pokeIdMap) == 0 {
		return result, nil
	}

	// 2. 使用高效的索引查询查找剩余宝可梦在哪个盒子中
	// 构建 IN 查询的参数
	remainingPokeIds := make([]int64, 0, len(pokeIdMap))
	for _, pokeId := range pokeIdMap {
		remainingPokeIds = append(remainingPokeIds, pokeId)
	}

	// 构建参数占位符
	placeholders := make([]string, len(remainingPokeIds))
	args := make([]interface{}, len(remainingPokeIds)+1)
	args[0] = tid

	for i, pokeId := range remainingPokeIds {
		placeholders[i] = fmt.Sprintf("$%d", i+2)
		args[i+1] = pokeId
	}

	// poke_boxes.pokes 的结构是 {"0": {"id": 9184}, "1": {"id": 9185}}
	// 其中 key 是位置（loc），value 是包含 id 的对象
	query := fmt.Sprintf(`
		SELECT pb.type, pb.index, pk.key, (pk.value->>'id')::bigint as poke_id
		FROM %s pb, jsonb_each(pb.pokes) pk
		WHERE pb.tid = $1 AND (pk.value->>'id')::bigint IN (%s)
	`, TablePokeBoxName, strings.Join(placeholders, ", "))

	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("批量查询宝可梦位置失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var boxType int32
		var boxIndex int32
		var location string
		var pokeId int64

		err := rows.Scan(&boxType, &boxIndex, &location, &pokeId)
		if err != nil {
			return nil, fmt.Errorf("扫描宝可梦位置数据失败: %w", err)
		}

		result[pokeId] = &BoxLocationInfo{
			boxType:  MainServer.PokeBoxType(boxType),
			boxIndex: boxIndex,
			location: location,
		}
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历宝可梦位置数据失败: %w", err)
	}

	// 检查是否所有宝可梦都找到了位置
	for _, pokeId := range pokeIds {
		if _, ok := result[pokeId]; !ok {
			return nil, fmt.Errorf("宝可梦 ID %d 不在训练师 %d 的任何盒子中", pokeId, tid)
		}
	}

	return result, nil
}

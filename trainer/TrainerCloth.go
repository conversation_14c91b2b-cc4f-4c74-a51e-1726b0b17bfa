package trainer

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableTrainerCloth = "trainer_cloth"

// InitTrainerCloth 初始化训练师服装模块
func InitTrainerCloth(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTrainerClothTableIfNotExists(ctx, logger, db)
}

// createTrainerClothTableIfNotExists 创建训练师服装表
func createTrainerClothTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTableSQL := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,                    -- 自增长 ID
            tid BIGINT NOT NULL,                         -- 训练师ID
            type INT NOT NULL,                           -- 服装类型 (MainServer.TrainerClothType)
            cloth JSONB NOT NULL,                        -- 服装数据 (MainServer.TrainerBoxCloth)
            create_ts BIGINT NOT NULL,                   -- 创建时间戳
            update_ts BIGINT NOT NULL,                   -- 更新时间戳
            UNIQUE (tid, type)                           -- 每个训练师的每个类型只能有一条记录
        );`, TableTrainerCloth)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("Failed to create table %s: %v", TableTrainerCloth, err)
	} else {
		logger.Info("Successfully created table %s", TableTrainerCloth)

		// 创建索引以优化查询性能
		createIndexSQL := fmt.Sprintf(`
            CREATE INDEX IF NOT EXISTS idx_%[1]s_tid ON %[1]s (tid);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_type ON %[1]s (type);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_tid_type ON %[1]s (tid, type);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_update_ts ON %[1]s (update_ts);
        `, TableTrainerCloth)

		_, err = db.ExecContext(ctx, createIndexSQL)
		if err != nil {
			logger.Error("Failed to create indexes on table %s: %v", TableTrainerCloth, err)
		} else {
			logger.Info("Successfully created indexes on table %s", TableTrainerCloth)
		}
	}
}

// AddTrainerCloth 添加训练师服装到指定品类
func AddTrainerCloth(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, clothType MainServer.TrainerClothType, clothId string) error {
	nowTs := time.Now().Unix() // 精确到秒
	cloth := &MainServer.TrainerCloth{
		Name:     clothId,
		Type:     clothType,
		CreateTs: nowTs,
		UpdateTs: nowTs,
	}

	// 先获取现有的TrainerBoxCloth
	existingBoxCloth, err := GetTrainerBoxCloth(ctx, logger, tx, tid, clothType)
	if err != nil && err.Error() != "Cloth box not found" {
		return err
	}

	// 如果不存在，创建新的TrainerBoxCloth
	if existingBoxCloth == nil {
		existingBoxCloth = &MainServer.TrainerBoxCloth{
			Cloths: make(map[string]*MainServer.TrainerCloth),
		}
	}

	// 添加新的cloth到集合中
	existingBoxCloth.Cloths[clothId] = cloth

	// 将TrainerBoxCloth转换为JSON
	boxClothJSON, err := tool.ProtoToJson(existingBoxCloth)
	if err != nil {
		logger.Error("Failed to convert box cloth to JSON: %v", err)
		return runtime.NewError("Failed to convert cloth data", 500)
	}

	// 使用UPSERT操作
	upsertSQL := fmt.Sprintf(`
		INSERT INTO %s (tid, type, cloth, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (tid, type)
		DO UPDATE SET cloth = $3, update_ts = $5
	`, TableTrainerCloth)

	_, err = tx.ExecContext(ctx, upsertSQL, tid, int(clothType), boxClothJSON, nowTs, nowTs)
	if err != nil {
		logger.Error("Failed to upsert trainer cloth: %v", err)
		return runtime.NewError("Failed to add trainer cloth", 500)
	}

	logger.Info("Successfully added trainer cloth for tid %d, type %d, clothId %s", tid, int(clothType), clothId)
	return nil
}

// GetTrainerBoxCloth 获取训练师指定类型的服装盒子
func GetTrainerBoxCloth(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, clothType MainServer.TrainerClothType) (*MainServer.TrainerBoxCloth, error) {
	query := fmt.Sprintf(`
		SELECT cloth
		FROM %s
		WHERE tid = $1 AND type = $2
	`, TableTrainerCloth)

	var clothJSON string
	err := tx.QueryRowContext(ctx, query, tid, int(clothType)).Scan(&clothJSON)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, runtime.NewError("Cloth box not found", 404)
		}
		logger.Error("Failed to query trainer cloth box: %v", err)
		return nil, runtime.NewError("Failed to get trainer cloth box", 500)
	}

	// 将JSON转换为proto
	boxCloth := &MainServer.TrainerBoxCloth{}
	err = json.Unmarshal([]byte(clothJSON), boxCloth)
	if err != nil {
		logger.Error("Failed to convert JSON to cloth box proto: %v", err)
		return nil, runtime.NewError("Failed to parse cloth box data", 500)
	}

	return boxCloth, nil
}

// RemoveTrainerCloth 删除训练师指定服装
func RemoveTrainerCloth(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, clothType MainServer.TrainerClothType, clothId string) error {
	// 先获取现有的TrainerBoxCloth
	existingBoxCloth, err := GetTrainerBoxCloth(ctx, logger, tx, tid, clothType)
	if err != nil {
		return err
	}

	// 检查clothId是否存在
	if _, exists := existingBoxCloth.Cloths[clothId]; !exists {
		return runtime.NewError("Cloth not found", 404)
	}

	// 从集合中删除指定的cloth
	delete(existingBoxCloth.Cloths, clothId)

	// 如果集合为空，删除整个记录
	if len(existingBoxCloth.Cloths) == 0 {
		deleteSQL := fmt.Sprintf(`
			DELETE FROM %s
			WHERE tid = $1 AND type = $2
		`, TableTrainerCloth)

		_, err = tx.ExecContext(ctx, deleteSQL, tid, int(clothType))
		if err != nil {
			logger.Error("Failed to delete trainer cloth box: %v", err)
			return runtime.NewError("Failed to remove trainer cloth", 500)
		}
	} else {
		// 更新剩余的cloths
		nowTs := time.Now().Unix()
		boxClothJSON, err := tool.ProtoToJson(existingBoxCloth)
		if err != nil {
			logger.Error("Failed to convert box cloth to JSON: %v", err)
			return runtime.NewError("Failed to convert cloth data", 500)
		}

		updateSQL := fmt.Sprintf(`
			UPDATE %s
			SET cloth = $1, update_ts = $2
			WHERE tid = $3 AND type = $4
		`, TableTrainerCloth)

		_, err = tx.ExecContext(ctx, updateSQL, boxClothJSON, nowTs, tid, int(clothType))
		if err != nil {
			logger.Error("Failed to update trainer cloth box: %v", err)
			return runtime.NewError("Failed to remove trainer cloth", 500)
		}
	}

	logger.Info("Successfully removed trainer cloth for tid %d, type %d, clothId %s", tid, int(clothType), clothId)
	return nil
}

// GetTrainerCloth 获取训练师指定类型的服装盒子 (重命名为GetTrainerClothBox)
// func GetTrainerCloth(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, clothType MainServer.TrainerClothType) (*MainServer.TrainerBoxCloth, error) {
// 	return GetTrainerBoxCloth(ctx, logger, tx, tid, clothType)
// }

// GetAllTrainerCloth 获取训练师所有服装
func GetAllTrainerCloth(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64) (map[int32]*MainServer.TrainerBoxCloth, error) {
	query := fmt.Sprintf(`
		SELECT type, cloth 
		FROM %s 
		WHERE tid = $1
		ORDER BY type
	`, TableTrainerCloth)

	rows, err := tx.QueryContext(ctx, query, tid)
	if err != nil {
		logger.Error("Failed to query all trainer cloth: %v", err)
		return nil, runtime.NewError("Failed to get trainer cloth", 500)
	}
	defer rows.Close()

	clothMap := make(map[int32]*MainServer.TrainerBoxCloth)
	for rows.Next() {
		var clothType int32
		var clothJSON string

		err := rows.Scan(&clothType, &clothJSON)
		if err != nil {
			logger.Error("Failed to scan cloth row: %v", err)
			return nil, runtime.NewError("Failed to parse cloth data", 500)
		}

		// 将JSON转换为proto
		cloth := &MainServer.TrainerBoxCloth{}
		err = json.Unmarshal([]byte(clothJSON), cloth)
		if err != nil {
			logger.Error("Failed to convert JSON to cloth proto: %v", err)
			continue // 跳过无效数据
		}

		clothMap[clothType] = cloth
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error during rows iteration: %v", err)
		return nil, runtime.NewError("Failed to process cloth data", 500)
	}

	logger.Info("Successfully retrieved %d cloth items for tid %d", len(clothMap), tid)
	return clothMap, nil
}

// UpdateTrainerCloth 更新训练师指定服装
func UpdateTrainerCloth(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, clothType MainServer.TrainerClothType, clothId string) error {
	// 先获取现有的TrainerBoxCloth
	existingBoxCloth, err := GetTrainerBoxCloth(ctx, logger, tx, trainer.Id, clothType)
	if err != nil {
		return err
	}
	cloth, exists := existingBoxCloth.Cloths[clothId]
	// 检查clothId是否存在
	if !exists {
		return runtime.NewError("Cloth not found", 404)
	}
	oldCloth := trainer.Cloth
	trainer.Cloth = cloth
	_, err = UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil { //更新失败还原
		trainer.Cloth = oldCloth
		return err
	}
	return nil
}

// RpcAddTrainerCloth RPC函数：添加训练师服装
func RpcAddTrainerCloth(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcAddTrainerClothRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 添加服装
	err = AddTrainerCloth(ctx, logger, tx, trainer.Id, request.Type, request.ClothId)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.RpcAddTrainerClothResponse{
		Success: true,
		Message: "服装添加成功",
	}

	return tool.ProtoToBase64(result)
}

// RpcRemoveTrainerCloth RPC函数：删除训练师服装
func RpcRemoveTrainerCloth(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcRemoveTrainerClothRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 删除服装
	err = RemoveTrainerCloth(ctx, logger, tx, trainer.Id, request.Type, request.ClothId)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	result := &MainServer.RpcRemoveTrainerClothResponse{
		Success: true,
		Message: "服装删除成功",
	}

	return tool.ProtoToBase64(result)
}

// RpcGetTrainerBoxCloth RPC函数：获取训练师指定类型服装
func RpcGetTrainerBoxCloth(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcGetTrainerClothRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取服装
	cloth, err := GetTrainerBoxCloth(ctx, logger, tx, trainer.Id, request.Type)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	return tool.ProtoToBase64(cloth)
}

// RpcGetAllTrainerCloth RPC函数：获取训练师所有服装
func RpcGetAllTrainerCloth(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 获取所有服装
	clothMap, err := GetAllTrainerCloth(ctx, logger, tx, trainer.Id)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果
	result := &MainServer.RpcGetAllTrainerClothResponse{
		ClothBoxes: clothMap,
	}

	return tool.ProtoToBase64(result)
}

// RpcUpdateTrainerCloth RPC函数：更新训练师服装
func RpcUpdateTrainerCloth(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcUpdateTrainerClothRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 更新服装
	err = UpdateTrainerCloth(ctx, logger, tx, trainer, request.Type, request.ClothId)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回成功结果
	// result := &MainServer.RpcUpdateTrainerClothResponse{
	// 	Success: true,
	// 	Message: "服装更新成功",
	// }

	return tool.ProtoToBase64(trainer)
}

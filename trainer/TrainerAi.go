package trainer

import (
	"context"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

type AiTrainerId int

const (
	EmptyTrainerId AiTrainerId = -100 //2vs1的时候的空训练家
	WildTrainerId  AiTrainerId = -101
	NPCTrainerId   AiTrainerId = -110
)

func InitTrainerAiData() {

}
func GetAiTrainer(ctx context.Context, logger runtime.Logger, aitype AiTrainerId, id int64, name string) *MainServer.Trainer {
	trainer := &MainServer.Trainer{
		Id:     id,
		Uid:    "wild",
		Name:   "wild",
		Gender: MainServer.Gender_M,
		ActionInfo: &MainServer.TrainerActionInfo{
			Action: MainServer.TrainerActionType_battle,
		},
		PokeIds:    []string{},
		Badges:     &MainServer.TrainerBadges{},
		Team:       0,
		GroupId:    "group_101",
		StrictInfo: &MainServer.TrainerStrictInfo{},
		Cloth: &MainServer.TrainerCloth{
			Name: "02",
		},
	}
	if aitype == EmptyTrainerId {
		trainer = &MainServer.Trainer{
			Id:      id,
			Uid:     "empty",
			Name:    "empty",
			PokeIds: []string{},
			ActionInfo: &MainServer.TrainerActionInfo{
				Action: MainServer.TrainerActionType_idle,
			},
			StrictInfo: &MainServer.TrainerStrictInfo{},
		}
	} else if aitype == NPCTrainerId {

	}
	return trainer
}


//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TbConfigQuestUnlockInfo struct {
    Id int32
    QuestUnlockId int32
    QuestUnlockName string
    QuestUnlockType string
    QuestConditionNameId string
    QuestConditionCount int32
    JsonValue string
    Description string
}

const TypeId_TbConfigQuestUnlockInfo = -887110154

func (*TbConfigQuestUnlockInfo) GetTypeId() int32 {
    return -887110154
}

func NewTbConfigQuestUnlockInfo(_buf *luban.ByteBuf) (_v *TbConfigQuestUnlockInfo, err error) {
    _v = &TbConfigQuestUnlockInfo{}
    { if _v.Id, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestUnlockId, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestUnlockName, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestUnlockType, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestConditionNameId, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestConditionCount, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.JsonValue, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.Description, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    return
}


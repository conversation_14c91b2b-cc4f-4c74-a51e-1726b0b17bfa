
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TbConfigTrainerTitleConfig struct {
    Id int32
    Name string
    Type int32
    Price float32
    ExpireDuration int32
    SpecialPrice float32
    EncounterEnemy float32
    EncounterShine float32
    BreedGoodPokebaby float32
    BreedDifficultBaby float32
    BorrowGoodPoke float32
    FishingGoodItem float32
    FishingGoodPoke float32
    EncounterGoodEnemy float32
    GetBattlePoke float32
    GetBattleItem float32
    TreeFruitGrowup float32
    TreeFruitWithered float32
    HatchEggTime float32
    FishingBattleItem float32
    GetBattleExp float32
    GetBattleMoney float32
    GetBattleEvs float32
    BreedShine float32
}

const TypeId_TbConfigTrainerTitleConfig = -424556765

func (*TbConfigTrainerTitleConfig) GetTypeId() int32 {
    return -424556765
}

func NewTbConfigTrainerTitleConfig(_buf *luban.ByteBuf) (_v *TbConfigTrainerTitleConfig, err error) {
    _v = &TbConfigTrainerTitleConfig{}
    { if _v.Id, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.Name, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.Type, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.Price, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.ExpireDuration, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.SpecialPrice, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.EncounterEnemy, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.EncounterShine, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.BreedGoodPokebaby, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.BreedDifficultBaby, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.BorrowGoodPoke, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.FishingGoodItem, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.FishingGoodPoke, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.EncounterGoodEnemy, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.GetBattlePoke, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.GetBattleItem, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.TreeFruitGrowup, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.TreeFruitWithered, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.HatchEggTime, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.FishingBattleItem, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.GetBattleExp, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.GetBattleMoney, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.GetBattleEvs, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.BreedShine, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    return
}


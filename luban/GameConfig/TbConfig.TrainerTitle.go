
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import "go-nakama-poke/luban"

type TbConfigTrainerTitle struct {
    _dataMap map[int32]*TbConfigTrainerTitleConfig
    _dataList []*TbConfigTrainerTitleConfig
}

func NewTbConfigTrainerTitle(_buf *luban.ByteBuf) (*TbConfigTrainerTitle, error) {
	if size, err := _buf.ReadSize() ; err != nil {
		return nil, err
	} else {
		_dataList := make([]*TbConfigTrainerTitleConfig, 0, size)
		dataMap := make(map[int32]*TbConfigTrainerTitleConfig)

		for i := 0 ; i < size ; i++ {
			if _v, err2 := NewTbConfigTrainerTitleConfig(_buf); err2 != nil {
				return nil, err2
			} else {
				_dataList = append(_dataList, _v)
				dataMap[_v.Id] = _v
			}
		}
		return &TbConfigTrainerTitle{_dataList:_dataList, _dataMap:dataMap}, nil
	}
}

func (table *TbConfigTrainerTitle) GetDataMap() map[int32]*TbConfigTrainerTitleConfig {
    return table._dataMap
}

func (table *TbConfigTrainerTitle) GetDataList() []*TbConfigTrainerTitleConfig {
    return table._dataList
}

func (table *TbConfigTrainerTitle) Get(key int32) *TbConfigTrainerTitleConfig {
    return table._dataMap[key]
}



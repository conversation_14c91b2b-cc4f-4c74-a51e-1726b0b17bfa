
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type ItemItem struct {
    Id int32
    Name string
    Price int32
    UpgradeToItemId int32
    ExpireTime *int64
    Quality int32
    ExchangeList []*ItemItemExchange
    ExchangeColumn *ItemItemExchange
}

const TypeId_ItemItem = 2107285806

func (*ItemItem) GetTypeId() int32 {
    return 2107285806
}

func NewItemItem(_buf *luban.ByteBuf) (_v *ItemItem, err error) {
    _v = &ItemItem{}
    { if _v.Id, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.Name, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.Price, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.UpgradeToItemId, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { var __exists__ bool; if __exists__, err = _buf.ReadBool(); err != nil { return }; if __exists__ { var __x__ int64;  { if __x__, err = _buf.ReadLong(); err != nil { err = errors.New("error"); return } }; _v.ExpireTime = &__x__ }}
    { if _v.Quality, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    {_v.ExchangeList = make([]*ItemItemExchange, 0); var _n0_ int; if _n0_, err = _buf.ReadSize(); err != nil { err = errors.New("error"); return}; for i0 := 0 ; i0 < _n0_ ; i0++ { var _e0_ *ItemItemExchange; { if _e0_, err = NewItemItemExchange(_buf); err != nil { err = errors.New("error"); return } }; _v.ExchangeList = append(_v.ExchangeList, _e0_) } }
    { if _v.ExchangeColumn, err = NewItemItemExchange(_buf); err != nil { err = errors.New("error"); return } }
    return
}


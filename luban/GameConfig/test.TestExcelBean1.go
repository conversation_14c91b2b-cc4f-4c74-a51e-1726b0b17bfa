
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TestTestExcelBean1 struct {
    X1 int32
    X2 string
    X3 int32
    X4 float32
}

const TypeId_TestTestExcelBean1 = -1738345160

func (*TestTestExcelBean1) GetTypeId() int32 {
    return -1738345160
}

func NewTestTestExcelBean1(_buf *luban.ByteBuf) (_v *TestTestExcelBean1, err error) {
    _v = &TestTestExcelBean1{}
    { if _v.X1, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.X2, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.X3, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.X4, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    return
}


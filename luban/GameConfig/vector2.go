
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type vector2 struct {
    X float32
    Y float32
}

const TypeId_vector2 = 337790799

func (*vector2) GetTypeId() int32 {
    return 337790799
}

func Newvector2(_buf *luban.ByteBuf) (_v *vector2, err error) {
    _v = &vector2{}
    { if _v.X, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.Y, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    return
}


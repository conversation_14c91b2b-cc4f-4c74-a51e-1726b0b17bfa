
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TestShape struct {
}

const TypeId_TestShape = 637688613

func (*TestShape) GetTypeId() int32 {
    return 637688613
}

func NewTestShape(_buf *luban.ByteBuf) (interface{}, error) {
    var id int32
    var err error
    if id, err = _buf.ReadInt() ; err != nil {
        return nil, err
    }
    switch id {
        case 2131829196: _v, err := NewTestCircle(_buf); if err != nil { return nil, errors.New("test.circle") } else { return _v, nil }
        case -31893773: _v, err := NewTestRectangle(_buf); if err != nil { return nil, errors.New("test.rectangle") } else { return _v, nil }
        default: return nil, errors.New("unknown type id")
    }
}



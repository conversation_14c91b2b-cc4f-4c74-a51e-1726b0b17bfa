
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type vector3 struct {
    X float32
    Y float32
    Z float32
}

const TypeId_vector3 = 337790800

func (*vector3) GetTypeId() int32 {
    return 337790800
}

func Newvector3(_buf *luban.ByteBuf) (_v *vector3, err error) {
    _v = &vector3{}
    { if _v.X, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.Y, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.Z, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    return
}


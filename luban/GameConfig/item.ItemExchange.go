
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type ItemItemExchange struct {
    Id int32
    Num int32
}

const TypeId_ItemItemExchange = 1814660465

func (*ItemItemExchange) GetTypeId() int32 {
    return 1814660465
}

func NewItemItemExchange(_buf *luban.ByteBuf) (_v *ItemItemExchange, err error) {
    _v = &ItemItemExchange{}
    { if _v.Id, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.Num, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    return
}


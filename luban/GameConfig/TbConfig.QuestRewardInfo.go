
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TbConfigQuestRewardInfo struct {
    Id int32
    QuestRewardId int32
    QuestRewardName string
    QuestRewardType string
    QuestRewardRate int32
    QuestRewardCount int32
    QuestRewardCountRate int32
    DayWholeNetlocked int32
}

const TypeId_TbConfigQuestRewardInfo = 1663518817

func (*TbConfigQuestRewardInfo) GetTypeId() int32 {
    return 1663518817
}

func NewTbConfigQuestRewardInfo(_buf *luban.ByteBuf) (_v *TbConfigQuestRewardInfo, err error) {
    _v = &TbConfigQuestRewardInfo{}
    { if _v.Id, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRewardId, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRewardName, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRewardType, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRewardRate, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRewardCount, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRewardCountRate, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.DayWholeNetlocked, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    return
}


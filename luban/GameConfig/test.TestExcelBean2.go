
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TestTestExcelBean2 struct {
    Y1 int32
    Y2 string
    Y3 float32
}

const TypeId_TestTestExcelBean2 = -1738345159

func (*TestTestExcelBean2) GetTypeId() int32 {
    return -1738345159
}

func NewTestTestExcelBean2(_buf *luban.ByteBuf) (_v *TestTestExcelBean2, err error) {
    _v = &TestTestExcelBean2{}
    { if _v.Y1, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.Y2, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.Y3, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    return
}



//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TbConfigQuestInfo struct {
    Id int32
    QuestId int32
    QuestName string
    QuestType string
    QuestLevel int32
    QuestStatus string
    QuestUnlockId float32
    QuestCompleteId int32
    LinearQuestIds string
    CurrentQuestIds string
    SingleQuest bool
    QuestRewardId int32
    QuestStartTime int64
    QuestEndTime int64
    QuestRepeatLimit int32
    QuestRepeatInterval int32
    QuestBroadcast int64
    QuestRepeatLimitTime int32
}

const TypeId_TbConfigQuestInfo = 2012376978

func (*TbConfigQuestInfo) GetTypeId() int32 {
    return 2012376978
}

func NewTbConfigQuestInfo(_buf *luban.ByteBuf) (_v *TbConfigQuestInfo, err error) {
    _v = &TbConfigQuestInfo{}
    { if _v.Id, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestId, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestName, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestType, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestLevel, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestStatus, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestUnlockId, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestCompleteId, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.LinearQuestIds, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.CurrentQuestIds, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.SingleQuest, err = _buf.ReadBool(); err != nil { err = errors.New("error"); err = errors.New("error"); return } }
    { if _v.QuestRewardId, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestStartTime, err = _buf.ReadLong(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestEndTime, err = _buf.ReadLong(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRepeatLimit, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRepeatInterval, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestBroadcast, err = _buf.ReadLong(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestRepeatLimitTime, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    return
}


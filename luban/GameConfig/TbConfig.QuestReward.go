
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import "go-nakama-poke/luban"

type TbConfigQuestReward struct {
    _dataMap map[int32]*TbConfigQuestRewardInfo
    _dataList []*TbConfigQuestRewardInfo
}

func NewTbConfigQuestReward(_buf *luban.ByteBuf) (*TbConfigQuestReward, error) {
	if size, err := _buf.ReadSize() ; err != nil {
		return nil, err
	} else {
		_dataList := make([]*TbConfigQuestRewardInfo, 0, size)
		dataMap := make(map[int32]*TbConfigQuestRewardInfo)

		for i := 0 ; i < size ; i++ {
			if _v, err2 := NewTbConfigQuestRewardInfo(_buf); err2 != nil {
				return nil, err2
			} else {
				_dataList = append(_dataList, _v)
				dataMap[_v.Id] = _v
			}
		}
		return &TbConfigQuestReward{_dataList:_dataList, _dataMap:dataMap}, nil
	}
}

func (table *TbConfigQuestReward) GetDataMap() map[int32]*TbConfigQuestRewardInfo {
    return table._dataMap
}

func (table *TbConfigQuestReward) GetDataList() []*TbConfigQuestRewardInfo {
    return table._dataList
}

func (table *TbConfigQuestReward) Get(key int32) *TbConfigQuestRewardInfo {
    return table._dataMap[key]
}



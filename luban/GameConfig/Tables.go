
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;

import (
    "go-nakama-poke/luban"
    "errors"
)

type ByteBufLoader func(string) (*luban.ByteBuf, error)

type Tables struct {
    TbItem *ItemTbItem
    TrainerTitle *TbConfigTrainerTitle
    QuestComplete *TbConfigQuestComplete
    Quest *TbConfigQuest
    QuestUnlock *TbConfigQuestUnlock
    QuestReward *TbConfigQuestReward
}

func NewTables(loader ByteBufLoader) (*Tables, error) {
    var err error
    var buf *luban.ByteBuf

    tables := &Tables{}
    if buf, err = loader("item_tbitem") ; err != nil {
        return nil, err
    }
    if tables.TbItem, err = NewItemTbItem(buf) ; err != nil {
        return nil, errors.Join(errors.New("failed to load TbItem"), err)
    }
    if buf, err = loader("tbconfig_trainertitle") ; err != nil {
        return nil, err
    }
    if tables.TrainerTitle, err = NewTbConfigTrainerTitle(buf) ; err != nil {
        return nil, errors.Join(errors.New("failed to load TrainerTitle"), err)
    }
    if buf, err = loader("tbconfig_questcomplete") ; err != nil {
        return nil, err
    }
    if tables.QuestComplete, err = NewTbConfigQuestComplete(buf) ; err != nil {
        return nil, errors.Join(errors.New("failed to load QuestComplete"), err)
    }
    if buf, err = loader("tbconfig_quest") ; err != nil {
        return nil, err
    }
    if tables.Quest, err = NewTbConfigQuest(buf) ; err != nil {
        return nil, errors.Join(errors.New("failed to load Quest"), err)
    }
    if buf, err = loader("tbconfig_questunlock") ; err != nil {
        return nil, err
    }
    if tables.QuestUnlock, err = NewTbConfigQuestUnlock(buf) ; err != nil {
        return nil, errors.Join(errors.New("failed to load QuestUnlock"), err)
    }
    if buf, err = loader("tbconfig_questreward") ; err != nil {
        return nil, err
    }
    if tables.QuestReward, err = NewTbConfigQuestReward(buf) ; err != nil {
        return nil, errors.Join(errors.New("failed to load QuestReward"), err)
    }
    return tables, nil
}



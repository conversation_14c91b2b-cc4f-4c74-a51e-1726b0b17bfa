
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type vector2int struct {
    X int32
    Y int32
}

const TypeId_vector2int = 17422912

func (*vector2int) GetTypeId() int32 {
    return 17422912
}

func Newvector2int(_buf *luban.ByteBuf) (_v *vector2int, err error) {
    _v = &vector2int{}
    { if _v.X, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.Y, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    return
}


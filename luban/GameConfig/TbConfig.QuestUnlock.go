
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import "go-nakama-poke/luban"

type TbConfigQuestUnlock struct {
    _dataMap map[int32]*TbConfigQuestUnlockInfo
    _dataList []*TbConfigQuestUnlockInfo
}

func NewTbConfigQuestUnlock(_buf *luban.ByteBuf) (*TbConfigQuestUnlock, error) {
	if size, err := _buf.ReadSize() ; err != nil {
		return nil, err
	} else {
		_dataList := make([]*TbConfigQuestUnlockInfo, 0, size)
		dataMap := make(map[int32]*TbConfigQuestUnlockInfo)

		for i := 0 ; i < size ; i++ {
			if _v, err2 := NewTbConfigQuestUnlockInfo(_buf); err2 != nil {
				return nil, err2
			} else {
				_dataList = append(_dataList, _v)
				dataMap[_v.Id] = _v
			}
		}
		return &TbConfigQuestUnlock{_dataList:_dataList, _dataMap:dataMap}, nil
	}
}

func (table *TbConfigQuestUnlock) GetDataMap() map[int32]*TbConfigQuestUnlockInfo {
    return table._dataMap
}

func (table *TbConfigQuestUnlock) GetDataList() []*TbConfigQuestUnlockInfo {
    return table._dataList
}

func (table *TbConfigQuestUnlock) Get(key int32) *TbConfigQuestUnlockInfo {
    return table._dataMap[key]
}



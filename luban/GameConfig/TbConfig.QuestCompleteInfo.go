
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TbConfigQuestCompleteInfo struct {
    Id int32
    QuestCompleteId int32
    QuestCompleteName string
    QuestCompleteType string
    QuestConditionNameId string
    QuestConditionCount int32
    JsonValue string
    Description string
}

const TypeId_TbConfigQuestCompleteInfo = -946557845

func (*TbConfigQuestCompleteInfo) GetTypeId() int32 {
    return -946557845
}

func NewTbConfigQuestCompleteInfo(_buf *luban.ByteBuf) (_v *TbConfigQuestCompleteInfo, err error) {
    _v = &TbConfigQuestCompleteInfo{}
    { if _v.Id, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestCompleteId, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestCompleteName, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestCompleteType, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestConditionNameId, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.QuestConditionCount, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.JsonValue, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    { if _v.Description, err = _buf.ReadString(); err != nil { err = errors.New("error"); return } }
    return
}


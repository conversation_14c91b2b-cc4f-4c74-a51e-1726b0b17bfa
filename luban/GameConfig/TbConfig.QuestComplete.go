
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import "go-nakama-poke/luban"

type TbConfigQuestComplete struct {
    _dataMap map[int32]*TbConfigQuestCompleteInfo
    _dataList []*TbConfigQuestCompleteInfo
}

func NewTbConfigQuestComplete(_buf *luban.ByteBuf) (*TbConfigQuestComplete, error) {
	if size, err := _buf.ReadSize() ; err != nil {
		return nil, err
	} else {
		_dataList := make([]*TbConfigQuestCompleteInfo, 0, size)
		dataMap := make(map[int32]*TbConfigQuestCompleteInfo)

		for i := 0 ; i < size ; i++ {
			if _v, err2 := NewTbConfigQuestCompleteInfo(_buf); err2 != nil {
				return nil, err2
			} else {
				_dataList = append(_dataList, _v)
				dataMap[_v.Id] = _v
			}
		}
		return &TbConfigQuestComplete{_dataList:_dataList, _dataMap:dataMap}, nil
	}
}

func (table *TbConfigQuestComplete) GetDataMap() map[int32]*TbConfigQuestCompleteInfo {
    return table._dataMap
}

func (table *TbConfigQuestComplete) GetDataList() []*TbConfigQuestCompleteInfo {
    return table._dataList
}

func (table *TbConfigQuestComplete) Get(key int32) *TbConfigQuestCompleteInfo {
    return table._dataMap[key]
}



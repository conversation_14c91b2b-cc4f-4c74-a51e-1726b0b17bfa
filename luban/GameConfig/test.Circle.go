
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TestCircle struct {
    Radius float32
}

const TypeId_TestCircle = 2131829196

func (*TestCircle) GetTypeId() int32 {
    return 2131829196
}

func NewTestCircle(_buf *luban.ByteBuf) (_v *TestCircle, err error) {
    _v = &TestCircle{}
    { if _v.Radius, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    return
}


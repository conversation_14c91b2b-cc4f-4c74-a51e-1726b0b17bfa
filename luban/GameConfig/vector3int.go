
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type vector3int struct {
    X int32
    Y int32
    Z int32
}

const TypeId_vector3int = 17452703

func (*vector3int) GetTypeId() int32 {
    return 17452703
}

func Newvector3int(_buf *luban.ByteBuf) (_v *vector3int, err error) {
    _v = &vector3int{}
    { if _v.X, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.Y, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    { if _v.Z, err = _buf.ReadInt(); err != nil { err = errors.New("error"); return } }
    return
}


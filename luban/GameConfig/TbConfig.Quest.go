
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import "go-nakama-poke/luban"

type TbConfigQuest struct {
    _dataMap map[int32]*TbConfigQuestInfo
    _dataList []*TbConfigQuestInfo
}

func NewTbConfigQuest(_buf *luban.ByteBuf) (*TbConfigQuest, error) {
	if size, err := _buf.ReadSize() ; err != nil {
		return nil, err
	} else {
		_dataList := make([]*TbConfigQuestInfo, 0, size)
		dataMap := make(map[int32]*TbConfigQuestInfo)

		for i := 0 ; i < size ; i++ {
			if _v, err2 := NewTbConfigQuestInfo(_buf); err2 != nil {
				return nil, err2
			} else {
				_dataList = append(_dataList, _v)
				dataMap[_v.Id] = _v
			}
		}
		return &TbConfigQuest{_dataList:_dataList, _dataMap:dataMap}, nil
	}
}

func (table *TbConfigQuest) GetDataMap() map[int32]*TbConfigQuestInfo {
    return table._dataMap
}

func (table *TbConfigQuest) GetDataList() []*TbConfigQuestInfo {
    return table._dataList
}

func (table *TbConfigQuest) Get(key int32) *TbConfigQuestInfo {
    return table._dataMap[key]
}




//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package GameConfig;


import (
    "go-nakama-poke/luban"
)

import "errors"

type TestRectangle struct {
    Width float32
    Height float32
}

const TypeId_TestRectangle = -31893773

func (*TestRectangle) GetTypeId() int32 {
    return -31893773
}

func NewTestRectangle(_buf *luban.ByteBuf) (_v *TestRectangle, err error) {
    _v = &TestRectangle{}
    { if _v.Width, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    { if _v.Height, err = _buf.ReadFloat(); err != nil { err = errors.New("error"); return } }
    return
}


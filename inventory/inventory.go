package inventory

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	TableInventoryName = "inventory"
)

// 初始化库存表
func InitInventory(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createInventoryTableIfNotExists(ctx, logger, db)
}

// 创建库存表（如果不存在）
func createInventoryTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	query := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,
            tid BIGINT NOT NULL,
            item_name VARCHAR(30) NOT NULL,
            quantity INT NOT NULL DEFAULT 0,
            price INT NOT NULL DEFAULT 0,
            special_coin INT NOT NULL DEFAULT 0,
            status INT NOT NULL DEFAULT 0,
            create_ts BIGINT NOT NULL,
            update_ts BIGINT NOT NULL,
            UNIQUE(tid, item_name, status)
        );
        CREATE INDEX IF NOT EXISTS idx_id ON %s (id);
        CREATE INDEX IF NOT EXISTS idx_tid ON %s (tid);
        CREATE INDEX IF NOT EXISTS idx_item_name ON %s (item_name);
        CREATE INDEX IF NOT EXISTS idx_status ON %s (status);
        CREATE INDEX IF NOT EXISTS idx_price ON %s (price);
        CREATE INDEX IF NOT EXISTS idx_special_coin ON %s (special_coin);
    `, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("创建库存表失败: %v", err)
		return
	}

	logger.Info("库存表初始化完成")
}

// 使用道具
// 通过ItemName、tid和count进行使用，注意数量判断
// func UseItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int) error {
// 	// 检查道具数量是否足够
// 	hasEnough, err := CheckItemQuantity(ctx, tx, tid, itemName, count)
// 	if err != nil {
// 		return err
// 	}
// 	if !hasEnough {
// 		return fmt.Errorf("道具数量不足")
// 	}
// 	// 更新道具数量
// 	err = updateItemQuantity(ctx, tx, tid, itemName, -count, MainServer.InventoryStatus_InventoryStatus_Normal)
// 	if err != nil {
// 		return err
// 	}

// 	return nil
// }

// 移除道具
// 通过ItemName、tid和count进行移除，注意数量判断
func RemoveItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int) error {
	// 检查道具数量是否足够
	hasEnough, err := CheckItemQuantity(ctx, tx, tid, itemName, count)
	if err != nil {
		return err
	}
	if !hasEnough {
		return fmt.Errorf("道具数量不足")
	}

	// 更新道具数量
	err = updateItemQuantity(ctx, tx, tid, itemName, -count, MainServer.InventoryStatus_InventoryStatus_Normal)
	if err != nil {
		return err
	}

	return nil
}

// 上架道具
// 通过ItemName、tid、count和价格进行上架
func SaleItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int, price int, specialCoin int) error {
	// 检查道具数量是否足够
	hasEnough, err := CheckItemQuantity(ctx, tx, tid, itemName, count)
	if err != nil {
		return err
	}
	if !hasEnough {
		return fmt.Errorf("道具数量不足")
	}

	// 从普通状态移除道具
	err = updateItemQuantity(ctx, tx, tid, itemName, -count, MainServer.InventoryStatus_InventoryStatus_Normal)
	if err != nil {
		return err
	}

	// 添加到上架状态
	now := time.Now().Unix()
	query := fmt.Sprintf(`
		INSERT INTO %s (tid, item_name, quantity, price, special_coin, status, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $7)
		ON CONFLICT (tid, item_name, status)
		DO UPDATE SET quantity = %s.quantity + $3, price = $4, special_coin = $5, update_ts = $7
	`, TableInventoryName, TableInventoryName)

	_, err = tx.ExecContext(ctx, query, tid, itemName, count, price, specialCoin, int(MainServer.InventoryStatus_InventoryStatus_Sale), now)
	if err != nil {
		return fmt.Errorf("上架道具失败: %w", err)
	}

	return nil
}

// 下架道具
// 通过ItemName、tid和count进行下架
func UnsaleItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int) error {
	// 检查上架道具数量是否足够
	hasEnough, err := checkItemQuantityWithStatus(ctx, tx, tid, itemName, count, MainServer.InventoryStatus_InventoryStatus_Sale)
	if err != nil {
		return err
	}
	if !hasEnough {
		return fmt.Errorf("上架道具数量不足")
	}

	// 从上架状态移除道具
	err = updateItemQuantity(ctx, tx, tid, itemName, -count, MainServer.InventoryStatus_InventoryStatus_Sale)
	if err != nil {
		return err
	}

	// 添加到普通状态
	now := time.Now().Unix()
	query := fmt.Sprintf(`
		INSERT INTO %s (tid, item_name, quantity, price, special_coin, status, create_ts, update_ts)
		VALUES ($1, $2, $3, 0, 0, $4, $5, $5)
		ON CONFLICT (tid, item_name, status)
		DO UPDATE SET quantity = %s.quantity + $3, update_ts = $5
	`, TableInventoryName, TableInventoryName)

	_, err = tx.ExecContext(ctx, query, tid, itemName, count, int(MainServer.InventoryStatus_InventoryStatus_Normal), now)
	if err != nil {
		return fmt.Errorf("下架道具失败: %w", err)
	}

	return nil
}

// 检查道具数量
// 通过ItemName、tid和count进行判断，看该tid是否有相应数量的Item
func CheckItemQuantity(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int) (bool, error) {
	return checkItemQuantityWithStatus(ctx, tx, tid, itemName, count, MainServer.InventoryStatus_InventoryStatus_Normal)
}

// 检查指定状态的道具数量
func checkItemQuantityWithStatus(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int, status MainServer.InventoryStatus) (bool, error) {
	var quantity int
	query := fmt.Sprintf(`
		SELECT quantity FROM %s
		WHERE tid = $1 AND item_name = $2 AND status = $3
	`, TableInventoryName)

	err := tx.QueryRowContext(ctx, query, tid, itemName, int(status)).Scan(&quantity)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil // 道具不存在
		}
		return false, fmt.Errorf("查询道具数量失败: %w", err)
	}

	return quantity >= count, nil
}

// 添加道具
// 通过ItemName、tid和count进行添加
func AddItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32) error {
	// 添加道具
	now := time.Now().Unix()
	query := fmt.Sprintf(`
		INSERT INTO %s (tid, item_name, quantity, status, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $5)
		ON CONFLICT (tid, item_name, status)
		DO UPDATE SET quantity = %s.quantity + $3, update_ts = $5
	`, TableInventoryName, TableInventoryName)

	_, err := tx.ExecContext(ctx, query, tid, itemName, count, int(MainServer.InventoryStatus_InventoryStatus_Normal), now)
	if err != nil {
		return fmt.Errorf("添加道具失败: %w", err)
	}

	return nil
}

// 更新道具数量
// 内部函数，用于更新道具数量
func updateItemQuantity(ctx context.Context, tx *sql.Tx, tid int64, itemName string, countDelta int, status MainServer.InventoryStatus) error {
	now := time.Now().Unix()
	query := fmt.Sprintf(`
		UPDATE %s
		SET quantity = quantity + $1, update_ts = $2
		WHERE tid = $3 AND item_name = $4 AND status = $5
	`, TableInventoryName)

	result, err := tx.ExecContext(ctx, query, countDelta, now, tid, itemName, int(status))
	if err != nil {
		return fmt.Errorf("更新道具数量失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("道具不存在")
	}

	return nil
}

// 获取玩家的所有道具
// 如果updateTs大于0，则只查询更新时间大于updateTs的记录
func GetAllItems(ctx context.Context, tx *sql.Tx, tid int64, updateTs int64) ([]*MainServer.Inventory, error) {
	var query string
	var args []interface{}

	if updateTs > 0 {
		query = fmt.Sprintf(`
			SELECT id, tid, item_name, quantity, price, special_coin, status, create_ts, update_ts
			FROM %s
			WHERE tid = $1 AND update_ts > $2
		`, TableInventoryName)
		args = []interface{}{tid, updateTs}
	} else {
		query = fmt.Sprintf(`
			SELECT id, tid, item_name, quantity, price, special_coin, status, create_ts, update_ts
			FROM %s
			WHERE tid = $1
		`, TableInventoryName)
		args = []interface{}{tid}
	}

	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}
	defer rows.Close()

	var items []*MainServer.Inventory
	for rows.Next() {
		item := &MainServer.Inventory{}
		var status int
		err := rows.Scan(
			&item.Id,
			&item.Tid,
			&item.ItemName,
			&item.Quantity,
			&item.Price,
			&item.SpecialCoin,
			&status,
			&item.CreateTs,
			&item.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描道具数据失败: %w", err)
		}
		item.Status = MainServer.InventoryStatus(status)
		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历道具数据失败: %w", err)
	}

	return items, nil
}

// 查询道具
// 通过ItemName、tid、Status和updateTs获取道具列表
// 如果itemName为空字符串，则查询所有道具
// 如果status为InventoryStatus_Unknown，则查询所有状态
// 如果updateTs大于0，则只查询更新时间大于updateTs的记录
func QueryItems(ctx context.Context, tx *sql.Tx, tid int64, itemName string, status MainServer.InventoryStatus, updateTs int64) ([]*MainServer.Inventory, error) {
	// 构建查询条件
	conditions := []string{"tid = $1"}
	args := []interface{}{tid}
	paramIndex := 2

	if itemName != "" {
		conditions = append(conditions, fmt.Sprintf("item_name = $%d", paramIndex))
		args = append(args, itemName)
		paramIndex++
	}

	if status != 0 { // 0 表示 Unknown
		conditions = append(conditions, fmt.Sprintf("status = $%d", paramIndex))
		args = append(args, int(status))
		paramIndex++
	}

	if updateTs > 0 {
		conditions = append(conditions, fmt.Sprintf("update_ts > $%d", paramIndex))
		args = append(args, updateTs)
	}

	// 构建查询语句
	query := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, special_coin, status, create_ts, update_ts
		FROM %s
		WHERE %s
	`, TableInventoryName, strings.Join(conditions, " AND "))

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	var items []*MainServer.Inventory
	for rows.Next() {
		item := &MainServer.Inventory{}
		var statusInt int
		err := rows.Scan(
			&item.Id,
			&item.Tid,
			&item.ItemName,
			&item.Quantity,
			&item.Price,
			&item.SpecialCoin,
			&statusInt,
			&item.CreateTs,
			&item.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描道具数据失败: %w", err)
		}
		item.Status = MainServer.InventoryStatus(statusInt)
		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历道具数据失败: %w", err)
	}

	return items, nil
}

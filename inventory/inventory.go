package inventory

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	TableInventoryName = "inventory"
)

// 初始化库存表
func InitInventory(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createInventoryTableIfNotExists(ctx, logger, db)
}

// 创建库存表（如果不存在）
func createInventoryTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	query := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,
            tid BIGINT NOT NULL,
            item_name VARCHAR(50) NOT NULL,
            quantity INT NOT NULL DEFAULT 0,
            price INT NOT NULL DEFAULT 0,
            team_coin INT NOT NULL DEFAULT 0,
            status INT NOT NULL DEFAULT 0,
            item_sale_type INT NOT NULL DEFAULT 0,
            summon_poke_name_id VARCHAR(50) NOT NULL DEFAULT '',
            seal_info JSONB NOT NULL DEFAULT '{}',
            create_ts BIGINT NOT NULL,
            update_ts BIGINT NOT NULL,
            UNIQUE(tid, item_name, summon_poke_name_id, status, item_sale_type, (seal_info->>'poke_id'))
        );
        CREATE INDEX IF NOT EXISTS idx_inventory_id ON %s (id);
        CREATE INDEX IF NOT EXISTS idx_inventory_tid ON %s (tid);
        CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON %s (item_name);
        CREATE INDEX IF NOT EXISTS idx_inventory_status ON %s (status);
        CREATE INDEX IF NOT EXISTS idx_inventory_price ON %s (price);
        CREATE INDEX IF NOT EXISTS idx_inventory_team_coin ON %s (team_coin);
        CREATE INDEX IF NOT EXISTS idx_inventory_item_sale_type ON %s (item_sale_type);
        CREATE INDEX IF NOT EXISTS idx_inventory_summon_poke_name_id ON %s (summon_poke_name_id);
        CREATE INDEX IF NOT EXISTS idx_inventory_seal_info_poke_id ON %s ((seal_info->>'poke_id'));
    `, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName, TableInventoryName)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("创建库存表失败: %v", err)
		return
	}

	logger.Info("库存表初始化完成")
}

// 添加或更新道具（合并函数）
// 通过完整的道具信息进行添加或更新
func UpsertItem(ctx context.Context, tx *sql.Tx, inventory *MainServer.Inventory) error {
	// 序列化 seal_info
	sealInfoBytes, err := json.Marshal(inventory.SealInfo)
	if err != nil {
		return fmt.Errorf("序列化封印信息失败: %w", err)
	}

	now := time.Now().Unix()
	if inventory.CreateTs == 0 {
		inventory.CreateTs = now
	}
	inventory.UpdateTs = now

	query := fmt.Sprintf(`
		INSERT INTO %s (tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		ON CONFLICT (tid, item_name, summon_poke_name_id, status, item_sale_type, (seal_info->>'poke_id'))
		DO UPDATE SET
			quantity = %s.quantity + $3,
			price = $4,
			team_coin = $5,
			update_ts = $11
		RETURNING id
	`, TableInventoryName, TableInventoryName)

	err = tx.QueryRowContext(ctx, query,
		inventory.Tid,
		inventory.ItemName,
		inventory.Quantity,
		inventory.Price,
		inventory.TeamCoin,
		int(inventory.Status),
		int(inventory.ItemSaleType),
		inventory.SummonPokeNameId,
		string(sealInfoBytes),
		inventory.CreateTs,
		inventory.UpdateTs,
	).Scan(&inventory.Id)

	if err != nil {
		return fmt.Errorf("添加或更新道具失败: %w", err)
	}

	return nil
}

// 移除道具
// 通过ItemName、tid和count进行移除，注意数量判断
func RemoveItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) error {
	// 检查道具数量是否足够
	hasEnough, err := CheckItemQuantityWithDetails(ctx, tx, tid, itemName, count, summonPokeNameId, sealInfo)
	if err != nil {
		return err
	}
	if !hasEnough {
		return fmt.Errorf("道具数量不足")
	}

	// 更新道具数量
	err = updateItemQuantityWithDetails(ctx, tx, tid, itemName, -count, MainServer.InventoryStatus_InventoryStatus_Normal, summonPokeNameId, sealInfo)
	if err != nil {
		return err
	}

	return nil
}

// 查找普通状态的道具（用于上架）
func findNormalItemForSale(ctx context.Context, tx *sql.Tx, tid int64, itemName string, requiredCount int32) (*MainServer.Inventory, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
		FROM %s
		WHERE tid = $1 AND item_name = $2 AND status = $3 AND (item_sale_type = $4 OR item_sale_type = $5) AND quantity >= $6
		ORDER BY quantity DESC, update_ts ASC
		LIMIT 1
	`, TableInventoryName)

	row := tx.QueryRowContext(ctx, query, tid, itemName,
		int(MainServer.InventoryStatus_InventoryStatus_Normal),
		int(MainServer.InventoryItemSaleType_ItemSaleType_Normal),
		int(MainServer.InventoryItemSaleType_ItemSaleType_Team_Normal),
		requiredCount)

	item := &MainServer.Inventory{}
	var status int
	var itemSaleType int
	var sealInfoStr string

	err := row.Scan(
		&item.Id,
		&item.Tid,
		&item.ItemName,
		&item.Quantity,
		&item.Price,
		&item.TeamCoin,
		&status,
		&itemSaleType,
		&item.SummonPokeNameId,
		&sealInfoStr,
		&item.CreateTs,
		&item.UpdateTs,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("没有找到足够数量的可上架道具")
		}
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}

	item.Status = MainServer.InventoryStatus(status)
	item.ItemSaleType = MainServer.InventoryItemSaleType(itemSaleType)

	// 反序列化 seal_info
	item.SealInfo = &MainServer.InventorySealInfo{}
	if sealInfoStr != "" && sealInfoStr != "{}" {
		err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
		if err != nil {
			return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
		}
	}

	return item, nil
}

// 上架道具
// 通过 tid, itemName, count, price 进行上架
func SaleItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32, price int32) error {
	// 1. 查找可上架的普通状态道具
	sourceItem, err := findNormalItemForSale(ctx, tx, tid, itemName, count)
	if err != nil {
		return err
	}

	// 2. 减少原道具数量
	sourceItem.Quantity -= count
	if sourceItem.Quantity > 0 {
		// 如果还有剩余，更新原记录
		err = UpsertItem(ctx, tx, sourceItem)
		if err != nil {
			return fmt.Errorf("更新原道具数量失败: %w", err)
		}
	} else {
		// 如果数量为0，删除原记录
		err = deleteItemById(ctx, tx, sourceItem.Id)
		if err != nil {
			return fmt.Errorf("删除空道具记录失败: %w", err)
		}
	}

	// 3. 创建或更新上架状态的道具
	saleItem := &MainServer.Inventory{
		Tid:              tid,
		ItemName:         itemName,
		Quantity:         count,
		Price:            price,
		TeamCoin:         0, // 上架时默认为0
		Status:           MainServer.InventoryStatus_InventoryStatus_Sale,
		ItemSaleType:     sourceItem.ItemSaleType, // 保持原有的销售类型
		SummonPokeNameId: sourceItem.SummonPokeNameId,
		SealInfo:         sourceItem.SealInfo,
	}

	err = UpsertItem(ctx, tx, saleItem)
	if err != nil {
		return fmt.Errorf("上架道具失败: %w", err)
	}

	return nil
}

// 根据ID查询道具
func getItemById(ctx context.Context, tx *sql.Tx, itemId int64) (*MainServer.Inventory, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
		FROM %s
		WHERE id = $1
	`, TableInventoryName)

	row := tx.QueryRowContext(ctx, query, itemId)

	item := &MainServer.Inventory{}
	var status int
	var itemSaleType int
	var sealInfoStr string

	err := row.Scan(
		&item.Id,
		&item.Tid,
		&item.ItemName,
		&item.Quantity,
		&item.Price,
		&item.TeamCoin,
		&status,
		&itemSaleType,
		&item.SummonPokeNameId,
		&sealInfoStr,
		&item.CreateTs,
		&item.UpdateTs,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("道具不存在")
		}
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}

	item.Status = MainServer.InventoryStatus(status)
	item.ItemSaleType = MainServer.InventoryItemSaleType(itemSaleType)

	// 反序列化 seal_info
	item.SealInfo = &MainServer.InventorySealInfo{}
	if sealInfoStr != "" && sealInfoStr != "{}" {
		err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
		if err != nil {
			return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
		}
	}

	return item, nil
}

// 根据ID删除道具
func deleteItemById(ctx context.Context, tx *sql.Tx, itemId int64) error {
	query := fmt.Sprintf(`DELETE FROM %s WHERE id = $1`, TableInventoryName)

	result, err := tx.ExecContext(ctx, query, itemId)
	if err != nil {
		return fmt.Errorf("删除道具失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("道具不存在")
	}

	return nil
}

// 查找上架状态的道具（用于下架）
func findSaleItemForUnsale(ctx context.Context, tx *sql.Tx, tid int64, itemName string, requiredCount int32) (*MainServer.Inventory, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
		FROM %s
		WHERE tid = $1 AND item_name = $2 AND status = $3 AND quantity >= $4
		ORDER BY quantity DESC, update_ts ASC
		LIMIT 1
	`, TableInventoryName)

	row := tx.QueryRowContext(ctx, query, tid, itemName,
		int(MainServer.InventoryStatus_InventoryStatus_Sale),
		requiredCount)

	item := &MainServer.Inventory{}
	var status int
	var itemSaleType int
	var sealInfoStr string

	err := row.Scan(
		&item.Id,
		&item.Tid,
		&item.ItemName,
		&item.Quantity,
		&item.Price,
		&item.TeamCoin,
		&status,
		&itemSaleType,
		&item.SummonPokeNameId,
		&sealInfoStr,
		&item.CreateTs,
		&item.UpdateTs,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("没有找到足够数量的上架道具")
		}
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}

	item.Status = MainServer.InventoryStatus(status)
	item.ItemSaleType = MainServer.InventoryItemSaleType(itemSaleType)

	// 反序列化 seal_info
	item.SealInfo = &MainServer.InventorySealInfo{}
	if sealInfoStr != "" && sealInfoStr != "{}" {
		err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
		if err != nil {
			return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
		}
	}

	return item, nil
}

// 下架道具
// 通过 tid, itemName, count 进行下架
func UnsaleItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32) error {
	// 1. 查找可下架的上架状态道具
	sourceItem, err := findSaleItemForUnsale(ctx, tx, tid, itemName, count)
	if err != nil {
		return err
	}

	// 2. 减少上架道具数量
	sourceItem.Quantity -= count
	if sourceItem.Quantity > 0 {
		// 如果还有剩余，更新原记录
		err = UpsertItem(ctx, tx, sourceItem)
		if err != nil {
			return fmt.Errorf("更新上架道具数量失败: %w", err)
		}
	} else {
		// 如果数量为0，删除原记录
		err = deleteItemById(ctx, tx, sourceItem.Id)
		if err != nil {
			return fmt.Errorf("删除空道具记录失败: %w", err)
		}
	}

	// 3. 创建或更新普通状态的道具
	normalItem := &MainServer.Inventory{
		Tid:              tid,
		ItemName:         itemName,
		Quantity:         count,
		Price:            0, // 下架后价格重置为0
		TeamCoin:         0, // 下架后团队币重置为0
		Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
		ItemSaleType:     sourceItem.ItemSaleType, // 保持原有的销售类型
		SummonPokeNameId: sourceItem.SummonPokeNameId,
		SealInfo:         sourceItem.SealInfo,
	}

	err = UpsertItem(ctx, tx, normalItem)
	if err != nil {
		return fmt.Errorf("下架道具失败: %w", err)
	}

	return nil
}

// 检查道具数量（简化版本，向后兼容）
// 通过ItemName、tid和count进行判断，看该tid是否有相应数量的Item
func CheckItemQuantity(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int) (bool, error) {
	return checkItemQuantityWithStatus(ctx, tx, tid, itemName, count, MainServer.InventoryStatus_InventoryStatus_Normal)
}

// 检查道具数量（详细版本）
// 通过完整的道具信息进行判断
func CheckItemQuantityWithDetails(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) (bool, error) {
	return checkItemQuantityWithStatusAndDetails(ctx, tx, tid, itemName, count, MainServer.InventoryStatus_InventoryStatus_Normal, summonPokeNameId, sealInfo)
}

// 检查指定状态的道具数量（简化版本）
func checkItemQuantityWithStatus(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int, status MainServer.InventoryStatus) (bool, error) {
	var quantity int
	query := fmt.Sprintf(`
		SELECT quantity FROM %s
		WHERE tid = $1 AND item_name = $2 AND status = $3 AND summon_poke_name_id = '' AND seal_info = '{}'
	`, TableInventoryName)

	err := tx.QueryRowContext(ctx, query, tid, itemName, int(status)).Scan(&quantity)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil // 道具不存在
		}
		return false, fmt.Errorf("查询道具数量失败: %w", err)
	}

	return quantity >= count, nil
}

// 检查指定状态的道具数量（详细版本）
func checkItemQuantityWithStatusAndDetails(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32, status MainServer.InventoryStatus, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) (bool, error) {
	// 序列化 seal_info
	sealInfoBytes, err := json.Marshal(sealInfo)
	if err != nil {
		return false, fmt.Errorf("序列化封印信息失败: %w", err)
	}

	var quantity int32
	query := fmt.Sprintf(`
		SELECT quantity FROM %s
		WHERE tid = $1 AND item_name = $2 AND status = $3 AND summon_poke_name_id = $4 AND seal_info = $5
	`, TableInventoryName)

	err = tx.QueryRowContext(ctx, query, tid, itemName, int(status), summonPokeNameId, string(sealInfoBytes)).Scan(&quantity)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, nil // 道具不存在
		}
		return false, fmt.Errorf("查询道具数量失败: %w", err)
	}

	return quantity >= count, nil
}

// 添加道具（简化版本，向后兼容）
// 通过ItemName、tid和count进行添加
func AddItem(ctx context.Context, tx *sql.Tx, tid int64, itemName string, count int32) error {
	inventory := &MainServer.Inventory{
		Tid:              tid,
		ItemName:         itemName,
		Quantity:         count,
		Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
		ItemSaleType:     MainServer.InventoryItemSaleType_ItemSaleType_Normal,
		SummonPokeNameId: "",
		SealInfo:         &MainServer.InventorySealInfo{},
	}
	return UpsertItem(ctx, tx, inventory)
}

// 更新道具数量（简化版本，向后兼容）
// 内部函数，用于更新道具数量
func updateItemQuantity(ctx context.Context, tx *sql.Tx, tid int64, itemName string, countDelta int, status MainServer.InventoryStatus) error {
	return updateItemQuantityWithDetails(ctx, tx, tid, itemName, int32(countDelta), status, "", &MainServer.InventorySealInfo{})
}

// 更新道具数量（详细版本）
// 内部函数，用于更新道具数量
func updateItemQuantityWithDetails(ctx context.Context, tx *sql.Tx, tid int64, itemName string, countDelta int32, status MainServer.InventoryStatus, summonPokeNameId string, sealInfo *MainServer.InventorySealInfo) error {
	// 序列化 seal_info
	sealInfoBytes, err := json.Marshal(sealInfo)
	if err != nil {
		return fmt.Errorf("序列化封印信息失败: %w", err)
	}

	now := time.Now().Unix()
	query := fmt.Sprintf(`
		UPDATE %s
		SET quantity = quantity + $1, update_ts = $2
		WHERE tid = $3 AND item_name = $4 AND status = $5 AND summon_poke_name_id = $6 AND seal_info = $7
	`, TableInventoryName)

	result, err := tx.ExecContext(ctx, query, countDelta, now, tid, itemName, int(status), summonPokeNameId, string(sealInfoBytes))
	if err != nil {
		return fmt.Errorf("更新道具数量失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("道具不存在")
	}

	return nil
}

// 获取玩家的所有道具
// 如果updateTs大于0，则只查询更新时间大于updateTs的记录
func GetAllItems(ctx context.Context, tx *sql.Tx, tid int64, updateTs int64) ([]*MainServer.Inventory, error) {
	var query string
	var args []interface{}

	if updateTs > 0 {
		query = fmt.Sprintf(`
			SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
			FROM %s
			WHERE tid = $1 AND update_ts > $2
		`, TableInventoryName)
		args = []interface{}{tid, updateTs}
	} else {
		query = fmt.Sprintf(`
			SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
			FROM %s
			WHERE tid = $1
		`, TableInventoryName)
		args = []interface{}{tid}
	}

	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}
	defer rows.Close()

	var items []*MainServer.Inventory
	for rows.Next() {
		item := &MainServer.Inventory{}
		var status int
		var itemSaleType int
		var sealInfoStr string
		err := rows.Scan(
			&item.Id,
			&item.Tid,
			&item.ItemName,
			&item.Quantity,
			&item.Price,
			&item.TeamCoin,
			&status,
			&itemSaleType,
			&item.SummonPokeNameId,
			&sealInfoStr,
			&item.CreateTs,
			&item.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描道具数据失败: %w", err)
		}

		item.Status = MainServer.InventoryStatus(status)
		item.ItemSaleType = MainServer.InventoryItemSaleType(itemSaleType)

		// 反序列化 seal_info
		item.SealInfo = &MainServer.InventorySealInfo{}
		if sealInfoStr != "" && sealInfoStr != "{}" {
			err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
			}
		}

		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历道具数据失败: %w", err)
	}

	return items, nil
}

// 查询道具
// 通过ItemName、tid、Status和updateTs获取道具列表
// 如果itemName为空字符串，则查询所有道具
// 如果status为InventoryStatus_Unknown，则查询所有状态
// 如果updateTs大于0，则只查询更新时间大于updateTs的记录
func QueryItems(ctx context.Context, tx *sql.Tx, tid int64, itemName string, status MainServer.InventoryStatus, updateTs int64) ([]*MainServer.Inventory, error) {
	// 构建查询条件
	conditions := []string{"tid = $1"}
	args := []interface{}{tid}
	paramIndex := 2

	if itemName != "" {
		conditions = append(conditions, fmt.Sprintf("item_name = $%d", paramIndex))
		args = append(args, itemName)
		paramIndex++
	}

	if status != 0 { // 0 表示 Unknown
		conditions = append(conditions, fmt.Sprintf("status = $%d", paramIndex))
		args = append(args, int(status))
		paramIndex++
	}

	if updateTs > 0 {
		conditions = append(conditions, fmt.Sprintf("update_ts > $%d", paramIndex))
		args = append(args, updateTs)
	}

	// 构建查询语句
	query := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
		FROM %s
		WHERE %s
	`, TableInventoryName, strings.Join(conditions, " AND "))

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	var items []*MainServer.Inventory
	for rows.Next() {
		item := &MainServer.Inventory{}
		var statusInt int
		var itemSaleType int
		var sealInfoStr string
		err := rows.Scan(
			&item.Id,
			&item.Tid,
			&item.ItemName,
			&item.Quantity,
			&item.Price,
			&item.TeamCoin,
			&statusInt,
			&itemSaleType,
			&item.SummonPokeNameId,
			&sealInfoStr,
			&item.CreateTs,
			&item.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描道具数据失败: %w", err)
		}

		item.Status = MainServer.InventoryStatus(statusInt)
		item.ItemSaleType = MainServer.InventoryItemSaleType(itemSaleType)

		// 反序列化 seal_info
		item.SealInfo = &MainServer.InventorySealInfo{}
		if sealInfoStr != "" && sealInfoStr != "{}" {
			err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
			}
		}

		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历道具数据失败: %w", err)
	}

	return items, nil
}

// 查询道具（扩展版本）
// 支持更多查询条件：ItemSaleType、SummonPokeNameId、SealInfo.PokeId
func QueryItemsExtended(ctx context.Context, tx *sql.Tx, tid int64, itemName string, status MainServer.InventoryStatus, itemSaleType MainServer.InventoryItemSaleType, summonPokeNameId string, sealPokeId int64, updateTs int64) ([]*MainServer.Inventory, error) {
	// 构建查询条件
	conditions := []string{"tid = $1"}
	args := []interface{}{tid}
	paramIndex := 2

	if itemName != "" {
		conditions = append(conditions, fmt.Sprintf("item_name = $%d", paramIndex))
		args = append(args, itemName)
		paramIndex++
	}

	if status != 0 { // 0 表示 Unknown
		conditions = append(conditions, fmt.Sprintf("status = $%d", paramIndex))
		args = append(args, int(status))
		paramIndex++
	}

	if itemSaleType != 0 { // 0 表示 Normal
		conditions = append(conditions, fmt.Sprintf("item_sale_type = $%d", paramIndex))
		args = append(args, int(itemSaleType))
		paramIndex++
	}

	if summonPokeNameId != "" {
		conditions = append(conditions, fmt.Sprintf("summon_poke_name_id = $%d", paramIndex))
		args = append(args, summonPokeNameId)
		paramIndex++
	}

	if sealPokeId > 0 {
		conditions = append(conditions, fmt.Sprintf("seal_info->>'poke_id' = $%d", paramIndex))
		args = append(args, fmt.Sprintf("%d", sealPokeId))
		paramIndex++
	}

	if updateTs > 0 {
		conditions = append(conditions, fmt.Sprintf("update_ts > $%d", paramIndex))
		args = append(args, updateTs)
	}

	// 构建查询语句
	query := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
		FROM %s
		WHERE %s
		ORDER BY update_ts DESC
	`, TableInventoryName, strings.Join(conditions, " AND "))

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询道具失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	var items []*MainServer.Inventory
	for rows.Next() {
		item := &MainServer.Inventory{}
		var statusInt int
		var itemSaleTypeInt int
		var sealInfoStr string
		err := rows.Scan(
			&item.Id,
			&item.Tid,
			&item.ItemName,
			&item.Quantity,
			&item.Price,
			&item.TeamCoin,
			&statusInt,
			&itemSaleTypeInt,
			&item.SummonPokeNameId,
			&sealInfoStr,
			&item.CreateTs,
			&item.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描道具数据失败: %w", err)
		}

		item.Status = MainServer.InventoryStatus(statusInt)
		item.ItemSaleType = MainServer.InventoryItemSaleType(itemSaleTypeInt)

		// 反序列化 seal_info
		item.SealInfo = &MainServer.InventorySealInfo{}
		if sealInfoStr != "" && sealInfoStr != "{}" {
			err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
			}
		}

		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历道具数据失败: %w", err)
	}

	return items, nil
}

// 批量添加或更新道具
func BatchUpsertItems(ctx context.Context, tx *sql.Tx, inventories []*MainServer.Inventory) error {
	for _, inventory := range inventories {
		err := UpsertItem(ctx, tx, inventory)
		if err != nil {
			return fmt.Errorf("批量添加道具失败，道具: %s, 错误: %w", inventory.ItemName, err)
		}
	}
	return nil
}

// 获取道具总数量（按条件）
func GetItemTotalQuantity(ctx context.Context, tx *sql.Tx, tid int64, itemName string, status MainServer.InventoryStatus) (int32, error) {
	var total int32
	query := fmt.Sprintf(`
		SELECT COALESCE(SUM(quantity), 0) FROM %s
		WHERE tid = $1 AND item_name = $2 AND status = $3
	`, TableInventoryName)

	err := tx.QueryRowContext(ctx, query, tid, itemName, int(status)).Scan(&total)
	if err != nil {
		return 0, fmt.Errorf("查询道具总数量失败: %w", err)
	}

	return total, nil
}

// 获取指定训练师的可上架道具列表
func GetSaleableItems(ctx context.Context, tx *sql.Tx, tid int64) ([]*MainServer.Inventory, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
		FROM %s
		WHERE tid = $1 AND status = $2 AND (item_sale_type = $3 OR item_sale_type = $4) AND quantity > 0
		ORDER BY item_name, update_ts DESC
	`, TableInventoryName)

	rows, err := tx.QueryContext(ctx, query, tid,
		int(MainServer.InventoryStatus_InventoryStatus_Normal),
		int(MainServer.InventoryItemSaleType_ItemSaleType_Normal),
		int(MainServer.InventoryItemSaleType_ItemSaleType_Team_Normal))
	if err != nil {
		return nil, fmt.Errorf("查询可上架道具失败: %w", err)
	}
	defer rows.Close()

	var items []*MainServer.Inventory
	for rows.Next() {
		item := &MainServer.Inventory{}
		var status int
		var itemSaleType int
		var sealInfoStr string
		err := rows.Scan(
			&item.Id,
			&item.Tid,
			&item.ItemName,
			&item.Quantity,
			&item.Price,
			&item.TeamCoin,
			&status,
			&itemSaleType,
			&item.SummonPokeNameId,
			&sealInfoStr,
			&item.CreateTs,
			&item.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描道具数据失败: %w", err)
		}

		item.Status = MainServer.InventoryStatus(status)
		item.ItemSaleType = MainServer.InventoryItemSaleType(itemSaleType)

		// 反序列化 seal_info
		item.SealInfo = &MainServer.InventorySealInfo{}
		if sealInfoStr != "" && sealInfoStr != "{}" {
			err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
			}
		}

		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历道具数据失败: %w", err)
	}

	return items, nil
}

// 获取指定训练师的已上架道具列表
func GetSaledItems(ctx context.Context, tx *sql.Tx, tid int64) ([]*MainServer.Inventory, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, item_name, quantity, price, team_coin, status, item_sale_type, summon_poke_name_id, seal_info, create_ts, update_ts
		FROM %s
		WHERE tid = $1 AND status = $2 AND quantity > 0
		ORDER BY item_name, update_ts DESC
	`, TableInventoryName)

	rows, err := tx.QueryContext(ctx, query, tid, int(MainServer.InventoryStatus_InventoryStatus_Sale))
	if err != nil {
		return nil, fmt.Errorf("查询已上架道具失败: %w", err)
	}
	defer rows.Close()

	var items []*MainServer.Inventory
	for rows.Next() {
		item := &MainServer.Inventory{}
		var status int
		var itemSaleType int
		var sealInfoStr string
		err := rows.Scan(
			&item.Id,
			&item.Tid,
			&item.ItemName,
			&item.Quantity,
			&item.Price,
			&item.TeamCoin,
			&status,
			&itemSaleType,
			&item.SummonPokeNameId,
			&sealInfoStr,
			&item.CreateTs,
			&item.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描道具数据失败: %w", err)
		}

		item.Status = MainServer.InventoryStatus(status)
		item.ItemSaleType = MainServer.InventoryItemSaleType(itemSaleType)

		// 反序列化 seal_info
		item.SealInfo = &MainServer.InventorySealInfo{}
		if sealInfoStr != "" && sealInfoStr != "{}" {
			err = json.Unmarshal([]byte(sealInfoStr), item.SealInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化封印信息失败: %w", err)
			}
		}

		items = append(items, item)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历道具数据失败: %w", err)
	}

	return items, nil
}

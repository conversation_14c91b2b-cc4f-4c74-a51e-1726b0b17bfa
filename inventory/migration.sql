-- Inventory 表结构迁移脚本
-- 执行前请备份数据库！

-- 1. 添加新字段
ALTER TABLE inventory ADD COLUMN IF NOT EXISTS item_sale_type INT NOT NULL DEFAULT 0;
ALTER TABLE inventory ADD COLUMN IF NOT EXISTS summon_poke_name_id VARCHAR(50) NOT NULL DEFAULT '';
ALTER TABLE inventory ADD COLUMN IF NOT EXISTS seal_info JSONB NOT NULL DEFAULT '{}';

-- 2. 重命名字段 (如果需要保留数据)
-- 注意：这会创建新字段并复制数据，然后删除旧字段
ALTER TABLE inventory ADD COLUMN IF NOT EXISTS team_coin INT NOT NULL DEFAULT 0;
UPDATE inventory SET team_coin = special_coin WHERE team_coin = 0;
-- 在确认数据正确后，可以删除旧字段：
-- ALTER TABLE inventory DROP COLUMN IF EXISTS special_coin;

-- 3. 修改字段长度
ALTER TABLE inventory ALTER COLUMN item_name TYPE VARCHAR(50);

-- 4. 删除旧的唯一约束
ALTER TABLE inventory DROP CONSTRAINT IF EXISTS inventory_tid_item_name_status_key;

-- 5. 添加新的唯一约束
ALTER TABLE inventory ADD CONSTRAINT inventory_unique_constraint 
    UNIQUE(tid, item_name, summon_poke_name_id, status, item_sale_type, (seal_info->>'poke_id'));

-- 6. 删除旧索引（如果存在）
DROP INDEX IF EXISTS idx_id;
DROP INDEX IF EXISTS idx_tid;
DROP INDEX IF EXISTS idx_item_name;
DROP INDEX IF EXISTS idx_status;
DROP INDEX IF EXISTS idx_price;
DROP INDEX IF EXISTS idx_special_coin;

-- 7. 创建新索引
CREATE INDEX IF NOT EXISTS idx_inventory_id ON inventory (id);
CREATE INDEX IF NOT EXISTS idx_inventory_tid ON inventory (tid);
CREATE INDEX IF NOT EXISTS idx_inventory_item_name ON inventory (item_name);
CREATE INDEX IF NOT EXISTS idx_inventory_status ON inventory (status);
CREATE INDEX IF NOT EXISTS idx_inventory_price ON inventory (price);
CREATE INDEX IF NOT EXISTS idx_inventory_team_coin ON inventory (team_coin);
CREATE INDEX IF NOT EXISTS idx_inventory_item_sale_type ON inventory (item_sale_type);
CREATE INDEX IF NOT EXISTS idx_inventory_summon_poke_name_id ON inventory (summon_poke_name_id);
CREATE INDEX IF NOT EXISTS idx_inventory_seal_info_poke_id ON inventory ((seal_info->>'poke_id'));

-- 8. 验证迁移结果
-- 检查表结构
\d inventory;

-- 检查数据完整性
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT tid) as unique_trainers,
    COUNT(DISTINCT item_name) as unique_items
FROM inventory;

-- 检查新字段的数据分布
SELECT 
    item_sale_type,
    COUNT(*) as count
FROM inventory 
GROUP BY item_sale_type;

SELECT 
    CASE 
        WHEN summon_poke_name_id = '' THEN 'empty'
        ELSE 'has_value'
    END as summon_status,
    COUNT(*) as count
FROM inventory 
GROUP BY summon_status;

SELECT 
    CASE 
        WHEN seal_info = '{}' THEN 'empty'
        ELSE 'has_value'
    END as seal_status,
    COUNT(*) as count
FROM inventory 
GROUP BY seal_status;

-- 9. 清理脚本（可选，在确认迁移成功后执行）
-- 删除旧的 special_coin 字段
-- ALTER TABLE inventory DROP COLUMN IF EXISTS special_coin;

COMMIT;

package inventory

// import (
// 	"context"
// 	"database/sql"
// 	"encoding/json"
// 	"fmt"
// 	"go-nakama-poke/config"
// 	"go-nakama-poke/item"
// 	"go-nakama-poke/proto/MainServer"
// 	"time"

// 	"github.com/heroiclabs/nakama-common/runtime"
// )

// const TableInventoryName = "inventory"

// func InitInventorys(ctx context.Context, logger runtime.Logger, db *sql.DB) {
// 	createInventoryTableIfNotExists(ctx, logger, db)
// }

// func InitTrainerInventory(ctx context.Context, tid int64, tx *sql.Tx) error {
// 	// tid := tool.GetActiveTid(ctx)

// 	// 定义所有 Inventory 类型
// 	inventoryTypes := []MainServer.InventoryType{
// 		MainServer.InventoryType_inventory_nor,
// 		MainServer.InventoryType_inventory_poke,
// 		MainServer.InventoryType_inventory_ball,
// 		MainServer.InventoryType_inventory_battlesys,
// 		MainServer.InventoryType_inventory_healing,
// 		MainServer.InventoryType_inventory_berry,
// 		MainServer.InventoryType_inventory_box,
// 		MainServer.InventoryType_inventory_carry,
// 		MainServer.InventoryType_inventory_cultivate,
// 	}

// 	// 遍历每种 InventoryType 并插入记录
// 	for _, inventoryType := range inventoryTypes {
// 		inventory := &MainServer.Inventory{
// 			Tid:           tid,
// 			Name:          "", // 根据需要可赋值实际名称
// 			MaxSlots:      config.ItemMaxSlots,
// 			InventoryType: inventoryType,
// 			CreateTs:      time.Now().UnixMilli(),
// 			UpdateTs:      time.Now().UnixMilli(),
// 		}

// 		// 调用 upsertInventory 函数
// 		if err := upsertInventory(ctx, tx, inventory); err != nil {
// 			tx.Rollback() // 出错时回滚事务
// 			return fmt.Errorf("failed to upsert inventory for type %d: %v", inventoryType, err)
// 		}
// 	}

// 	return nil
// }

// func upsertInventory(ctx context.Context, tx *sql.Tx, inventory *MainServer.Inventory) (int64 error) {
// 	inventory.UpdateTs = time.Now().UnixMilli()
// 	// tableName := TableInventoryName
// 	// if inventory.Id < 0 {
// 	// 	err := tx.QueryRowContext(ctx, fmt.Sprintf("SELECT nextval(pg_get_serial_sequence('%s', 'id'))", tableName)).Scan(&inventory.Id)
// 	// 	if err != nil {
// 	// 		return 0, fmt.Errorf("failed to generate new id: %v", err)
// 	// 	}
// 	// }
// 	tableName := TableInventoryName
// 	updateSQL := fmt.Sprintf(`
// 		INSERT INTO %s (tid, name, max_slots, inventory_type, slots, create_ts, update_ts)
// 		VALUES ($1, $2, $3, $4, $5, $6, $7)
// 		ON CONFLICT (tid, inventory_type) DO UPDATE  -- 确保唯一性
// 		SET tid = EXCLUDED.tid,
// 		    name = EXCLUDED.name,
// 		    max_slots = EXCLUDED.max_slots,
// 		    inventory_type = EXCLUDED.inventory_type,
// 		    slots = EXCLUDED.slots,
// 		    create_ts = EXCLUDED.create_ts,
// 		    update_ts = EXCLUDED.update_ts;
// 	`, tableName)

// 	// 序列化 slots 字段为 JSON
// 	slotsJSON, err := json.Marshal(inventory.Slots)
// 	if err != nil {
// 		return fmt.Errorf("序列化 slots 失败: %v", err)
// 	}

// 	_, err = tx.ExecContext(ctx, updateSQL,
// 		inventory.Tid,
// 		inventory.Name,
// 		inventory.MaxSlots,
// 		int32(inventory.InventoryType),
// 		slotsJSON,
// 		inventory.CreateTs,
// 		inventory.UpdateTs,
// 	)
// 	if err != nil {
// 		return fmt.Errorf("插入或更新 inventory 失败: %v", err)
// 	}

// 	return nil
// }

// func createInventoryTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
// 	tableName := TableInventoryName

// 	createTableSQL := fmt.Sprintf(`
//     CREATE TABLE IF NOT EXISTS %s (
//         id BIGSERIAL PRIMARY KEY,
//         tid BIGINT NOT NULL,
//         name VARCHAR(255),
//         max_slots INT NOT NULL,
//         inventory_type INT NOT NULL,
//         slots JSONB NOT NULL DEFAULT '{}'::jsonb,
//         create_ts BIGINT,
//         update_ts BIGINT,
//         UNIQUE (tid, inventory_type) -- 添加唯一约束
//     );
// `, tableName)

// 	_, err := db.ExecContext(ctx, createTableSQL)
// 	if err != nil {
// 		logger.Error("failed to create table %s: %v", tableName, err)
// 		return fmt.Errorf("failed to create table %s: %v", tableName, err)
// 	}

// 	logger.Info("Inventory table created successfully.")
// 	return nil
// }

// func SelectInventoryProto(ctx context.Context, db *sql.DB, inventoryId string) (*MainServer.Inventory, error) {
// 	query := fmt.Sprintf("SELECT id, tid, name, max_slots, inventory_type, slots, create_ts, update_ts FROM %s WHERE id = $1", TableInventoryName)
// 	row := db.QueryRowContext(ctx, query, inventoryId)

// 	var inventory MainServer.Inventory
// 	var slotsJSON []byte

// 	// 扫描每个字段
// 	if err := row.Scan(
// 		&inventory.Id,
// 		&inventory.Tid,
// 		&inventory.Name,
// 		&inventory.MaxSlots,
// 		&inventory.InventoryType,
// 		&slotsJSON,
// 		&inventory.CreateTs,
// 		&inventory.UpdateTs,
// 	); err != nil {
// 		return nil, fmt.Errorf("获取 inventory 失败: %v", err)
// 	}

// 	// 反序列化 slots 字段
// 	if err := json.Unmarshal(slotsJSON, &inventory.Slots); err != nil {
// 		return nil, fmt.Errorf("反序列化 slots 失败: %v", err)
// 	}

// 	return &inventory, nil
// }

// func SwapSlotIndex(ctx context.Context, db *sql.DB, inventoryID int64, index1, index2 int32) error {
// 	// 获取 Inventory
// 	inventory, err := SelectInventoryProto(ctx, db, fmt.Sprintf("%d", inventoryID))
// 	if err != nil {
// 		return fmt.Errorf("failed to retrieve inventory: %w", err)
// 	}

// 	// 找到指定的两个 slots
// 	var slot1, slot2 *MainServer.InventorySlot
// 	for i := range inventory.Slots {
// 		if inventory.Slots[i].Index == index1 {
// 			slot1 = inventory.Slots[i]
// 		}
// 		if inventory.Slots[i].Index == index2 {
// 			slot2 = inventory.Slots[i]
// 		}
// 	}

// 	// 验证 slots 是否存在
// 	if slot1 == nil || slot2 == nil {
// 		return fmt.Errorf("one or both slots not found: index1=%d, index2=%d", index1, index2)
// 	}

// 	// 交换 index 值
// 	slot1.Index, slot2.Index = slot2.Index, slot1.Index

// 	// 保存修改后的 Inventory
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return fmt.Errorf("failed to begin transaction: %w", err)
// 	}
// 	defer tx.Rollback()

// 	if err := upsertInventory(ctx, tx, inventory); err != nil {
// 		return fmt.Errorf("failed to save updated inventory: %w", err)
// 	}

// 	return tx.Commit()
// }

// func AddItemToInventory(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, itemName string, quantity int32) error {
// 	localItem, ex := item.GetItemByName(itemName)
// 	if !ex {
// 		return fmt.Errorf("failed to read item: %s", itemName)
// 	}
// 	inventoryType := MainServer.InventoryType(localItem.Type)
// 	// // 开启事务
// 	// tx, err := db.BeginTx(ctx, nil)
// 	// if err != nil {
// 	// 	logger.Error("事务启动失败: %v", err)
// 	// 	return fmt.Errorf("failed to start transaction: %w", err)
// 	// }
// 	// defer tx.Rollback()

// 	// 调用 SaveItems 更新或插入物品记录
// 	itemID, totalCount, saleCount, err := item.SaveItems(ctx, logger, tx, fmt.Sprintf("%d", tid), itemName, quantity)
// 	if err != nil {
// 		logger.Error("保存物品失败: %v", err)
// 		return err
// 	}

// 	// 获取 Inventory
// 	inventory, err := SelectInventoryByTidAndType(ctx, tx, tid, inventoryType)
// 	if err != nil {
// 		logger.Error("获取 Inventory 失败: %v", err)
// 		return fmt.Errorf("failed to retrieve inventory: %w", err)
// 	}

// 	// 计算实际数量（剩余数量）
// 	remainingQuantity := totalCount - saleCount

// 	// 查找是否已经有该物品的 Slot
// 	var existingSlot *MainServer.InventorySlot
// 	for i := range inventory.Slots {
// 		if inventory.Slots[i].ItemId == itemID {
// 			existingSlot = inventory.Slots[i]
// 			break
// 		}
// 	}

// 	if existingSlot != nil {
// 		// 更新现有 Slot 的数量
// 		existingSlot.Quantity = remainingQuantity
// 	} else {
// 		// 找到最小的未占用索引
// 		usedIndexes := make(map[int32]bool)
// 		for _, slot := range inventory.Slots {
// 			usedIndexes[slot.Index] = true
// 		}

// 		var newIndex int32
// 		for i := int32(0); i < int32(inventory.MaxSlots); i++ {
// 			if !usedIndexes[i] {
// 				newIndex = i
// 				break
// 			}
// 		}

// 		// 检查是否有可用 Slot
// 		if newIndex >= int32(inventory.MaxSlots) {
// 			logger.Error("Inventory 已满，无法添加新物品")
// 			return fmt.Errorf("no available slots in inventory")
// 		}

// 		// 创建新 Slot
// 		newSlot := MainServer.InventorySlot{
// 			ItemId:       itemID,
// 			ItemName:     itemName,
// 			Quantity:     remainingQuantity,
// 			Index:        newIndex,
// 			MaximumStack: item.MaximumStack,
// 		}
// 		inventory.Slots = append(inventory.Slots, &newSlot)
// 	}

// 	// 保存修改后的 Inventory
// 	if err := upsertInventory(ctx, tx, inventory); err != nil {
// 		logger.Error("保存 Inventory 失败: %v", err)
// 		return fmt.Errorf("failed to save inventory: %w", err)
// 	}

// 	// 提交事务
// 	// if err := tx.Commit(); err != nil {
// 	// 	logger.Error("事务提交失败: %v", err)
// 	// 	return fmt.Errorf("failed to commit transaction: %w", err)
// 	// }

// 	logger.Info("成功更新 Inventory，并添加物品 %s，数量 %d", itemName, quantity)
// 	return nil
// }

// func SelectInventoryByTidAndType(ctx context.Context, tx *sql.Tx, tid int64, inventoryType MainServer.InventoryType) (*MainServer.Inventory, error) {
// 	query := fmt.Sprintf(`
// 		SELECT id, tid, name, max_slots, inventory_type, slots, create_ts, update_ts
// 		FROM %s
// 		WHERE tid = $1 AND inventory_type = $2
// 	`, TableInventoryName)

// 	row := tx.QueryRowContext(ctx, query, tid, inventoryType)

// 	// 使用通用函数解析数据
// 	inventory, err := scanAndDeserializeInventory(row)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to retrieve inventory: %w", err)
// 	}

// 	return inventory, nil
// }

// func SelectAllInventoriesByTid(ctx context.Context, tx *sql.Tx, tid int64) ([]*MainServer.Inventory, error) {
// 	query := fmt.Sprintf(`
// 		SELECT id, tid, name, max_slots, inventory_type, slots, create_ts, update_ts
// 		FROM %s
// 		WHERE tid = $1
// 	`, TableInventoryName)

// 	rows, err := tx.QueryContext(ctx, query, tid)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to query inventories: %w", err)
// 	}
// 	defer rows.Close()

// 	inventories := make([]*MainServer.Inventory, 0)

// 	for rows.Next() {
// 		// 使用通用函数解析数据
// 		inventory, err := scanAndDeserializeInventory(rows)
// 		if err != nil {
// 			return nil, fmt.Errorf("failed to scan inventory row: %w", err)
// 		}
// 		inventories = append(inventories, inventory)
// 	}

// 	// 检查 rows 是否有错误
// 	if err := rows.Err(); err != nil {
// 		return nil, fmt.Errorf("error during rows iteration: %w", err)
// 	}

// 	return inventories, nil
// }

// func scanAndDeserializeInventory(rowScanner interface {
// 	Scan(dest ...interface{}) error
// }) (*MainServer.Inventory, error) {
// 	var inventory MainServer.Inventory
// 	var slotsJSON []byte

// 	// 扫描字段
// 	if err := rowScanner.Scan(
// 		&inventory.Id,
// 		&inventory.Tid,
// 		&inventory.Name,
// 		&inventory.MaxSlots,
// 		&inventory.InventoryType,
// 		&slotsJSON,
// 		&inventory.CreateTs,
// 		&inventory.UpdateTs,
// 	); err != nil {
// 		return nil, fmt.Errorf("failed to scan inventory: %w", err)
// 	}

// 	// 反序列化 slots
// 	if err := json.Unmarshal(slotsJSON, &inventory.Slots); err != nil {
// 		return nil, fmt.Errorf("failed to deserialize slots: %w", err)
// 	}

// 	return &inventory, nil
// }

// // func SelectInventoryByTidAndType(ctx context.Context, db *sql.DB, tid int64, inventoryType MainServer.InventoryType) (*MainServer.Inventory, error) {
// // 	query := fmt.Sprintf("SELECT id, tid, name, max_slots, inventory_type, slots, create_ts, update_ts FROM %s WHERE tid = $1 AND inventory_type = $2", TableInventoryName)
// // 	row := db.QueryRowContext(ctx, query, tid, inventoryType)

// // 	var inventory MainServer.Inventory
// // 	var slotsJSON []byte

// // 	// 扫描字段
// // 	if err := row.Scan(
// // 		&inventory.Id,
// // 		&inventory.Tid,
// // 		&inventory.Name,
// // 		&inventory.MaxSlots,
// // 		&inventory.InventoryType,
// // 		&slotsJSON,
// // 		&inventory.CreateTs,
// // 		&inventory.UpdateTs,
// // 	); err != nil {
// // 		return nil, fmt.Errorf("failed to retrieve inventory: %w", err)
// // 	}

// // 	// 反序列化 slots
// // 	if err := json.Unmarshal(slotsJSON, &inventory.Slots); err != nil {
// // 		return nil, fmt.Errorf("failed to deserialize slots: %w", err)
// // 	}

// // 	return &inventory, nil
// // }
// // func SelectAllInventoriesByTid(ctx context.Context, db *sql.DB, tid int64) (*MainServer.InventorysResult, error) {
// // 	query := fmt.Sprintf(`
// // 		SELECT id, tid, name, max_slots, inventory_type, slots, create_ts, update_ts
// // 		FROM %s
// // 		WHERE tid = $1
// // 	`, TableInventoryName)

// // 	rows, err := db.QueryContext(ctx, query, tid)
// // 	if err != nil {
// // 		return nil, fmt.Errorf("failed to query inventories: %w", err)
// // 	}
// // 	defer rows.Close()

// // 	inventories := make([]*MainServer.Inventory, 0)

// // 	for rows.Next() {
// // 		var inventory MainServer.Inventory
// // 		var slotsJSON []byte

// // 		// 扫描字段
// // 		if err := rows.Scan(
// // 			&inventory.Id,
// // 			&inventory.Tid,
// // 			&inventory.Name,
// // 			&inventory.MaxSlots,
// // 			&inventory.InventoryType,
// // 			&slotsJSON,
// // 			&inventory.CreateTs,
// // 			&inventory.UpdateTs,
// // 		); err != nil {
// // 			return nil, fmt.Errorf("failed to scan inventory row: %w", err)
// // 		}

// // 		// 反序列化 slots
// // 		if err := json.Unmarshal(slotsJSON, &inventory.Slots); err != nil {
// // 			return nil, fmt.Errorf("failed to deserialize slots: %w", err)
// // 		}

// // 		inventories = append(inventories, &inventory)
// // 	}

// // 	// 检查 rows 是否有错误
// // 	if err := rows.Err(); err != nil {
// // 		return nil, fmt.Errorf("error during rows iteration: %w", err)
// // 	}

// // 	// 构造结果
// // 	result := &MainServer.InventorysResult{
// // 		Page:       1,            // 默认第一页
// // 		Ts:         time.Now().UnixMilli(),
// // 		Inventorys: inventories,
// // 	}

// // 	return result, nil
// // }

// func UseItemOnInventory(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, itemName string, quantity int32) error {
// 	// 开启事务
// 	// tx, err := db.BeginTx(ctx, nil)
// 	// if err != nil {
// 	// 	logger.Error("事务启动失败: %v", err)
// 	// 	return fmt.Errorf("failed to start transaction: %w", err)
// 	// }
// 	// defer tx.Rollback()
// 	localItem, ex := item.GetItemByName(itemName)
// 	if !ex {
// 		return fmt.Errorf("failed to read item: %s", itemName)
// 	}
// 	// 获取 Inventory
// 	inventory, err := SelectInventoryByTidAndType(ctx, tx, tid, MainServer.InventoryType(localItem.Type))
// 	if err != nil {
// 		logger.Error("获取 Inventory 失败: %v", err)
// 		return fmt.Errorf("failed to retrieve inventory: %w", err)
// 	}

// 	// 查找指定的 Slot
// 	var targetSlot *MainServer.InventorySlot
// 	for _, slot := range inventory.Slots {
// 		if slot.ItemName == itemName {
// 			targetSlot = slot
// 			break
// 		}
// 	}

// 	// 检查是否找到对应道具
// 	if targetSlot == nil {
// 		logger.Warn("未找到用户 %d 的道具 %s", tid, itemName)
// 		return runtime.NewError("未找到指定道具", 404)
// 	}

// 	// 检查 Slot 中数量是否足够
// 	if targetSlot.Quantity < quantity {
// 		logger.Warn("用户 %d 的道具 %s 数量不足: 当前数量 %d, 需要 %d", tid, itemName, targetSlot.Quantity, quantity)
// 		return runtime.NewError("道具数量不足", 400)
// 	}

// 	// 调用 UseItem 扣减 Items 表中的数据
// 	if err := item.UseItem(ctx, logger, tx, tid, itemName, quantity); err != nil {
// 		logger.Error("扣减物品表中的道具失败: %v", err)
// 		return fmt.Errorf("failed to use item in items table: %w", err)
// 	}

// 	// 扣减 Slot 中的数量
// 	targetSlot.Quantity -= quantity

// 	// 如果 Slot 数量为 0，删除该 Slot
// 	if targetSlot.Quantity == 0 {
// 		for i, slot := range inventory.Slots {
// 			if slot.ItemId == targetSlot.ItemId {
// 				inventory.Slots = append(inventory.Slots[:i], inventory.Slots[i+1:]...)
// 				break
// 			}
// 		}
// 		logger.Info("道具 %s 数量为 0，已从 Inventory 中删除", itemName)
// 	} else {
// 		logger.Info("更新道具 %s 的剩余数量为 %d", itemName, targetSlot.Quantity)
// 	}

// 	// 更新 Inventory
// 	if err := upsertInventory(ctx, tx, inventory); err != nil {
// 		logger.Error("保存更新后的 Inventory 失败: %v", err)
// 		return fmt.Errorf("failed to save updated inventory: %w", err)
// 	}

// 	// 提交事务
// 	// if err := tx.Commit(); err != nil {
// 	// 	logger.Error("事务提交失败: %v", err)
// 	// 	return fmt.Errorf("failed to commit transaction: %w", err)
// 	// }

// 	logger.Info("用户 %d 成功使用了 %d 个道具 %s", tid, quantity, itemName)
// 	return nil
// }

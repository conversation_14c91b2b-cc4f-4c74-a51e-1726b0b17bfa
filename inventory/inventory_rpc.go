package inventory

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
)

// RPC函数：使用道具
// func RpcUseItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 获取当前用户的训练师ID
// 	trainer := tool.GetActiveTrainerByCtx(ctx)
// 	if trainer == nil {
// 		return "", runtime.NewError("未找到训练师信息", 400)
// 	}

// 	// 解析请求参数
// 	var param MainServer.UseItemParam
// 	if err := tool.Base64ToProto(payload, &param); err != nil {
// 		logger.Error("解析UseItemParam失败: %v", err)
// 		return "", runtime.NewError("请求参数无效", 400)
// 	}

// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return "", runtime.NewError(fmt.Sprintf("开始事务失败: %v", err), 500)
// 	}
// 	defer tx.Rollback()

// 	// 使用道具
// 	err = UseItem(ctx, tx, trainer.Id, param.ItemName, int(param.Count))
// 	if err != nil {
// 		return "", runtime.NewError(fmt.Sprintf("使用道具失败: %v", err), 500)
// 	}

// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		return "", runtime.NewError(fmt.Sprintf("提交事务失败: %v", err), 500)
// 	}

// 	// 构建响应
// 	response := &MainServer.UseItemResponse{
// 		Success: true,
// 		Message: "道具使用成功",
// 	}

// 	return tool.ProtoToBase64(response)
// }

// RPC函数：添加道具
// func RpcAddItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 获取当前用户的训练师ID
// 	trainer := tool.GetActiveTrainerByCtx(ctx)
// 	if trainer == nil {
// 		return "", runtime.NewError("未找到训练师信息", 400)
// 	}

// 	// 解析请求参数
// 	var param MainServer.AddItemParam
// 	if err := tool.Base64ToProto(payload, &param); err != nil {
// 		logger.Error("解析AddItemParam失败: %v", err)
// 		return "", runtime.NewError("请求参数无效", 400)
// 	}

// 	// 开始事务
// 	tx, err := db.BeginTx(ctx, nil)
// 	if err != nil {
// 		return "", runtime.NewError(fmt.Sprintf("开始事务失败: %v", err), 500)
// 	}
// 	defer tx.Rollback()

// 	// 添加道具
// 	err = AddItem(ctx, tx, trainer.Id, param.ItemName, param.Count)
// 	if err != nil {
// 		return "", runtime.NewError(fmt.Sprintf("添加道具失败: %v", err), 500)
// 	}

// 	// 提交事务
// 	if err := tx.Commit(); err != nil {
// 		return "", runtime.NewError(fmt.Sprintf("提交事务失败: %v", err), 500)
// 	}

// 	// 构建响应
// 	response := &MainServer.AddItemResponse{
// 		Success: true,
// 		Message: "道具添加成功",
// 	}

// 	return tool.ProtoToBase64(response)
// }

// RPC函数：上架道具
func RpcSaleItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	// 解析请求参数
	var param MainServer.SaleItemParam
	if err := tool.Base64ToProto(payload, &param); err != nil {
		logger.Error("解析SaleItemParam失败: %v", err)
		return "", runtime.NewError("请求参数无效", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("开始事务失败: %v", err), 500)
	}
	defer tx.Rollback()

	// 上架道具
	err = SaleItem(ctx, tx, trainer.Id, param.ItemName, param.Count, param.Price, param.SpecialCoin, MainServer.InventoryItemSaleType_ItemSaleType_Normal, "", &MainServer.InventorySealInfo{})
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("上架道具失败: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError(fmt.Sprintf("提交事务失败: %v", err), 500)
	}

	// 构建响应
	response := &MainServer.SaleItemResponse{
		Success: true,
		Message: "道具上架成功",
	}

	return tool.ProtoToBase64(response)
}

// RPC函数：下架道具
func RpcUnsaleItem(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	// 解析请求参数
	var param MainServer.UnsaleItemParam
	if err := tool.Base64ToProto(payload, &param); err != nil {
		logger.Error("解析UnsaleItemParam失败: %v", err)
		return "", runtime.NewError("请求参数无效", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("开始事务失败: %v", err), 500)
	}
	defer tx.Rollback()

	// 下架道具
	err = UnsaleItem(ctx, tx, trainer.Id, param.ItemName, param.Count, "", &MainServer.InventorySealInfo{})
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("下架道具失败: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError(fmt.Sprintf("提交事务失败: %v", err), 500)
	}

	// 构建响应
	response := &MainServer.UnsaleItemResponse{
		Success: true,
		Message: "道具下架成功",
	}

	return tool.ProtoToBase64(response)
}

// RPC函数：获取所有道具
func RpcGetAllItems(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	// 解析请求参数
	var param MainServer.GetAllItemsParam
	var updateTs int64 = 0

	if payload != "" {
		if err := tool.Base64ToProto(payload, &param); err != nil {
			logger.Error("解析GetAllItemsParam失败: %v", err)
			// 不返回错误，使用默认值继续
		} else {
			updateTs = param.UpdateTs
		}
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("开始事务失败: %v", err), 500)
	}
	defer tx.Rollback()

	// 获取所有道具
	items, err := GetAllItems(ctx, tx, trainer.Id, updateTs)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("获取道具失败: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError(fmt.Sprintf("提交事务失败: %v", err), 500)
	}

	// 构建响应
	response := &MainServer.GetAllItemsResponse{
		Success: true,
		Items:   items,
	}

	return tool.ProtoToBase64(response)
}

// RPC函数：查询道具
func RpcQueryItems(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	// 解析请求参数
	var param MainServer.QueryItemsParam
	if err := tool.Base64ToProto(payload, &param); err != nil {
		logger.Error("解析QueryItemsParam失败: %v", err)
		return "", runtime.NewError("请求参数无效", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("开始事务失败: %v", err), 500)
	}
	defer tx.Rollback()

	// 查询道具
	items, err := QueryItems(ctx, tx, trainer.Id, param.ItemName, param.Status, param.UpdateTs)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("查询道具失败: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError(fmt.Sprintf("提交事务失败: %v", err), 500)
	}

	// 构建响应
	response := &MainServer.QueryItemsResponse{
		Success: true,
		Items:   items,
	}

	return tool.ProtoToBase64(response)
}

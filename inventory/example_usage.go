package inventory

// 这个文件包含了新的 inventory 系统的使用示例
// 注意：这些是示例代码，不会被编译到最终程序中

/*
import (
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"
)

// 示例：完整的道具上架流程
func ExampleSaleItemFlow(ctx context.Context, tx *sql.Tx, trainerId int64) error {
	// 1. 首先添加一些道具到背包
	inventory := &MainServer.Inventory{
		Tid:              trainerId,
		ItemName:         "potion",
		Quantity:         10,
		Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
		ItemSaleType:     MainServer.InventoryItemSaleType_ItemSaleType_Normal,
		SummonPokeNameId: "",
		SealInfo:         &MainServer.InventorySealInfo{},
	}
	err := UpsertItem(ctx, tx, inventory)
	if err != nil {
		return err
	}

	// 2. 查看可上架的道具
	saleableItems, err := GetSaleableItems(ctx, tx, trainerId)
	if err != nil {
		return err
	}
	// saleableItems 现在包含所有可以上架的道具

	// 3. 上架道具
	err = SaleItem(ctx, tx, trainerId, "potion", 5, 100) // 上架5个药水，价格100
	if err != nil {
		return err
	}

	// 4. 查看已上架的道具
	saledItems, err := GetSaledItems(ctx, tx, trainerId)
	if err != nil {
		return err
	}
	// saledItems 现在包含所有已上架的道具

	// 5. 下架部分道具
	err = UnsaleItem(ctx, tx, trainerId, "potion", 2) // 下架2个药水
	if err != nil {
		return err
	}

	return nil
}

// 示例：处理召唤石道具
func ExampleSummonStoneFlow(ctx context.Context, tx *sql.Tx, trainerId int64) error {
	// 1. 添加召唤石道具
	summonStone := &MainServer.Inventory{
		Tid:              trainerId,
		ItemName:         "summon_stone",
		Quantity:         1,
		Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
		ItemSaleType:     MainServer.InventoryItemSaleType_ItemSaleType_Normal,
		SummonPokeNameId: "pikachu", // 召唤皮卡丘的石头
		SealInfo:         &MainServer.InventorySealInfo{},
	}
	err := UpsertItem(ctx, tx, summonStone)
	if err != nil {
		return err
	}

	// 2. 查询特定召唤石
	items, err := QueryItemsExtended(ctx, tx, trainerId, "summon_stone",
		MainServer.InventoryStatus_InventoryStatus_Normal,
		MainServer.InventoryItemSaleType_ItemSaleType_Normal,
		"pikachu", 0, 0)
	if err != nil {
		return err
	}
	// items 现在包含所有召唤皮卡丘的石头

	return nil
}

// 示例：处理封印道具
func ExampleSealedItemFlow(ctx context.Context, tx *sql.Tx, trainerId int64) error {
	// 1. 添加封印道具
	sealedItem := &MainServer.Inventory{
		Tid:              trainerId,
		ItemName:         "sealed_pokeball",
		Quantity:         1,
		Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
		ItemSaleType:     MainServer.InventoryItemSaleType_ItemSaleType_Trainer_Only,
		SummonPokeNameId: "",
		SealInfo: &MainServer.InventorySealInfo{
			PokeId:     12345,
			PokeNameId: "charizard",
		},
	}
	err := UpsertItem(ctx, tx, sealedItem)
	if err != nil {
		return err
	}

	// 2. 查询特定封印道具
	items, err := QueryItemsExtended(ctx, tx, trainerId, "sealed_pokeball",
		MainServer.InventoryStatus_InventoryStatus_Normal,
		MainServer.InventoryItemSaleType_ItemSaleType_Trainer_Only,
		"", 12345, 0)
	if err != nil {
		return err
	}
	// items 现在包含封印了ID为12345的宝可梦的精灵球

	return nil
}

// 示例：批量操作
func ExampleBatchOperations(ctx context.Context, tx *sql.Tx, trainerId int64) error {
	// 1. 批量添加道具
	inventories := []*MainServer.Inventory{
		{
			Tid:              trainerId,
			ItemName:         "potion",
			Quantity:         5,
			Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
			ItemSaleType:     MainServer.InventoryItemSaleType_ItemSaleType_Normal,
			SummonPokeNameId: "",
			SealInfo:         &MainServer.InventorySealInfo{},
		},
		{
			Tid:              trainerId,
			ItemName:         "super_potion",
			Quantity:         3,
			Status:           MainServer.InventoryStatus_InventoryStatus_Normal,
			ItemSaleType:     MainServer.InventoryItemSaleType_ItemSaleType_Normal,
			SummonPokeNameId: "",
			SealInfo:         &MainServer.InventorySealInfo{},
		},
	}

	err := BatchUpsertItems(ctx, tx, inventories)
	if err != nil {
		return err
	}

	// 2. 获取道具总数量
	totalPotions, err := GetItemTotalQuantity(ctx, tx, trainerId, "potion",
		MainServer.InventoryStatus_InventoryStatus_Normal)
	if err != nil {
		return err
	}
	// totalPotions 现在包含该训练师所有普通状态的药水总数

	return nil
}

// 示例：查询操作
func ExampleQueryOperations(ctx context.Context, tx *sql.Tx, trainerId int64) error {
	// 1. 获取所有道具
	allItems, err := GetAllItems(ctx, tx, trainerId, 0)
	if err != nil {
		return err
	}

	// 2. 查询特定状态的道具
	normalItems, err := QueryItems(ctx, tx, trainerId, "",
		MainServer.InventoryStatus_InventoryStatus_Normal, 0)
	if err != nil {
		return err
	}

	// 3. 查询特定道具
	potions, err := QueryItems(ctx, tx, trainerId, "potion",
		MainServer.InventoryStatus_InventoryStatus_Unknown, 0)
	if err != nil {
		return err
	}

	// 4. 扩展查询
	teamItems, err := QueryItemsExtended(ctx, tx, trainerId, "",
		MainServer.InventoryStatus_InventoryStatus_Normal,
		MainServer.InventoryItemSaleType_ItemSaleType_Team_Normal,
		"", 0, 0)
	if err != nil {
		return err
	}

	// 使用查询结果...
	_ = allItems
	_ = normalItems
	_ = potions
	_ = teamItems

	return nil
}
*/

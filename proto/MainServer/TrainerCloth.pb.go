// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerCloth.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerClothType int32

const (
	TrainerClothType_CLOTH_TYPE_OLD_NIN   TrainerClothType = 0
	TrainerClothType_CLOTH_TYPE_HAT       TrainerClothType = 1 // 帽子
	TrainerClothType_CLOTH_TYPE_TOP       TrainerClothType = 2 // 上衣
	TrainerClothType_CLOTH_TYPE_BOTTOM    TrainerClothType = 3 // 下装
	TrainerClothType_CLOTH_TYPE_SHOES     TrainerClothType = 4 // 鞋子
	TrainerClothType_CLOTH_TYPE_ACCESSORY TrainerClothType = 5 // 配饰
)

// Enum value maps for TrainerClothType.
var (
	TrainerClothType_name = map[int32]string{
		0: "CLOTH_TYPE_OLD_NIN",
		1: "CLOTH_TYPE_HAT",
		2: "CLOTH_TYPE_TOP",
		3: "CLOTH_TYPE_BOTTOM",
		4: "CLOTH_TYPE_SHOES",
		5: "CLOTH_TYPE_ACCESSORY",
	}
	TrainerClothType_value = map[string]int32{
		"CLOTH_TYPE_OLD_NIN":   0,
		"CLOTH_TYPE_HAT":       1,
		"CLOTH_TYPE_TOP":       2,
		"CLOTH_TYPE_BOTTOM":    3,
		"CLOTH_TYPE_SHOES":     4,
		"CLOTH_TYPE_ACCESSORY": 5,
	}
)

func (x TrainerClothType) Enum() *TrainerClothType {
	p := new(TrainerClothType)
	*p = x
	return p
}

func (x TrainerClothType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerClothType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerCloth_proto_enumTypes[0].Descriptor()
}

func (TrainerClothType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerCloth_proto_enumTypes[0]
}

func (x TrainerClothType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerClothType.Descriptor instead.
func (TrainerClothType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{0}
}

// 单个训练师服装
type TrainerCloth struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerClothType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	CreateTs      int64                  `protobuf:"varint,3,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"` // 创建时间戳
	UpdateTs      int64                  `protobuf:"varint,4,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerCloth) Reset() {
	*x = TrainerCloth{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerCloth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerCloth) ProtoMessage() {}

func (x *TrainerCloth) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerCloth.ProtoReflect.Descriptor instead.
func (*TrainerCloth) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerCloth) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

func (x *TrainerCloth) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TrainerCloth) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *TrainerCloth) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 训练师服装盒子 (某个品类下的全部服饰)
type TrainerBoxCloth struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Cloths        map[string]*TrainerCloth `protobuf:"bytes,1,rep,name=cloths,proto3" json:"cloths,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // clothId -> TrainerCloth
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerBoxCloth) Reset() {
	*x = TrainerBoxCloth{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerBoxCloth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBoxCloth) ProtoMessage() {}

func (x *TrainerBoxCloth) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBoxCloth.ProtoReflect.Descriptor instead.
func (*TrainerBoxCloth) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerBoxCloth) GetCloths() map[string]*TrainerCloth {
	if x != nil {
		return x.Cloths
	}
	return nil
}

// RPC请求：添加训练师服装
type RpcAddTrainerClothRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerClothType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"` // 服装类型
	ClothId       string                 `protobuf:"bytes,2,opt,name=cloth_id,json=clothId,proto3" json:"cloth_id,omitempty"`              // 服装ID
	Cloth         *TrainerCloth          `protobuf:"bytes,3,opt,name=cloth,proto3" json:"cloth,omitempty"`                                 // 服装数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcAddTrainerClothRequest) Reset() {
	*x = RpcAddTrainerClothRequest{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcAddTrainerClothRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAddTrainerClothRequest) ProtoMessage() {}

func (x *RpcAddTrainerClothRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAddTrainerClothRequest.ProtoReflect.Descriptor instead.
func (*RpcAddTrainerClothRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{2}
}

func (x *RpcAddTrainerClothRequest) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

func (x *RpcAddTrainerClothRequest) GetClothId() string {
	if x != nil {
		return x.ClothId
	}
	return ""
}

func (x *RpcAddTrainerClothRequest) GetCloth() *TrainerCloth {
	if x != nil {
		return x.Cloth
	}
	return nil
}

// RPC响应：添加训练师服装
type RpcAddTrainerClothResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcAddTrainerClothResponse) Reset() {
	*x = RpcAddTrainerClothResponse{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcAddTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcAddTrainerClothResponse) ProtoMessage() {}

func (x *RpcAddTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcAddTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcAddTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{3}
}

func (x *RpcAddTrainerClothResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcAddTrainerClothResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// RPC请求：删除训练师服装
type RpcRemoveTrainerClothRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerClothType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"` // 服装类型
	ClothId       string                 `protobuf:"bytes,2,opt,name=cloth_id,json=clothId,proto3" json:"cloth_id,omitempty"`              // 服装ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcRemoveTrainerClothRequest) Reset() {
	*x = RpcRemoveTrainerClothRequest{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcRemoveTrainerClothRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcRemoveTrainerClothRequest) ProtoMessage() {}

func (x *RpcRemoveTrainerClothRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcRemoveTrainerClothRequest.ProtoReflect.Descriptor instead.
func (*RpcRemoveTrainerClothRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{4}
}

func (x *RpcRemoveTrainerClothRequest) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

func (x *RpcRemoveTrainerClothRequest) GetClothId() string {
	if x != nil {
		return x.ClothId
	}
	return ""
}

// RPC响应：删除训练师服装
type RpcRemoveTrainerClothResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcRemoveTrainerClothResponse) Reset() {
	*x = RpcRemoveTrainerClothResponse{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcRemoveTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcRemoveTrainerClothResponse) ProtoMessage() {}

func (x *RpcRemoveTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcRemoveTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcRemoveTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{5}
}

func (x *RpcRemoveTrainerClothResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcRemoveTrainerClothResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// RPC请求：获取训练师服装盒子
type RpcGetTrainerClothRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerClothType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"` // 服装类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetTrainerClothRequest) Reset() {
	*x = RpcGetTrainerClothRequest{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetTrainerClothRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTrainerClothRequest) ProtoMessage() {}

func (x *RpcGetTrainerClothRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTrainerClothRequest.ProtoReflect.Descriptor instead.
func (*RpcGetTrainerClothRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{6}
}

func (x *RpcGetTrainerClothRequest) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

// RPC响应：获取训练师服装盒子
type RpcGetTrainerClothResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClothBox      *TrainerBoxCloth       `protobuf:"bytes,1,opt,name=cloth_box,json=clothBox,proto3" json:"cloth_box,omitempty"` // 服装盒子数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetTrainerClothResponse) Reset() {
	*x = RpcGetTrainerClothResponse{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTrainerClothResponse) ProtoMessage() {}

func (x *RpcGetTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcGetTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{7}
}

func (x *RpcGetTrainerClothResponse) GetClothBox() *TrainerBoxCloth {
	if x != nil {
		return x.ClothBox
	}
	return nil
}

// RPC响应：获取所有训练师服装盒子
type RpcGetAllTrainerClothResponse struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	ClothBoxes    map[int32]*TrainerBoxCloth `protobuf:"bytes,1,rep,name=cloth_boxes,json=clothBoxes,proto3" json:"cloth_boxes,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 服装盒子映射 (类型 -> 服装盒子)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetAllTrainerClothResponse) Reset() {
	*x = RpcGetAllTrainerClothResponse{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetAllTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetAllTrainerClothResponse) ProtoMessage() {}

func (x *RpcGetAllTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetAllTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcGetAllTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{8}
}

func (x *RpcGetAllTrainerClothResponse) GetClothBoxes() map[int32]*TrainerBoxCloth {
	if x != nil {
		return x.ClothBoxes
	}
	return nil
}

// RPC请求：更新训练师服装
type RpcUpdateTrainerClothRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerClothType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerClothType" json:"type,omitempty"` // 服装类型
	ClothId       string                 `protobuf:"bytes,2,opt,name=cloth_id,json=clothId,proto3" json:"cloth_id,omitempty"`              // 服装ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcUpdateTrainerClothRequest) Reset() {
	*x = RpcUpdateTrainerClothRequest{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcUpdateTrainerClothRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpdateTrainerClothRequest) ProtoMessage() {}

func (x *RpcUpdateTrainerClothRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpdateTrainerClothRequest.ProtoReflect.Descriptor instead.
func (*RpcUpdateTrainerClothRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{9}
}

func (x *RpcUpdateTrainerClothRequest) GetType() TrainerClothType {
	if x != nil {
		return x.Type
	}
	return TrainerClothType_CLOTH_TYPE_OLD_NIN
}

func (x *RpcUpdateTrainerClothRequest) GetClothId() string {
	if x != nil {
		return x.ClothId
	}
	return ""
}

// RPC响应：更新训练师服装
type RpcUpdateTrainerClothResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcUpdateTrainerClothResponse) Reset() {
	*x = RpcUpdateTrainerClothResponse{}
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcUpdateTrainerClothResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcUpdateTrainerClothResponse) ProtoMessage() {}

func (x *RpcUpdateTrainerClothResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerCloth_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcUpdateTrainerClothResponse.ProtoReflect.Descriptor instead.
func (*RpcUpdateTrainerClothResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerCloth_proto_rawDescGZIP(), []int{10}
}

func (x *RpcUpdateTrainerClothResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcUpdateTrainerClothResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_MainServer_TrainerCloth_proto protoreflect.FileDescriptor

const file_MainServer_TrainerCloth_proto_rawDesc = "" +
	"\n" +
	"\x1dMainServer/TrainerCloth.proto\x12\n" +
	"MainServer\"\x8e\x01\n" +
	"\fTrainerCloth\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerClothTypeR\x04type\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tcreate_ts\x18\x03 \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x04 \x01(\x03R\bupdateTs\"\xa7\x01\n" +
	"\x0fTrainerBoxCloth\x12?\n" +
	"\x06cloths\x18\x01 \x03(\v2'.MainServer.TrainerBoxCloth.ClothsEntryR\x06cloths\x1aS\n" +
	"\vClothsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12.\n" +
	"\x05value\x18\x02 \x01(\v2\x18.MainServer.TrainerClothR\x05value:\x028\x01\"\x98\x01\n" +
	"\x19RpcAddTrainerClothRequest\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerClothTypeR\x04type\x12\x19\n" +
	"\bcloth_id\x18\x02 \x01(\tR\aclothId\x12.\n" +
	"\x05cloth\x18\x03 \x01(\v2\x18.MainServer.TrainerClothR\x05cloth\"P\n" +
	"\x1aRpcAddTrainerClothResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"k\n" +
	"\x1cRpcRemoveTrainerClothRequest\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerClothTypeR\x04type\x12\x19\n" +
	"\bcloth_id\x18\x02 \x01(\tR\aclothId\"S\n" +
	"\x1dRpcRemoveTrainerClothResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"M\n" +
	"\x19RpcGetTrainerClothRequest\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerClothTypeR\x04type\"V\n" +
	"\x1aRpcGetTrainerClothResponse\x128\n" +
	"\tcloth_box\x18\x01 \x01(\v2\x1b.MainServer.TrainerBoxClothR\bclothBox\"\xd7\x01\n" +
	"\x1dRpcGetAllTrainerClothResponse\x12Z\n" +
	"\vcloth_boxes\x18\x01 \x03(\v29.MainServer.RpcGetAllTrainerClothResponse.ClothBoxesEntryR\n" +
	"clothBoxes\x1aZ\n" +
	"\x0fClothBoxesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x121\n" +
	"\x05value\x18\x02 \x01(\v2\x1b.MainServer.TrainerBoxClothR\x05value:\x028\x01\"k\n" +
	"\x1cRpcUpdateTrainerClothRequest\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerClothTypeR\x04type\x12\x19\n" +
	"\bcloth_id\x18\x02 \x01(\tR\aclothId\"S\n" +
	"\x1dRpcUpdateTrainerClothResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage*\x99\x01\n" +
	"\x10TrainerClothType\x12\x16\n" +
	"\x12CLOTH_TYPE_OLD_NIN\x10\x00\x12\x12\n" +
	"\x0eCLOTH_TYPE_HAT\x10\x01\x12\x12\n" +
	"\x0eCLOTH_TYPE_TOP\x10\x02\x12\x15\n" +
	"\x11CLOTH_TYPE_BOTTOM\x10\x03\x12\x14\n" +
	"\x10CLOTH_TYPE_SHOES\x10\x04\x12\x18\n" +
	"\x14CLOTH_TYPE_ACCESSORY\x10\x05B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerCloth_proto_rawDescOnce sync.Once
	file_MainServer_TrainerCloth_proto_rawDescData []byte
)

func file_MainServer_TrainerCloth_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerCloth_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerCloth_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerCloth_proto_rawDesc), len(file_MainServer_TrainerCloth_proto_rawDesc)))
	})
	return file_MainServer_TrainerCloth_proto_rawDescData
}

var file_MainServer_TrainerCloth_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerCloth_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_MainServer_TrainerCloth_proto_goTypes = []any{
	(TrainerClothType)(0),                 // 0: MainServer.TrainerClothType
	(*TrainerCloth)(nil),                  // 1: MainServer.TrainerCloth
	(*TrainerBoxCloth)(nil),               // 2: MainServer.TrainerBoxCloth
	(*RpcAddTrainerClothRequest)(nil),     // 3: MainServer.RpcAddTrainerClothRequest
	(*RpcAddTrainerClothResponse)(nil),    // 4: MainServer.RpcAddTrainerClothResponse
	(*RpcRemoveTrainerClothRequest)(nil),  // 5: MainServer.RpcRemoveTrainerClothRequest
	(*RpcRemoveTrainerClothResponse)(nil), // 6: MainServer.RpcRemoveTrainerClothResponse
	(*RpcGetTrainerClothRequest)(nil),     // 7: MainServer.RpcGetTrainerClothRequest
	(*RpcGetTrainerClothResponse)(nil),    // 8: MainServer.RpcGetTrainerClothResponse
	(*RpcGetAllTrainerClothResponse)(nil), // 9: MainServer.RpcGetAllTrainerClothResponse
	(*RpcUpdateTrainerClothRequest)(nil),  // 10: MainServer.RpcUpdateTrainerClothRequest
	(*RpcUpdateTrainerClothResponse)(nil), // 11: MainServer.RpcUpdateTrainerClothResponse
	nil,                                   // 12: MainServer.TrainerBoxCloth.ClothsEntry
	nil,                                   // 13: MainServer.RpcGetAllTrainerClothResponse.ClothBoxesEntry
}
var file_MainServer_TrainerCloth_proto_depIdxs = []int32{
	0,  // 0: MainServer.TrainerCloth.type:type_name -> MainServer.TrainerClothType
	12, // 1: MainServer.TrainerBoxCloth.cloths:type_name -> MainServer.TrainerBoxCloth.ClothsEntry
	0,  // 2: MainServer.RpcAddTrainerClothRequest.type:type_name -> MainServer.TrainerClothType
	1,  // 3: MainServer.RpcAddTrainerClothRequest.cloth:type_name -> MainServer.TrainerCloth
	0,  // 4: MainServer.RpcRemoveTrainerClothRequest.type:type_name -> MainServer.TrainerClothType
	0,  // 5: MainServer.RpcGetTrainerClothRequest.type:type_name -> MainServer.TrainerClothType
	2,  // 6: MainServer.RpcGetTrainerClothResponse.cloth_box:type_name -> MainServer.TrainerBoxCloth
	13, // 7: MainServer.RpcGetAllTrainerClothResponse.cloth_boxes:type_name -> MainServer.RpcGetAllTrainerClothResponse.ClothBoxesEntry
	0,  // 8: MainServer.RpcUpdateTrainerClothRequest.type:type_name -> MainServer.TrainerClothType
	1,  // 9: MainServer.TrainerBoxCloth.ClothsEntry.value:type_name -> MainServer.TrainerCloth
	2,  // 10: MainServer.RpcGetAllTrainerClothResponse.ClothBoxesEntry.value:type_name -> MainServer.TrainerBoxCloth
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerCloth_proto_init() }
func file_MainServer_TrainerCloth_proto_init() {
	if File_MainServer_TrainerCloth_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerCloth_proto_rawDesc), len(file_MainServer_TrainerCloth_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerCloth_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerCloth_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerCloth_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerCloth_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerCloth_proto = out.File
	file_MainServer_TrainerCloth_proto_goTypes = nil
	file_MainServer_TrainerCloth_proto_depIdxs = nil
}

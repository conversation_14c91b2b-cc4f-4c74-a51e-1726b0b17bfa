// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Transaction.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 交易类型枚举
type TransactionType int32

const (
	TransactionType_TransactionType_None             TransactionType = 0
	TransactionType_TransactionType_Market_Sale      TransactionType = 1 // 市场交易
	TransactionType_TransactionType_Market_Rent      TransactionType = 2 // 市场出租
	TransactionType_TransactionType_Market_Borrowing TransactionType = 3 //被借用中
	TransactionType_TransactionType_Purchase         TransactionType = 4 // 购买
	TransactionType_TransactionType_Sale             TransactionType = 5 // 出售
	TransactionType_TransactionType_Use              TransactionType = 6 // 使用
)

// Enum value maps for TransactionType.
var (
	TransactionType_name = map[int32]string{
		0: "TransactionType_None",
		1: "TransactionType_Market_Sale",
		2: "TransactionType_Market_Rent",
		3: "TransactionType_Market_Borrowing",
		4: "TransactionType_Purchase",
		5: "TransactionType_Sale",
		6: "TransactionType_Use",
	}
	TransactionType_value = map[string]int32{
		"TransactionType_None":             0,
		"TransactionType_Market_Sale":      1,
		"TransactionType_Market_Rent":      2,
		"TransactionType_Market_Borrowing": 3,
		"TransactionType_Purchase":         4,
		"TransactionType_Sale":             5,
		"TransactionType_Use":              6,
	}
)

func (x TransactionType) Enum() *TransactionType {
	p := new(TransactionType)
	*p = x
	return p
}

func (x TransactionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Transaction_proto_enumTypes[0].Descriptor()
}

func (TransactionType) Type() protoreflect.EnumType {
	return &file_MainServer_Transaction_proto_enumTypes[0]
}

func (x TransactionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionType.Descriptor instead.
func (TransactionType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{0}
}

// 交易操作类型枚举
type TransactionOperationType int32

const (
	TransactionOperationType_TransactionOperationType_Unknown TransactionOperationType = 0
	TransactionOperationType_TransactionOperationType_List    TransactionOperationType = 1 // 上架
	TransactionOperationType_TransactionOperationType_Delist  TransactionOperationType = 2 // 下架
	TransactionOperationType_TransactionOperationType_Buy     TransactionOperationType = 3 // 购买
	TransactionOperationType_TransactionOperationType_Sell    TransactionOperationType = 4 // 出售
	TransactionOperationType_TransactionOperationType_Use     TransactionOperationType = 5 // 使用
)

// Enum value maps for TransactionOperationType.
var (
	TransactionOperationType_name = map[int32]string{
		0: "TransactionOperationType_Unknown",
		1: "TransactionOperationType_List",
		2: "TransactionOperationType_Delist",
		3: "TransactionOperationType_Buy",
		4: "TransactionOperationType_Sell",
		5: "TransactionOperationType_Use",
	}
	TransactionOperationType_value = map[string]int32{
		"TransactionOperationType_Unknown": 0,
		"TransactionOperationType_List":    1,
		"TransactionOperationType_Delist":  2,
		"TransactionOperationType_Buy":     3,
		"TransactionOperationType_Sell":    4,
		"TransactionOperationType_Use":     5,
	}
)

func (x TransactionOperationType) Enum() *TransactionOperationType {
	p := new(TransactionOperationType)
	*p = x
	return p
}

func (x TransactionOperationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionOperationType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Transaction_proto_enumTypes[1].Descriptor()
}

func (TransactionOperationType) Type() protoreflect.EnumType {
	return &file_MainServer_Transaction_proto_enumTypes[1]
}

func (x TransactionOperationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionOperationType.Descriptor instead.
func (TransactionOperationType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{1}
}

// 交易物品类型枚举
type TransactionItemType int32

const (
	TransactionItemType_TransactionItemType_Unknown TransactionItemType = 0
	TransactionItemType_TransactionItemType_Pokemon TransactionItemType = 1 // 宝可梦
	TransactionItemType_TransactionItemType_Item    TransactionItemType = 2 // 道具
)

// Enum value maps for TransactionItemType.
var (
	TransactionItemType_name = map[int32]string{
		0: "TransactionItemType_Unknown",
		1: "TransactionItemType_Pokemon",
		2: "TransactionItemType_Item",
	}
	TransactionItemType_value = map[string]int32{
		"TransactionItemType_Unknown": 0,
		"TransactionItemType_Pokemon": 1,
		"TransactionItemType_Item":    2,
	}
)

func (x TransactionItemType) Enum() *TransactionItemType {
	p := new(TransactionItemType)
	*p = x
	return p
}

func (x TransactionItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransactionItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Transaction_proto_enumTypes[2].Descriptor()
}

func (TransactionItemType) Type() protoreflect.EnumType {
	return &file_MainServer_Transaction_proto_enumTypes[2]
}

func (x TransactionItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TransactionItemType.Descriptor instead.
func (TransactionItemType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{2}
}

// 交易记录信息
type TransactionRecordInfo struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	Id              int64                    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                     // 记录ID
	Tid             int64                    `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                                                                   // 训练师ID
	TransactionType TransactionType          `protobuf:"varint,3,opt,name=transaction_type,json=transactionType,proto3,enum=MainServer.TransactionType" json:"transaction_type,omitempty"`    // 交易类型
	OperationType   TransactionOperationType `protobuf:"varint,4,opt,name=operation_type,json=operationType,proto3,enum=MainServer.TransactionOperationType" json:"operation_type,omitempty"` // 操作类型
	ItemType        TransactionItemType      `protobuf:"varint,5,opt,name=item_type,json=itemType,proto3,enum=MainServer.TransactionItemType" json:"item_type,omitempty"`                     // 物品类型
	ItemId          string                   `protobuf:"bytes,6,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`                                                                // 物品ID
	ItemName        string                   `protobuf:"bytes,7,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`                                                          // 物品名称
	Quantity        int32                    `protobuf:"varint,8,opt,name=quantity,proto3" json:"quantity,omitempty"`                                                                         // 数量
	Price           int64                    `protobuf:"varint,9,opt,name=price,proto3" json:"price,omitempty"`                                                                               // 价格
	SpecialCoin     int64                    `protobuf:"varint,10,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"`                                               // 特殊货币
	SellerTid       int64                    `protobuf:"varint,11,opt,name=seller_tid,json=sellerTid,proto3" json:"seller_tid,omitempty"`                                                     // 卖家训练师ID
	BuyerTid        int64                    `protobuf:"varint,12,opt,name=buyer_tid,json=buyerTid,proto3" json:"buyer_tid,omitempty"`                                                        // 买家训练师ID
	MarketId        string                   `protobuf:"bytes,13,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`                                                         // 市场交易ID
	ExtraInfo       string                   `protobuf:"bytes,14,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`                                                      // 额外信息(JSON字符串)
	TransactionTs   int64                    `protobuf:"varint,15,opt,name=transaction_ts,json=transactionTs,proto3" json:"transaction_ts,omitempty"`                                         // 交易时间戳
	CreateTs        int64                    `protobuf:"varint,16,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`                                                        // 创建时间戳
	UpdateTs        int64                    `protobuf:"varint,17,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                                        // 更新时间戳
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TransactionRecordInfo) Reset() {
	*x = TransactionRecordInfo{}
	mi := &file_MainServer_Transaction_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionRecordInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionRecordInfo) ProtoMessage() {}

func (x *TransactionRecordInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionRecordInfo.ProtoReflect.Descriptor instead.
func (*TransactionRecordInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{0}
}

func (x *TransactionRecordInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TransactionRecordInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *TransactionRecordInfo) GetTransactionType() TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return TransactionType_TransactionType_None
}

func (x *TransactionRecordInfo) GetOperationType() TransactionOperationType {
	if x != nil {
		return x.OperationType
	}
	return TransactionOperationType_TransactionOperationType_Unknown
}

func (x *TransactionRecordInfo) GetItemType() TransactionItemType {
	if x != nil {
		return x.ItemType
	}
	return TransactionItemType_TransactionItemType_Unknown
}

func (x *TransactionRecordInfo) GetItemId() string {
	if x != nil {
		return x.ItemId
	}
	return ""
}

func (x *TransactionRecordInfo) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *TransactionRecordInfo) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *TransactionRecordInfo) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *TransactionRecordInfo) GetSpecialCoin() int64 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

func (x *TransactionRecordInfo) GetSellerTid() int64 {
	if x != nil {
		return x.SellerTid
	}
	return 0
}

func (x *TransactionRecordInfo) GetBuyerTid() int64 {
	if x != nil {
		return x.BuyerTid
	}
	return 0
}

func (x *TransactionRecordInfo) GetMarketId() string {
	if x != nil {
		return x.MarketId
	}
	return ""
}

func (x *TransactionRecordInfo) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

func (x *TransactionRecordInfo) GetTransactionTs() int64 {
	if x != nil {
		return x.TransactionTs
	}
	return 0
}

func (x *TransactionRecordInfo) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *TransactionRecordInfo) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 交易统计信息
type TransactionStatistics struct {
	state             protoimpl.MessageState                `protogen:"open.v1"`
	Tid               int64                                 `protobuf:"varint,1,opt,name=tid,proto3" json:"tid,omitempty"`                                                                                                       // 训练师ID
	TotalTransactions int32                                 `protobuf:"varint,2,opt,name=total_transactions,json=totalTransactions,proto3" json:"total_transactions,omitempty"`                                                  // 总交易数
	TotalSpent        int64                                 `protobuf:"varint,3,opt,name=total_spent,json=totalSpent,proto3" json:"total_spent,omitempty"`                                                                       // 总支出
	TotalEarned       int64                                 `protobuf:"varint,4,opt,name=total_earned,json=totalEarned,proto3" json:"total_earned,omitempty"`                                                                    // 总收入
	NetAmount         int64                                 `protobuf:"varint,5,opt,name=net_amount,json=netAmount,proto3" json:"net_amount,omitempty"`                                                                          // 净收益
	Days              int32                                 `protobuf:"varint,6,opt,name=days,proto3" json:"days,omitempty"`                                                                                                     // 统计天数
	TypeStats         map[string]*TransactionTypeStatistics `protobuf:"bytes,7,rep,name=type_stats,json=typeStats,proto3" json:"type_stats,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 按类型统计
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TransactionStatistics) Reset() {
	*x = TransactionStatistics{}
	mi := &file_MainServer_Transaction_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionStatistics) ProtoMessage() {}

func (x *TransactionStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionStatistics.ProtoReflect.Descriptor instead.
func (*TransactionStatistics) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{1}
}

func (x *TransactionStatistics) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *TransactionStatistics) GetTotalTransactions() int32 {
	if x != nil {
		return x.TotalTransactions
	}
	return 0
}

func (x *TransactionStatistics) GetTotalSpent() int64 {
	if x != nil {
		return x.TotalSpent
	}
	return 0
}

func (x *TransactionStatistics) GetTotalEarned() int64 {
	if x != nil {
		return x.TotalEarned
	}
	return 0
}

func (x *TransactionStatistics) GetNetAmount() int64 {
	if x != nil {
		return x.NetAmount
	}
	return 0
}

func (x *TransactionStatistics) GetDays() int32 {
	if x != nil {
		return x.Days
	}
	return 0
}

func (x *TransactionStatistics) GetTypeStats() map[string]*TransactionTypeStatistics {
	if x != nil {
		return x.TypeStats
	}
	return nil
}

// 按类型的交易统计
type TransactionTypeStatistics struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Count            int32                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`                                                 // 交易数量
	TotalPrice       int64                  `protobuf:"varint,2,opt,name=total_price,json=totalPrice,proto3" json:"total_price,omitempty"`                     // 总价格
	TotalSpecialCoin int64                  `protobuf:"varint,3,opt,name=total_special_coin,json=totalSpecialCoin,proto3" json:"total_special_coin,omitempty"` // 总特殊货币
	TotalQuantity    int64                  `protobuf:"varint,4,opt,name=total_quantity,json=totalQuantity,proto3" json:"total_quantity,omitempty"`            // 总数量
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TransactionTypeStatistics) Reset() {
	*x = TransactionTypeStatistics{}
	mi := &file_MainServer_Transaction_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionTypeStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionTypeStatistics) ProtoMessage() {}

func (x *TransactionTypeStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionTypeStatistics.ProtoReflect.Descriptor instead.
func (*TransactionTypeStatistics) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{2}
}

func (x *TransactionTypeStatistics) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TransactionTypeStatistics) GetTotalPrice() int64 {
	if x != nil {
		return x.TotalPrice
	}
	return 0
}

func (x *TransactionTypeStatistics) GetTotalSpecialCoin() int64 {
	if x != nil {
		return x.TotalSpecialCoin
	}
	return 0
}

func (x *TransactionTypeStatistics) GetTotalQuantity() int64 {
	if x != nil {
		return x.TotalQuantity
	}
	return 0
}

// 获取交易历史请求
type RpcGetTransactionHistoryRequest struct {
	state           protoimpl.MessageState   `protogen:"open.v1"`
	TransactionType TransactionType          `protobuf:"varint,1,opt,name=transaction_type,json=transactionType,proto3,enum=MainServer.TransactionType" json:"transaction_type,omitempty"`    // 交易类型(可选)
	OperationType   TransactionOperationType `protobuf:"varint,2,opt,name=operation_type,json=operationType,proto3,enum=MainServer.TransactionOperationType" json:"operation_type,omitempty"` // 操作类型(可选)
	ItemType        TransactionItemType      `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3,enum=MainServer.TransactionItemType" json:"item_type,omitempty"`                     // 物品类型(可选)
	Limit           int32                    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`                                                                               // 限制数量
	Offset          int32                    `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`                                                                             // 偏移量
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RpcGetTransactionHistoryRequest) Reset() {
	*x = RpcGetTransactionHistoryRequest{}
	mi := &file_MainServer_Transaction_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetTransactionHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTransactionHistoryRequest) ProtoMessage() {}

func (x *RpcGetTransactionHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTransactionHistoryRequest.ProtoReflect.Descriptor instead.
func (*RpcGetTransactionHistoryRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{3}
}

func (x *RpcGetTransactionHistoryRequest) GetTransactionType() TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return TransactionType_TransactionType_None
}

func (x *RpcGetTransactionHistoryRequest) GetOperationType() TransactionOperationType {
	if x != nil {
		return x.OperationType
	}
	return TransactionOperationType_TransactionOperationType_Unknown
}

func (x *RpcGetTransactionHistoryRequest) GetItemType() TransactionItemType {
	if x != nil {
		return x.ItemType
	}
	return TransactionItemType_TransactionItemType_Unknown
}

func (x *RpcGetTransactionHistoryRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *RpcGetTransactionHistoryRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

// 获取交易历史响应
type RpcGetTransactionHistoryResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Records       []*TransactionRecordInfo `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"` // 交易记录列表
	Count         int32                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`    // 记录数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetTransactionHistoryResponse) Reset() {
	*x = RpcGetTransactionHistoryResponse{}
	mi := &file_MainServer_Transaction_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetTransactionHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTransactionHistoryResponse) ProtoMessage() {}

func (x *RpcGetTransactionHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTransactionHistoryResponse.ProtoReflect.Descriptor instead.
func (*RpcGetTransactionHistoryResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{4}
}

func (x *RpcGetTransactionHistoryResponse) GetRecords() []*TransactionRecordInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *RpcGetTransactionHistoryResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 获取交易统计请求
type RpcGetTransactionStatisticsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Days          int32                  `protobuf:"varint,1,opt,name=days,proto3" json:"days,omitempty"` // 统计天数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetTransactionStatisticsRequest) Reset() {
	*x = RpcGetTransactionStatisticsRequest{}
	mi := &file_MainServer_Transaction_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetTransactionStatisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTransactionStatisticsRequest) ProtoMessage() {}

func (x *RpcGetTransactionStatisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTransactionStatisticsRequest.ProtoReflect.Descriptor instead.
func (*RpcGetTransactionStatisticsRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{5}
}

func (x *RpcGetTransactionStatisticsRequest) GetDays() int32 {
	if x != nil {
		return x.Days
	}
	return 0
}

// 获取交易统计响应
type RpcGetTransactionStatisticsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Statistics    *TransactionStatistics `protobuf:"bytes,1,opt,name=statistics,proto3" json:"statistics,omitempty"` // 统计信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetTransactionStatisticsResponse) Reset() {
	*x = RpcGetTransactionStatisticsResponse{}
	mi := &file_MainServer_Transaction_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetTransactionStatisticsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetTransactionStatisticsResponse) ProtoMessage() {}

func (x *RpcGetTransactionStatisticsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetTransactionStatisticsResponse.ProtoReflect.Descriptor instead.
func (*RpcGetTransactionStatisticsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{6}
}

func (x *RpcGetTransactionStatisticsResponse) GetStatistics() *TransactionStatistics {
	if x != nil {
		return x.Statistics
	}
	return nil
}

// 记录道具使用请求
type RpcRecordItemUsageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemId        string                 `protobuf:"bytes,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`          // 道具ID
	ItemName      string                 `protobuf:"bytes,2,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`    // 道具名称
	Quantity      int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`                   // 使用数量
	ExtraInfo     string                 `protobuf:"bytes,4,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"` // 额外信息(JSON字符串)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcRecordItemUsageRequest) Reset() {
	*x = RpcRecordItemUsageRequest{}
	mi := &file_MainServer_Transaction_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcRecordItemUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcRecordItemUsageRequest) ProtoMessage() {}

func (x *RpcRecordItemUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcRecordItemUsageRequest.ProtoReflect.Descriptor instead.
func (*RpcRecordItemUsageRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{7}
}

func (x *RpcRecordItemUsageRequest) GetItemId() string {
	if x != nil {
		return x.ItemId
	}
	return ""
}

func (x *RpcRecordItemUsageRequest) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *RpcRecordItemUsageRequest) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *RpcRecordItemUsageRequest) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

// 记录道具使用响应
type RpcRecordItemUsageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcRecordItemUsageResponse) Reset() {
	*x = RpcRecordItemUsageResponse{}
	mi := &file_MainServer_Transaction_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcRecordItemUsageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcRecordItemUsageResponse) ProtoMessage() {}

func (x *RpcRecordItemUsageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcRecordItemUsageResponse.ProtoReflect.Descriptor instead.
func (*RpcRecordItemUsageResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{8}
}

func (x *RpcRecordItemUsageResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcRecordItemUsageResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取购买历史请求
type RpcGetPurchaseHistoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemType      TransactionItemType    `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3,enum=MainServer.TransactionItemType" json:"item_type,omitempty"` // 物品类型(可选)
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`                                                           // 限制数量
	Offset        int32                  `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`                                                         // 偏移量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetPurchaseHistoryRequest) Reset() {
	*x = RpcGetPurchaseHistoryRequest{}
	mi := &file_MainServer_Transaction_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetPurchaseHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetPurchaseHistoryRequest) ProtoMessage() {}

func (x *RpcGetPurchaseHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetPurchaseHistoryRequest.ProtoReflect.Descriptor instead.
func (*RpcGetPurchaseHistoryRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{9}
}

func (x *RpcGetPurchaseHistoryRequest) GetItemType() TransactionItemType {
	if x != nil {
		return x.ItemType
	}
	return TransactionItemType_TransactionItemType_Unknown
}

func (x *RpcGetPurchaseHistoryRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *RpcGetPurchaseHistoryRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

// 获取购买历史响应
type RpcGetPurchaseHistoryResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Records       []*TransactionRecordInfo `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"` // 购买记录列表
	Count         int32                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`    // 记录数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcGetPurchaseHistoryResponse) Reset() {
	*x = RpcGetPurchaseHistoryResponse{}
	mi := &file_MainServer_Transaction_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcGetPurchaseHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcGetPurchaseHistoryResponse) ProtoMessage() {}

func (x *RpcGetPurchaseHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcGetPurchaseHistoryResponse.ProtoReflect.Descriptor instead.
func (*RpcGetPurchaseHistoryResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{10}
}

func (x *RpcGetPurchaseHistoryResponse) GetRecords() []*TransactionRecordInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *RpcGetPurchaseHistoryResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 删除交易记录请求
type RpcDeleteTransactionRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecordId      string                 `protobuf:"bytes,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"` // 记录ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcDeleteTransactionRecordRequest) Reset() {
	*x = RpcDeleteTransactionRecordRequest{}
	mi := &file_MainServer_Transaction_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcDeleteTransactionRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcDeleteTransactionRecordRequest) ProtoMessage() {}

func (x *RpcDeleteTransactionRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcDeleteTransactionRecordRequest.ProtoReflect.Descriptor instead.
func (*RpcDeleteTransactionRecordRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{11}
}

func (x *RpcDeleteTransactionRecordRequest) GetRecordId() string {
	if x != nil {
		return x.RecordId
	}
	return ""
}

// 删除交易记录响应
type RpcDeleteTransactionRecordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcDeleteTransactionRecordResponse) Reset() {
	*x = RpcDeleteTransactionRecordResponse{}
	mi := &file_MainServer_Transaction_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcDeleteTransactionRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcDeleteTransactionRecordResponse) ProtoMessage() {}

func (x *RpcDeleteTransactionRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcDeleteTransactionRecordResponse.ProtoReflect.Descriptor instead.
func (*RpcDeleteTransactionRecordResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{12}
}

func (x *RpcDeleteTransactionRecordResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcDeleteTransactionRecordResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 通用响应消息
type TransactionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`                     // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                      // 消息
	ErrorCode     string                 `protobuf:"bytes,3,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"` // 错误代码(可选)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionResponse) Reset() {
	*x = TransactionResponse{}
	mi := &file_MainServer_Transaction_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionResponse) ProtoMessage() {}

func (x *TransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Transaction_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionResponse.ProtoReflect.Descriptor instead.
func (*TransactionResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Transaction_proto_rawDescGZIP(), []int{13}
}

func (x *TransactionResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *TransactionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TransactionResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

var File_MainServer_Transaction_proto protoreflect.FileDescriptor

const file_MainServer_Transaction_proto_rawDesc = "" +
	"\n" +
	"\x1cMainServer/Transaction.proto\x12\n" +
	"MainServer\"\xf0\x04\n" +
	"\x15TransactionRecordInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12F\n" +
	"\x10transaction_type\x18\x03 \x01(\x0e2\x1b.MainServer.TransactionTypeR\x0ftransactionType\x12K\n" +
	"\x0eoperation_type\x18\x04 \x01(\x0e2$.MainServer.TransactionOperationTypeR\roperationType\x12<\n" +
	"\titem_type\x18\x05 \x01(\x0e2\x1f.MainServer.TransactionItemTypeR\bitemType\x12\x17\n" +
	"\aitem_id\x18\x06 \x01(\tR\x06itemId\x12\x1b\n" +
	"\titem_name\x18\a \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\b \x01(\x05R\bquantity\x12\x14\n" +
	"\x05price\x18\t \x01(\x03R\x05price\x12!\n" +
	"\fspecial_coin\x18\n" +
	" \x01(\x03R\vspecialCoin\x12\x1d\n" +
	"\n" +
	"seller_tid\x18\v \x01(\x03R\tsellerTid\x12\x1b\n" +
	"\tbuyer_tid\x18\f \x01(\x03R\bbuyerTid\x12\x1b\n" +
	"\tmarket_id\x18\r \x01(\tR\bmarketId\x12\x1d\n" +
	"\n" +
	"extra_info\x18\x0e \x01(\tR\textraInfo\x12%\n" +
	"\x0etransaction_ts\x18\x0f \x01(\x03R\rtransactionTs\x12\x1b\n" +
	"\tcreate_ts\x18\x10 \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x11 \x01(\x03R\bupdateTs\"\x85\x03\n" +
	"\x15TransactionStatistics\x12\x10\n" +
	"\x03tid\x18\x01 \x01(\x03R\x03tid\x12-\n" +
	"\x12total_transactions\x18\x02 \x01(\x05R\x11totalTransactions\x12\x1f\n" +
	"\vtotal_spent\x18\x03 \x01(\x03R\n" +
	"totalSpent\x12!\n" +
	"\ftotal_earned\x18\x04 \x01(\x03R\vtotalEarned\x12\x1d\n" +
	"\n" +
	"net_amount\x18\x05 \x01(\x03R\tnetAmount\x12\x12\n" +
	"\x04days\x18\x06 \x01(\x05R\x04days\x12O\n" +
	"\n" +
	"type_stats\x18\a \x03(\v20.MainServer.TransactionStatistics.TypeStatsEntryR\ttypeStats\x1ac\n" +
	"\x0eTypeStatsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12;\n" +
	"\x05value\x18\x02 \x01(\v2%.MainServer.TransactionTypeStatisticsR\x05value:\x028\x01\"\xa7\x01\n" +
	"\x19TransactionTypeStatistics\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x05R\x05count\x12\x1f\n" +
	"\vtotal_price\x18\x02 \x01(\x03R\n" +
	"totalPrice\x12,\n" +
	"\x12total_special_coin\x18\x03 \x01(\x03R\x10totalSpecialCoin\x12%\n" +
	"\x0etotal_quantity\x18\x04 \x01(\x03R\rtotalQuantity\"\xa2\x02\n" +
	"\x1fRpcGetTransactionHistoryRequest\x12F\n" +
	"\x10transaction_type\x18\x01 \x01(\x0e2\x1b.MainServer.TransactionTypeR\x0ftransactionType\x12K\n" +
	"\x0eoperation_type\x18\x02 \x01(\x0e2$.MainServer.TransactionOperationTypeR\roperationType\x12<\n" +
	"\titem_type\x18\x03 \x01(\x0e2\x1f.MainServer.TransactionItemTypeR\bitemType\x12\x14\n" +
	"\x05limit\x18\x04 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x05 \x01(\x05R\x06offset\"u\n" +
	" RpcGetTransactionHistoryResponse\x12;\n" +
	"\arecords\x18\x01 \x03(\v2!.MainServer.TransactionRecordInfoR\arecords\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"8\n" +
	"\"RpcGetTransactionStatisticsRequest\x12\x12\n" +
	"\x04days\x18\x01 \x01(\x05R\x04days\"h\n" +
	"#RpcGetTransactionStatisticsResponse\x12A\n" +
	"\n" +
	"statistics\x18\x01 \x01(\v2!.MainServer.TransactionStatisticsR\n" +
	"statistics\"\x8c\x01\n" +
	"\x19RpcRecordItemUsageRequest\x12\x17\n" +
	"\aitem_id\x18\x01 \x01(\tR\x06itemId\x12\x1b\n" +
	"\titem_name\x18\x02 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12\x1d\n" +
	"\n" +
	"extra_info\x18\x04 \x01(\tR\textraInfo\"P\n" +
	"\x1aRpcRecordItemUsageResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x8a\x01\n" +
	"\x1cRpcGetPurchaseHistoryRequest\x12<\n" +
	"\titem_type\x18\x01 \x01(\x0e2\x1f.MainServer.TransactionItemTypeR\bitemType\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x03 \x01(\x05R\x06offset\"r\n" +
	"\x1dRpcGetPurchaseHistoryResponse\x12;\n" +
	"\arecords\x18\x01 \x03(\v2!.MainServer.TransactionRecordInfoR\arecords\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"@\n" +
	"!RpcDeleteTransactionRecordRequest\x12\x1b\n" +
	"\trecord_id\x18\x01 \x01(\tR\brecordId\"X\n" +
	"\"RpcDeleteTransactionRecordResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"h\n" +
	"\x13TransactionResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"error_code\x18\x03 \x01(\tR\terrorCode*\xe4\x01\n" +
	"\x0fTransactionType\x12\x18\n" +
	"\x14TransactionType_None\x10\x00\x12\x1f\n" +
	"\x1bTransactionType_Market_Sale\x10\x01\x12\x1f\n" +
	"\x1bTransactionType_Market_Rent\x10\x02\x12$\n" +
	" TransactionType_Market_Borrowing\x10\x03\x12\x1c\n" +
	"\x18TransactionType_Purchase\x10\x04\x12\x18\n" +
	"\x14TransactionType_Sale\x10\x05\x12\x17\n" +
	"\x13TransactionType_Use\x10\x06*\xef\x01\n" +
	"\x18TransactionOperationType\x12$\n" +
	" TransactionOperationType_Unknown\x10\x00\x12!\n" +
	"\x1dTransactionOperationType_List\x10\x01\x12#\n" +
	"\x1fTransactionOperationType_Delist\x10\x02\x12 \n" +
	"\x1cTransactionOperationType_Buy\x10\x03\x12!\n" +
	"\x1dTransactionOperationType_Sell\x10\x04\x12 \n" +
	"\x1cTransactionOperationType_Use\x10\x05*u\n" +
	"\x13TransactionItemType\x12\x1f\n" +
	"\x1bTransactionItemType_Unknown\x10\x00\x12\x1f\n" +
	"\x1bTransactionItemType_Pokemon\x10\x01\x12\x1c\n" +
	"\x18TransactionItemType_Item\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Transaction_proto_rawDescOnce sync.Once
	file_MainServer_Transaction_proto_rawDescData []byte
)

func file_MainServer_Transaction_proto_rawDescGZIP() []byte {
	file_MainServer_Transaction_proto_rawDescOnce.Do(func() {
		file_MainServer_Transaction_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Transaction_proto_rawDesc), len(file_MainServer_Transaction_proto_rawDesc)))
	})
	return file_MainServer_Transaction_proto_rawDescData
}

var file_MainServer_Transaction_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_MainServer_Transaction_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_MainServer_Transaction_proto_goTypes = []any{
	(TransactionType)(0),                        // 0: MainServer.TransactionType
	(TransactionOperationType)(0),               // 1: MainServer.TransactionOperationType
	(TransactionItemType)(0),                    // 2: MainServer.TransactionItemType
	(*TransactionRecordInfo)(nil),               // 3: MainServer.TransactionRecordInfo
	(*TransactionStatistics)(nil),               // 4: MainServer.TransactionStatistics
	(*TransactionTypeStatistics)(nil),           // 5: MainServer.TransactionTypeStatistics
	(*RpcGetTransactionHistoryRequest)(nil),     // 6: MainServer.RpcGetTransactionHistoryRequest
	(*RpcGetTransactionHistoryResponse)(nil),    // 7: MainServer.RpcGetTransactionHistoryResponse
	(*RpcGetTransactionStatisticsRequest)(nil),  // 8: MainServer.RpcGetTransactionStatisticsRequest
	(*RpcGetTransactionStatisticsResponse)(nil), // 9: MainServer.RpcGetTransactionStatisticsResponse
	(*RpcRecordItemUsageRequest)(nil),           // 10: MainServer.RpcRecordItemUsageRequest
	(*RpcRecordItemUsageResponse)(nil),          // 11: MainServer.RpcRecordItemUsageResponse
	(*RpcGetPurchaseHistoryRequest)(nil),        // 12: MainServer.RpcGetPurchaseHistoryRequest
	(*RpcGetPurchaseHistoryResponse)(nil),       // 13: MainServer.RpcGetPurchaseHistoryResponse
	(*RpcDeleteTransactionRecordRequest)(nil),   // 14: MainServer.RpcDeleteTransactionRecordRequest
	(*RpcDeleteTransactionRecordResponse)(nil),  // 15: MainServer.RpcDeleteTransactionRecordResponse
	(*TransactionResponse)(nil),                 // 16: MainServer.TransactionResponse
	nil,                                         // 17: MainServer.TransactionStatistics.TypeStatsEntry
}
var file_MainServer_Transaction_proto_depIdxs = []int32{
	0,  // 0: MainServer.TransactionRecordInfo.transaction_type:type_name -> MainServer.TransactionType
	1,  // 1: MainServer.TransactionRecordInfo.operation_type:type_name -> MainServer.TransactionOperationType
	2,  // 2: MainServer.TransactionRecordInfo.item_type:type_name -> MainServer.TransactionItemType
	17, // 3: MainServer.TransactionStatistics.type_stats:type_name -> MainServer.TransactionStatistics.TypeStatsEntry
	0,  // 4: MainServer.RpcGetTransactionHistoryRequest.transaction_type:type_name -> MainServer.TransactionType
	1,  // 5: MainServer.RpcGetTransactionHistoryRequest.operation_type:type_name -> MainServer.TransactionOperationType
	2,  // 6: MainServer.RpcGetTransactionHistoryRequest.item_type:type_name -> MainServer.TransactionItemType
	3,  // 7: MainServer.RpcGetTransactionHistoryResponse.records:type_name -> MainServer.TransactionRecordInfo
	4,  // 8: MainServer.RpcGetTransactionStatisticsResponse.statistics:type_name -> MainServer.TransactionStatistics
	2,  // 9: MainServer.RpcGetPurchaseHistoryRequest.item_type:type_name -> MainServer.TransactionItemType
	3,  // 10: MainServer.RpcGetPurchaseHistoryResponse.records:type_name -> MainServer.TransactionRecordInfo
	5,  // 11: MainServer.TransactionStatistics.TypeStatsEntry.value:type_name -> MainServer.TransactionTypeStatistics
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_MainServer_Transaction_proto_init() }
func file_MainServer_Transaction_proto_init() {
	if File_MainServer_Transaction_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Transaction_proto_rawDesc), len(file_MainServer_Transaction_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Transaction_proto_goTypes,
		DependencyIndexes: file_MainServer_Transaction_proto_depIdxs,
		EnumInfos:         file_MainServer_Transaction_proto_enumTypes,
		MessageInfos:      file_MainServer_Transaction_proto_msgTypes,
	}.Build()
	File_MainServer_Transaction_proto = out.File
	file_MainServer_Transaction_proto_goTypes = nil
	file_MainServer_Transaction_proto_depIdxs = nil
}

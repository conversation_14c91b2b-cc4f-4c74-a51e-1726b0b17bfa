// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/ServerUser.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PublicUserStatus 存储公开状态（不包含敏感数据）
//
//	message PublicUserStatus {
//	    string loc = 1;  // 仅公开位置
//	}
type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_MainServer_ServerUser_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerUser_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerUser_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_MainServer_ServerUser_proto protoreflect.FileDescriptor

const file_MainServer_ServerUser_proto_rawDesc = "" +
	"\n" +
	"\x1bMainServer/ServerUser.proto\x12\n" +
	"MainServer\"*\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04nameB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_ServerUser_proto_rawDescOnce sync.Once
	file_MainServer_ServerUser_proto_rawDescData []byte
)

func file_MainServer_ServerUser_proto_rawDescGZIP() []byte {
	file_MainServer_ServerUser_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerUser_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_ServerUser_proto_rawDesc), len(file_MainServer_ServerUser_proto_rawDesc)))
	})
	return file_MainServer_ServerUser_proto_rawDescData
}

var file_MainServer_ServerUser_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_ServerUser_proto_goTypes = []any{
	(*User)(nil), // 0: MainServer.User
}
var file_MainServer_ServerUser_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_MainServer_ServerUser_proto_init() }
func file_MainServer_ServerUser_proto_init() {
	if File_MainServer_ServerUser_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_ServerUser_proto_rawDesc), len(file_MainServer_ServerUser_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerUser_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerUser_proto_depIdxs,
		MessageInfos:      file_MainServer_ServerUser_proto_msgTypes,
	}.Build()
	File_MainServer_ServerUser_proto = out.File
	file_MainServer_ServerUser_proto_goTypes = nil
	file_MainServer_ServerUser_proto_depIdxs = nil
}

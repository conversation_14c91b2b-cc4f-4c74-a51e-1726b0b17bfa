// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/PokeBox.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeBoxType int32

const (
	PokeBoxType_normal        PokeBoxType = 0
	PokeBoxType_hatch         PokeBoxType = 1   //孵化
	PokeBoxType_sale          PokeBoxType = 2   //出售
	PokeBoxType_rent          PokeBoxType = 3   //出租
	PokeBoxType_around        PokeBoxType = 4   //携带 //还未使用
	PokeBoxType_borrow        PokeBoxType = 5   //借用
	PokeBoxType_specialNormal PokeBoxType = 10  //特殊
	PokeBoxType_specialHatch  PokeBoxType = 11  //特殊孵化
	PokeBoxType_specialSale   PokeBoxType = 12  //特殊出售
	PokeBoxType_specialRent   PokeBoxType = 13  //特殊出租
	PokeBoxType_specialAround PokeBoxType = 14  //特殊携带
	PokeBoxType_specialBorrow PokeBoxType = 15  //特殊借用
	PokeBoxType_empty         PokeBoxType = 100 //空
)

// Enum value maps for PokeBoxType.
var (
	PokeBoxType_name = map[int32]string{
		0:   "normal",
		1:   "hatch",
		2:   "sale",
		3:   "rent",
		4:   "around",
		5:   "borrow",
		10:  "specialNormal",
		11:  "specialHatch",
		12:  "specialSale",
		13:  "specialRent",
		14:  "specialAround",
		15:  "specialBorrow",
		100: "empty",
	}
	PokeBoxType_value = map[string]int32{
		"normal":        0,
		"hatch":         1,
		"sale":          2,
		"rent":          3,
		"around":        4,
		"borrow":        5,
		"specialNormal": 10,
		"specialHatch":  11,
		"specialSale":   12,
		"specialRent":   13,
		"specialAround": 14,
		"specialBorrow": 15,
		"empty":         100,
	}
)

func (x PokeBoxType) Enum() *PokeBoxType {
	p := new(PokeBoxType)
	*p = x
	return p
}

func (x PokeBoxType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeBoxType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_PokeBox_proto_enumTypes[0].Descriptor()
}

func (PokeBoxType) Type() protoreflect.EnumType {
	return &file_MainServer_PokeBox_proto_enumTypes[0]
}

func (x PokeBoxType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeBoxType.Descriptor instead.
func (PokeBoxType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{0}
}

type PokeBorrowType int32

const (
	PokeBorrowType_BorrowTypeNone        PokeBorrowType = 0
	PokeBorrowType_BorrowTypeTrainerTeam PokeBorrowType = 1 //向组织租借
	PokeBorrowType_BorrowTypeRent        PokeBorrowType = 2 //向个人租借
)

// Enum value maps for PokeBorrowType.
var (
	PokeBorrowType_name = map[int32]string{
		0: "BorrowTypeNone",
		1: "BorrowTypeTrainerTeam",
		2: "BorrowTypeRent",
	}
	PokeBorrowType_value = map[string]int32{
		"BorrowTypeNone":        0,
		"BorrowTypeTrainerTeam": 1,
		"BorrowTypeRent":        2,
	}
)

func (x PokeBorrowType) Enum() *PokeBorrowType {
	p := new(PokeBorrowType)
	*p = x
	return p
}

func (x PokeBorrowType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeBorrowType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_PokeBox_proto_enumTypes[1].Descriptor()
}

func (PokeBorrowType) Type() protoreflect.EnumType {
	return &file_MainServer_PokeBox_proto_enumTypes[1]
}

func (x PokeBorrowType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeBorrowType.Descriptor instead.
func (PokeBorrowType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{1}
}

type PokeBox struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Id            int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Index         int32                   `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
	Tid           int64                   `protobuf:"varint,3,opt,name=tid,proto3" json:"tid,omitempty"`
	Name          string                  `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Type          PokeBoxType             `protobuf:"varint,5,opt,name=type,proto3,enum=MainServer.PokeBoxType" json:"type,omitempty"`
	Pokes         map[string]*BoxPokeInfo `protobuf:"bytes,6,rep,name=pokes,proto3" json:"pokes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Extra         *PokeBoxExtra           `protobuf:"bytes,7,opt,name=extra,proto3" json:"extra,omitempty"`
	CreateTs      int64                   `protobuf:"varint,8,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	UpdateTs      int64                   `protobuf:"varint,9,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeBox) Reset() {
	*x = PokeBox{}
	mi := &file_MainServer_PokeBox_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBox) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBox) ProtoMessage() {}

func (x *PokeBox) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBox.ProtoReflect.Descriptor instead.
func (*PokeBox) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{0}
}

func (x *PokeBox) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PokeBox) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *PokeBox) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *PokeBox) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PokeBox) GetType() PokeBoxType {
	if x != nil {
		return x.Type
	}
	return PokeBoxType_normal
}

func (x *PokeBox) GetPokes() map[string]*BoxPokeInfo {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *PokeBox) GetExtra() *PokeBoxExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *PokeBox) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *PokeBox) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type BoxPokeInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// PokeBoxType type = 1;
	// int32 loc = 2;
	Id     int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	Around bool  `protobuf:"varint,4,opt,name=around,proto3" json:"around,omitempty"`
	// string name = 4;
	// int32 lv = 5;
	// string item = 6;
	// int32 shiny = 7;
	// bool egg = 8;
	BorrowType    PokeBorrowType `protobuf:"varint,7,opt,name=borrow_type,json=borrowType,proto3,enum=MainServer.PokeBorrowType" json:"borrow_type,omitempty"`
	BorrowTime    int32          `protobuf:"varint,8,opt,name=borrow_time,json=borrowTime,proto3" json:"borrow_time,omitempty"` //租借的时间，
	ValueTs       int32          `protobuf:"varint,9,opt,name=value_ts,json=valueTs,proto3" json:"value_ts,omitempty"`          //孵化的时候可以用来统计 剩余时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BoxPokeInfo) Reset() {
	*x = BoxPokeInfo{}
	mi := &file_MainServer_PokeBox_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BoxPokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoxPokeInfo) ProtoMessage() {}

func (x *BoxPokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoxPokeInfo.ProtoReflect.Descriptor instead.
func (*BoxPokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{1}
}

func (x *BoxPokeInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BoxPokeInfo) GetAround() bool {
	if x != nil {
		return x.Around
	}
	return false
}

func (x *BoxPokeInfo) GetBorrowType() PokeBorrowType {
	if x != nil {
		return x.BorrowType
	}
	return PokeBorrowType_BorrowTypeNone
}

func (x *BoxPokeInfo) GetBorrowTime() int32 {
	if x != nil {
		return x.BorrowTime
	}
	return 0
}

func (x *BoxPokeInfo) GetValueTs() int32 {
	if x != nil {
		return x.ValueTs
	}
	return 0
}

type PokeBoxExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeBoxExtra) Reset() {
	*x = PokeBoxExtra{}
	mi := &file_MainServer_PokeBox_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBoxExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBoxExtra) ProtoMessage() {}

func (x *PokeBoxExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBoxExtra.ProtoReflect.Descriptor instead.
func (*PokeBoxExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{2}
}

type PokeBatchBoxExchange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Exchanges     []*PokeBoxExchange     `protobuf:"bytes,1,rep,name=exchanges,proto3" json:"exchanges,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeBatchBoxExchange) Reset() {
	*x = PokeBatchBoxExchange{}
	mi := &file_MainServer_PokeBox_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBatchBoxExchange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBatchBoxExchange) ProtoMessage() {}

func (x *PokeBatchBoxExchange) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBatchBoxExchange.ProtoReflect.Descriptor instead.
func (*PokeBatchBoxExchange) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{3}
}

func (x *PokeBatchBoxExchange) GetExchanges() []*PokeBoxExchange {
	if x != nil {
		return x.Exchanges
	}
	return nil
}

type PokeBoxExchange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SourceBox     int32                  `protobuf:"varint,1,opt,name=source_box,json=sourceBox,proto3" json:"source_box,omitempty"` //PokeBox.index
	SourceBoxType PokeBoxType            `protobuf:"varint,2,opt,name=source_box_type,json=sourceBoxType,proto3,enum=MainServer.PokeBoxType" json:"source_box_type,omitempty"`
	SourceLoc     int32                  `protobuf:"varint,3,opt,name=source_loc,json=sourceLoc,proto3" json:"source_loc,omitempty"`
	TargetBox     int32                  `protobuf:"varint,4,opt,name=target_box,json=targetBox,proto3" json:"target_box,omitempty"` //PokeBox.index
	TargetBoxType PokeBoxType            `protobuf:"varint,5,opt,name=target_box_type,json=targetBoxType,proto3,enum=MainServer.PokeBoxType" json:"target_box_type,omitempty"`
	TargetLoc     int32                  `protobuf:"varint,6,opt,name=target_loc,json=targetLoc,proto3" json:"target_loc,omitempty"`
	IsDelete      bool                   `protobuf:"varint,7,opt,name=is_delete,json=isDelete,proto3" json:"is_delete,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeBoxExchange) Reset() {
	*x = PokeBoxExchange{}
	mi := &file_MainServer_PokeBox_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBoxExchange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBoxExchange) ProtoMessage() {}

func (x *PokeBoxExchange) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeBox_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBoxExchange.ProtoReflect.Descriptor instead.
func (*PokeBoxExchange) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeBox_proto_rawDescGZIP(), []int{4}
}

func (x *PokeBoxExchange) GetSourceBox() int32 {
	if x != nil {
		return x.SourceBox
	}
	return 0
}

func (x *PokeBoxExchange) GetSourceBoxType() PokeBoxType {
	if x != nil {
		return x.SourceBoxType
	}
	return PokeBoxType_normal
}

func (x *PokeBoxExchange) GetSourceLoc() int32 {
	if x != nil {
		return x.SourceLoc
	}
	return 0
}

func (x *PokeBoxExchange) GetTargetBox() int32 {
	if x != nil {
		return x.TargetBox
	}
	return 0
}

func (x *PokeBoxExchange) GetTargetBoxType() PokeBoxType {
	if x != nil {
		return x.TargetBoxType
	}
	return PokeBoxType_normal
}

func (x *PokeBoxExchange) GetTargetLoc() int32 {
	if x != nil {
		return x.TargetLoc
	}
	return 0
}

func (x *PokeBoxExchange) GetIsDelete() bool {
	if x != nil {
		return x.IsDelete
	}
	return false
}

var File_MainServer_PokeBox_proto protoreflect.FileDescriptor

const file_MainServer_PokeBox_proto_rawDesc = "" +
	"\n" +
	"\x18MainServer/PokeBox.proto\x12\n" +
	"MainServer\"\xf5\x02\n" +
	"\aPokeBox\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05index\x18\x02 \x01(\x05R\x05index\x12\x10\n" +
	"\x03tid\x18\x03 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12+\n" +
	"\x04type\x18\x05 \x01(\x0e2\x17.MainServer.PokeBoxTypeR\x04type\x124\n" +
	"\x05pokes\x18\x06 \x03(\v2\x1e.MainServer.PokeBox.PokesEntryR\x05pokes\x12.\n" +
	"\x05extra\x18\a \x01(\v2\x18.MainServer.PokeBoxExtraR\x05extra\x12\x1b\n" +
	"\tcreate_ts\x18\b \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\t \x01(\x03R\bupdateTs\x1aQ\n" +
	"\n" +
	"PokesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12-\n" +
	"\x05value\x18\x02 \x01(\v2\x17.MainServer.BoxPokeInfoR\x05value:\x028\x01\"\xae\x01\n" +
	"\vBoxPokeInfo\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\x03R\x02id\x12\x16\n" +
	"\x06around\x18\x04 \x01(\bR\x06around\x12;\n" +
	"\vborrow_type\x18\a \x01(\x0e2\x1a.MainServer.PokeBorrowTypeR\n" +
	"borrowType\x12\x1f\n" +
	"\vborrow_time\x18\b \x01(\x05R\n" +
	"borrowTime\x12\x19\n" +
	"\bvalue_ts\x18\t \x01(\x05R\avalueTs\"\x0e\n" +
	"\fPokeBoxExtra\"Q\n" +
	"\x14PokeBatchBoxExchange\x129\n" +
	"\texchanges\x18\x01 \x03(\v2\x1b.MainServer.PokeBoxExchangeR\texchanges\"\xac\x02\n" +
	"\x0fPokeBoxExchange\x12\x1d\n" +
	"\n" +
	"source_box\x18\x01 \x01(\x05R\tsourceBox\x12?\n" +
	"\x0fsource_box_type\x18\x02 \x01(\x0e2\x17.MainServer.PokeBoxTypeR\rsourceBoxType\x12\x1d\n" +
	"\n" +
	"source_loc\x18\x03 \x01(\x05R\tsourceLoc\x12\x1d\n" +
	"\n" +
	"target_box\x18\x04 \x01(\x05R\ttargetBox\x12?\n" +
	"\x0ftarget_box_type\x18\x05 \x01(\x0e2\x17.MainServer.PokeBoxTypeR\rtargetBoxType\x12\x1d\n" +
	"\n" +
	"target_loc\x18\x06 \x01(\x05R\ttargetLoc\x12\x1b\n" +
	"\tis_delete\x18\a \x01(\bR\bisDelete*\xc8\x01\n" +
	"\vPokeBoxType\x12\n" +
	"\n" +
	"\x06normal\x10\x00\x12\t\n" +
	"\x05hatch\x10\x01\x12\b\n" +
	"\x04sale\x10\x02\x12\b\n" +
	"\x04rent\x10\x03\x12\n" +
	"\n" +
	"\x06around\x10\x04\x12\n" +
	"\n" +
	"\x06borrow\x10\x05\x12\x11\n" +
	"\rspecialNormal\x10\n" +
	"\x12\x10\n" +
	"\fspecialHatch\x10\v\x12\x0f\n" +
	"\vspecialSale\x10\f\x12\x0f\n" +
	"\vspecialRent\x10\r\x12\x11\n" +
	"\rspecialAround\x10\x0e\x12\x11\n" +
	"\rspecialBorrow\x10\x0f\x12\t\n" +
	"\x05empty\x10d*S\n" +
	"\x0ePokeBorrowType\x12\x12\n" +
	"\x0eBorrowTypeNone\x10\x00\x12\x19\n" +
	"\x15BorrowTypeTrainerTeam\x10\x01\x12\x12\n" +
	"\x0eBorrowTypeRent\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_PokeBox_proto_rawDescOnce sync.Once
	file_MainServer_PokeBox_proto_rawDescData []byte
)

func file_MainServer_PokeBox_proto_rawDescGZIP() []byte {
	file_MainServer_PokeBox_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeBox_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_PokeBox_proto_rawDesc), len(file_MainServer_PokeBox_proto_rawDesc)))
	})
	return file_MainServer_PokeBox_proto_rawDescData
}

var file_MainServer_PokeBox_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_PokeBox_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_MainServer_PokeBox_proto_goTypes = []any{
	(PokeBoxType)(0),             // 0: MainServer.PokeBoxType
	(PokeBorrowType)(0),          // 1: MainServer.PokeBorrowType
	(*PokeBox)(nil),              // 2: MainServer.PokeBox
	(*BoxPokeInfo)(nil),          // 3: MainServer.BoxPokeInfo
	(*PokeBoxExtra)(nil),         // 4: MainServer.PokeBoxExtra
	(*PokeBatchBoxExchange)(nil), // 5: MainServer.PokeBatchBoxExchange
	(*PokeBoxExchange)(nil),      // 6: MainServer.PokeBoxExchange
	nil,                          // 7: MainServer.PokeBox.PokesEntry
}
var file_MainServer_PokeBox_proto_depIdxs = []int32{
	0, // 0: MainServer.PokeBox.type:type_name -> MainServer.PokeBoxType
	7, // 1: MainServer.PokeBox.pokes:type_name -> MainServer.PokeBox.PokesEntry
	4, // 2: MainServer.PokeBox.extra:type_name -> MainServer.PokeBoxExtra
	1, // 3: MainServer.BoxPokeInfo.borrow_type:type_name -> MainServer.PokeBorrowType
	6, // 4: MainServer.PokeBatchBoxExchange.exchanges:type_name -> MainServer.PokeBoxExchange
	0, // 5: MainServer.PokeBoxExchange.source_box_type:type_name -> MainServer.PokeBoxType
	0, // 6: MainServer.PokeBoxExchange.target_box_type:type_name -> MainServer.PokeBoxType
	3, // 7: MainServer.PokeBox.PokesEntry.value:type_name -> MainServer.BoxPokeInfo
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_MainServer_PokeBox_proto_init() }
func file_MainServer_PokeBox_proto_init() {
	if File_MainServer_PokeBox_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_PokeBox_proto_rawDesc), len(file_MainServer_PokeBox_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeBox_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeBox_proto_depIdxs,
		EnumInfos:         file_MainServer_PokeBox_proto_enumTypes,
		MessageInfos:      file_MainServer_PokeBox_proto_msgTypes,
	}.Build()
	File_MainServer_PokeBox_proto = out.File
	file_MainServer_PokeBox_proto_goTypes = nil
	file_MainServer_PokeBox_proto_depIdxs = nil
}

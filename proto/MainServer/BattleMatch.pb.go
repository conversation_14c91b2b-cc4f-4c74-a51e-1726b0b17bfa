// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/BattleMatch.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BattleMatchMatchStyle int32

const (
	BattleMatchMatchStyle_free BattleMatchMatchStyle = 0
	BattleMatchMatchStyle_rank BattleMatchMatchStyle = 1
	BattleMatchMatchStyle_net  BattleMatchMatchStyle = 2
)

// Enum value maps for BattleMatchMatchStyle.
var (
	BattleMatchMatchStyle_name = map[int32]string{
		0: "free",
		1: "rank",
		2: "net",
	}
	BattleMatchMatchStyle_value = map[string]int32{
		"free": 0,
		"rank": 1,
		"net":  2,
	}
)

func (x BattleMatchMatchStyle) Enum() *BattleMatchMatchStyle {
	p := new(BattleMatchMatchStyle)
	*p = x
	return p
}

func (x BattleMatchMatchStyle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleMatchMatchStyle) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleMatch_proto_enumTypes[0].Descriptor()
}

func (BattleMatchMatchStyle) Type() protoreflect.EnumType {
	return &file_MainServer_BattleMatch_proto_enumTypes[0]
}

func (x BattleMatchMatchStyle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleMatchMatchStyle.Descriptor instead.
func (BattleMatchMatchStyle) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{0}
}

type BattleMatchThreeTrainerType int32

const (
	BattleMatchThreeTrainerType_none   BattleMatchThreeTrainerType = 0
	BattleMatchThreeTrainerType_accept BattleMatchThreeTrainerType = 1
	BattleMatchThreeTrainerType_only   BattleMatchThreeTrainerType = 2
)

// Enum value maps for BattleMatchThreeTrainerType.
var (
	BattleMatchThreeTrainerType_name = map[int32]string{
		0: "none",
		1: "accept",
		2: "only",
	}
	BattleMatchThreeTrainerType_value = map[string]int32{
		"none":   0,
		"accept": 1,
		"only":   2,
	}
)

func (x BattleMatchThreeTrainerType) Enum() *BattleMatchThreeTrainerType {
	p := new(BattleMatchThreeTrainerType)
	*p = x
	return p
}

func (x BattleMatchThreeTrainerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BattleMatchThreeTrainerType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_BattleMatch_proto_enumTypes[1].Descriptor()
}

func (BattleMatchThreeTrainerType) Type() protoreflect.EnumType {
	return &file_MainServer_BattleMatch_proto_enumTypes[1]
}

func (x BattleMatchThreeTrainerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BattleMatchThreeTrainerType.Descriptor instead.
func (BattleMatchThreeTrainerType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{1}
}

type BattleMatchMaker struct {
	state            protoimpl.MessageState      `protogen:"open.v1"`
	MatchStyle       BattleMatchMatchStyle       `protobuf:"varint,1,opt,name=matchStyle,proto3,enum=MainServer.BattleMatchMatchStyle" json:"matchStyle,omitempty"`
	ThreeTrainerType BattleMatchThreeTrainerType `protobuf:"varint,2,opt,name=threeTrainerType,proto3,enum=MainServer.BattleMatchThreeTrainerType" json:"threeTrainerType,omitempty"`
	Label            *BattleMatchLabel           `protobuf:"bytes,3,opt,name=label,proto3" json:"label,omitempty"`
	Name             string                      `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BattleMatchMaker) Reset() {
	*x = BattleMatchMaker{}
	mi := &file_MainServer_BattleMatch_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleMatchMaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleMatchMaker) ProtoMessage() {}

func (x *BattleMatchMaker) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleMatch_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleMatchMaker.ProtoReflect.Descriptor instead.
func (*BattleMatchMaker) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{0}
}

func (x *BattleMatchMaker) GetMatchStyle() BattleMatchMatchStyle {
	if x != nil {
		return x.MatchStyle
	}
	return BattleMatchMatchStyle_free
}

func (x *BattleMatchMaker) GetThreeTrainerType() BattleMatchThreeTrainerType {
	if x != nil {
		return x.ThreeTrainerType
	}
	return BattleMatchThreeTrainerType_none
}

func (x *BattleMatchMaker) GetLabel() *BattleMatchLabel {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *BattleMatchMaker) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BattleMatchInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MatchId       string                 `protobuf:"bytes,1,opt,name=matchId,proto3" json:"matchId,omitempty"`
	Label         *BattleMatchLabel      `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	TrainerCount  int32                  `protobuf:"varint,3,opt,name=trainerCount,proto3" json:"trainerCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BattleMatchInfo) Reset() {
	*x = BattleMatchInfo{}
	mi := &file_MainServer_BattleMatch_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleMatchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleMatchInfo) ProtoMessage() {}

func (x *BattleMatchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleMatch_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleMatchInfo.ProtoReflect.Descriptor instead.
func (*BattleMatchInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{1}
}

func (x *BattleMatchInfo) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *BattleMatchInfo) GetLabel() *BattleMatchLabel {
	if x != nil {
		return x.Label
	}
	return nil
}

func (x *BattleMatchInfo) GetTrainerCount() int32 {
	if x != nil {
		return x.TrainerCount
	}
	return 0
}

type BattleMatchLabel struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Open       bool                   `protobuf:"varint,1,opt,name=open,proto3" json:"open,omitempty"`
	Started    bool                   `protobuf:"varint,2,opt,name=started,proto3" json:"started,omitempty"`
	PartyUpTwo bool                   `protobuf:"varint,3,opt,name=party_up_two,json=partyUpTwo,proto3" json:"party_up_two,omitempty"` //2人以上
	// bool three = 4; //接受3人
	Min              int32                       `protobuf:"varint,4,opt,name=min,proto3" json:"min,omitempty"`
	Max              int32                       `protobuf:"varint,5,opt,name=max,proto3" json:"max,omitempty"`
	MatchStyle       BattleMatchMatchStyle       `protobuf:"varint,6,opt,name=matchStyle,proto3,enum=MainServer.BattleMatchMatchStyle" json:"matchStyle,omitempty"`
	ThreeTrainerType BattleMatchThreeTrainerType `protobuf:"varint,7,opt,name=threeTrainerType,proto3,enum=MainServer.BattleMatchThreeTrainerType" json:"threeTrainerType,omitempty"`
	Name             string                      `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BattleMatchLabel) Reset() {
	*x = BattleMatchLabel{}
	mi := &file_MainServer_BattleMatch_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleMatchLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleMatchLabel) ProtoMessage() {}

func (x *BattleMatchLabel) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleMatch_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleMatchLabel.ProtoReflect.Descriptor instead.
func (*BattleMatchLabel) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{2}
}

func (x *BattleMatchLabel) GetOpen() bool {
	if x != nil {
		return x.Open
	}
	return false
}

func (x *BattleMatchLabel) GetStarted() bool {
	if x != nil {
		return x.Started
	}
	return false
}

func (x *BattleMatchLabel) GetPartyUpTwo() bool {
	if x != nil {
		return x.PartyUpTwo
	}
	return false
}

func (x *BattleMatchLabel) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *BattleMatchLabel) GetMax() int32 {
	if x != nil {
		return x.Max
	}
	return 0
}

func (x *BattleMatchLabel) GetMatchStyle() BattleMatchMatchStyle {
	if x != nil {
		return x.MatchStyle
	}
	return BattleMatchMatchStyle_free
}

func (x *BattleMatchLabel) GetThreeTrainerType() BattleMatchThreeTrainerType {
	if x != nil {
		return x.ThreeTrainerType
	}
	return BattleMatchThreeTrainerType_none
}

func (x *BattleMatchLabel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BattleMatchAiMaker struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ReginId         string                 `protobuf:"bytes,2,opt,name=reginId,proto3" json:"reginId,omitempty"`
	AreaId          string                 `protobuf:"bytes,3,opt,name=areaId,proto3" json:"areaId,omitempty"`
	NpcIds          []string               `protobuf:"bytes,4,rep,name=npcIds,proto3" json:"npcIds,omitempty"`
	EncounterMethod EncounterMethod        `protobuf:"varint,5,opt,name=encounterMethod,proto3,enum=MainServer.EncounterMethod" json:"encounterMethod,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BattleMatchAiMaker) Reset() {
	*x = BattleMatchAiMaker{}
	mi := &file_MainServer_BattleMatch_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattleMatchAiMaker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattleMatchAiMaker) ProtoMessage() {}

func (x *BattleMatchAiMaker) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_BattleMatch_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattleMatchAiMaker.ProtoReflect.Descriptor instead.
func (*BattleMatchAiMaker) Descriptor() ([]byte, []int) {
	return file_MainServer_BattleMatch_proto_rawDescGZIP(), []int{3}
}

func (x *BattleMatchAiMaker) GetReginId() string {
	if x != nil {
		return x.ReginId
	}
	return ""
}

func (x *BattleMatchAiMaker) GetAreaId() string {
	if x != nil {
		return x.AreaId
	}
	return ""
}

func (x *BattleMatchAiMaker) GetNpcIds() []string {
	if x != nil {
		return x.NpcIds
	}
	return nil
}

func (x *BattleMatchAiMaker) GetEncounterMethod() EncounterMethod {
	if x != nil {
		return x.EncounterMethod
	}
	return EncounterMethod_ENCOUNTER_METHOD_UNSPECIFIED
}

var File_MainServer_BattleMatch_proto protoreflect.FileDescriptor

const file_MainServer_BattleMatch_proto_rawDesc = "" +
	"\n" +
	"\x1cMainServer/BattleMatch.proto\x12\n" +
	"MainServer\x1a\x18MainServer/LocInfo.proto\"\xf2\x01\n" +
	"\x10BattleMatchMaker\x12A\n" +
	"\n" +
	"matchStyle\x18\x01 \x01(\x0e2!.MainServer.BattleMatchMatchStyleR\n" +
	"matchStyle\x12S\n" +
	"\x10threeTrainerType\x18\x02 \x01(\x0e2'.MainServer.BattleMatchThreeTrainerTypeR\x10threeTrainerType\x122\n" +
	"\x05label\x18\x03 \x01(\v2\x1c.MainServer.BattleMatchLabelR\x05label\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\"\x83\x01\n" +
	"\x0fBattleMatchInfo\x12\x18\n" +
	"\amatchId\x18\x01 \x01(\tR\amatchId\x122\n" +
	"\x05label\x18\x02 \x01(\v2\x1c.MainServer.BattleMatchLabelR\x05label\x12\"\n" +
	"\ftrainerCount\x18\x03 \x01(\x05R\ftrainerCount\"\xb2\x02\n" +
	"\x10BattleMatchLabel\x12\x12\n" +
	"\x04open\x18\x01 \x01(\bR\x04open\x12\x18\n" +
	"\astarted\x18\x02 \x01(\bR\astarted\x12 \n" +
	"\fparty_up_two\x18\x03 \x01(\bR\n" +
	"partyUpTwo\x12\x10\n" +
	"\x03min\x18\x04 \x01(\x05R\x03min\x12\x10\n" +
	"\x03max\x18\x05 \x01(\x05R\x03max\x12A\n" +
	"\n" +
	"matchStyle\x18\x06 \x01(\x0e2!.MainServer.BattleMatchMatchStyleR\n" +
	"matchStyle\x12S\n" +
	"\x10threeTrainerType\x18\a \x01(\x0e2'.MainServer.BattleMatchThreeTrainerTypeR\x10threeTrainerType\x12\x12\n" +
	"\x04name\x18\b \x01(\tR\x04name\"\xa5\x01\n" +
	"\x12BattleMatchAiMaker\x12\x18\n" +
	"\areginId\x18\x02 \x01(\tR\areginId\x12\x16\n" +
	"\x06areaId\x18\x03 \x01(\tR\x06areaId\x12\x16\n" +
	"\x06npcIds\x18\x04 \x03(\tR\x06npcIds\x12E\n" +
	"\x0fencounterMethod\x18\x05 \x01(\x0e2\x1b.MainServer.EncounterMethodR\x0fencounterMethod*4\n" +
	"\x15BattleMatchMatchStyle\x12\b\n" +
	"\x04free\x10\x00\x12\b\n" +
	"\x04rank\x10\x01\x12\a\n" +
	"\x03net\x10\x02*=\n" +
	"\x1bBattleMatchThreeTrainerType\x12\b\n" +
	"\x04none\x10\x00\x12\n" +
	"\n" +
	"\x06accept\x10\x01\x12\b\n" +
	"\x04only\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_BattleMatch_proto_rawDescOnce sync.Once
	file_MainServer_BattleMatch_proto_rawDescData []byte
)

func file_MainServer_BattleMatch_proto_rawDescGZIP() []byte {
	file_MainServer_BattleMatch_proto_rawDescOnce.Do(func() {
		file_MainServer_BattleMatch_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_BattleMatch_proto_rawDesc), len(file_MainServer_BattleMatch_proto_rawDesc)))
	})
	return file_MainServer_BattleMatch_proto_rawDescData
}

var file_MainServer_BattleMatch_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_BattleMatch_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_BattleMatch_proto_goTypes = []any{
	(BattleMatchMatchStyle)(0),       // 0: MainServer.BattleMatchMatchStyle
	(BattleMatchThreeTrainerType)(0), // 1: MainServer.BattleMatchThreeTrainerType
	(*BattleMatchMaker)(nil),         // 2: MainServer.BattleMatchMaker
	(*BattleMatchInfo)(nil),          // 3: MainServer.BattleMatchInfo
	(*BattleMatchLabel)(nil),         // 4: MainServer.BattleMatchLabel
	(*BattleMatchAiMaker)(nil),       // 5: MainServer.BattleMatchAiMaker
	(EncounterMethod)(0),             // 6: MainServer.EncounterMethod
}
var file_MainServer_BattleMatch_proto_depIdxs = []int32{
	0, // 0: MainServer.BattleMatchMaker.matchStyle:type_name -> MainServer.BattleMatchMatchStyle
	1, // 1: MainServer.BattleMatchMaker.threeTrainerType:type_name -> MainServer.BattleMatchThreeTrainerType
	4, // 2: MainServer.BattleMatchMaker.label:type_name -> MainServer.BattleMatchLabel
	4, // 3: MainServer.BattleMatchInfo.label:type_name -> MainServer.BattleMatchLabel
	0, // 4: MainServer.BattleMatchLabel.matchStyle:type_name -> MainServer.BattleMatchMatchStyle
	1, // 5: MainServer.BattleMatchLabel.threeTrainerType:type_name -> MainServer.BattleMatchThreeTrainerType
	6, // 6: MainServer.BattleMatchAiMaker.encounterMethod:type_name -> MainServer.EncounterMethod
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_MainServer_BattleMatch_proto_init() }
func file_MainServer_BattleMatch_proto_init() {
	if File_MainServer_BattleMatch_proto != nil {
		return
	}
	file_MainServer_LocInfo_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_BattleMatch_proto_rawDesc), len(file_MainServer_BattleMatch_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_BattleMatch_proto_goTypes,
		DependencyIndexes: file_MainServer_BattleMatch_proto_depIdxs,
		EnumInfos:         file_MainServer_BattleMatch_proto_enumTypes,
		MessageInfos:      file_MainServer_BattleMatch_proto_msgTypes,
	}.Build()
	File_MainServer_BattleMatch_proto = out.File
	file_MainServer_BattleMatch_proto_goTypes = nil
	file_MainServer_BattleMatch_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerBadge.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerBadgeType int32

const (
	TrainerBadgeType_TRAINER_BADAGE_NONE TrainerBadgeType = 0
)

// Enum value maps for TrainerBadgeType.
var (
	TrainerBadgeType_name = map[int32]string{
		0: "TRAINER_BADAGE_NONE",
	}
	TrainerBadgeType_value = map[string]int32{
		"TRAINER_BADAGE_NONE": 0,
	}
)

func (x TrainerBadgeType) Enum() *TrainerBadgeType {
	p := new(TrainerBadgeType)
	*p = x
	return p
}

func (x TrainerBadgeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerBadgeType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerBadge_proto_enumTypes[0].Descriptor()
}

func (TrainerBadgeType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerBadge_proto_enumTypes[0]
}

func (x TrainerBadgeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerBadgeType.Descriptor instead.
func (TrainerBadgeType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{0}
}

type TrainerBadges struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Badges        []*TrainerBadge        `protobuf:"bytes,1,rep,name=badges,proto3" json:"badges,omitempty"`
	LastUpdateTs  int64                  `protobuf:"varint,2,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerBadges) Reset() {
	*x = TrainerBadges{}
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerBadges) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBadges) ProtoMessage() {}

func (x *TrainerBadges) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBadges.ProtoReflect.Descriptor instead.
func (*TrainerBadges) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerBadges) GetBadges() []*TrainerBadge {
	if x != nil {
		return x.Badges
	}
	return nil
}

func (x *TrainerBadges) GetLastUpdateTs() int64 {
	if x != nil {
		return x.LastUpdateTs
	}
	return 0
}

type TrainerBadge struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          TrainerBadgeType       `protobuf:"varint,1,opt,name=type,proto3,enum=MainServer.TrainerBadgeType" json:"type,omitempty"`
	CreateTs      int64                  `protobuf:"varint,2,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerBadge) Reset() {
	*x = TrainerBadge{}
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerBadge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerBadge) ProtoMessage() {}

func (x *TrainerBadge) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerBadge_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerBadge.ProtoReflect.Descriptor instead.
func (*TrainerBadge) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerBadge_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerBadge) GetType() TrainerBadgeType {
	if x != nil {
		return x.Type
	}
	return TrainerBadgeType_TRAINER_BADAGE_NONE
}

func (x *TrainerBadge) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

var File_MainServer_TrainerBadge_proto protoreflect.FileDescriptor

const file_MainServer_TrainerBadge_proto_rawDesc = "" +
	"\n" +
	"\x1dMainServer/TrainerBadge.proto\x12\n" +
	"MainServer\"g\n" +
	"\rTrainerBadges\x120\n" +
	"\x06badges\x18\x01 \x03(\v2\x18.MainServer.TrainerBadgeR\x06badges\x12$\n" +
	"\x0elast_update_ts\x18\x02 \x01(\x03R\flastUpdateTs\"]\n" +
	"\fTrainerBadge\x120\n" +
	"\x04type\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerBadgeTypeR\x04type\x12\x1b\n" +
	"\tcreate_ts\x18\x02 \x01(\x03R\bcreateTs*+\n" +
	"\x10TrainerBadgeType\x12\x17\n" +
	"\x13TRAINER_BADAGE_NONE\x10\x00B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerBadge_proto_rawDescOnce sync.Once
	file_MainServer_TrainerBadge_proto_rawDescData []byte
)

func file_MainServer_TrainerBadge_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerBadge_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerBadge_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerBadge_proto_rawDesc), len(file_MainServer_TrainerBadge_proto_rawDesc)))
	})
	return file_MainServer_TrainerBadge_proto_rawDescData
}

var file_MainServer_TrainerBadge_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerBadge_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_TrainerBadge_proto_goTypes = []any{
	(TrainerBadgeType)(0), // 0: MainServer.TrainerBadgeType
	(*TrainerBadges)(nil), // 1: MainServer.TrainerBadges
	(*TrainerBadge)(nil),  // 2: MainServer.TrainerBadge
}
var file_MainServer_TrainerBadge_proto_depIdxs = []int32{
	2, // 0: MainServer.TrainerBadges.badges:type_name -> MainServer.TrainerBadge
	0, // 1: MainServer.TrainerBadge.type:type_name -> MainServer.TrainerBadgeType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerBadge_proto_init() }
func file_MainServer_TrainerBadge_proto_init() {
	if File_MainServer_TrainerBadge_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerBadge_proto_rawDesc), len(file_MainServer_TrainerBadge_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerBadge_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerBadge_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerBadge_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerBadge_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerBadge_proto = out.File
	file_MainServer_TrainerBadge_proto_goTypes = nil
	file_MainServer_TrainerBadge_proto_depIdxs = nil
}

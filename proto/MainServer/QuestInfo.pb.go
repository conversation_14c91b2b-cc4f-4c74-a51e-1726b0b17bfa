// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/QuestInfo.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QuestInfoList struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	QuestInfos   []*QuestInfo           `protobuf:"bytes,1,rep,name=quest_infos,json=questInfos,proto3" json:"quest_infos,omitempty"`
	QuestStricts []*QuestStrictInfo     `protobuf:"bytes,2,rep,name=quest_stricts,json=questStricts,proto3" json:"quest_stricts,omitempty"`
	// repeated QuestUnlockInfo quest_unlocks = 3;
	QuestUnlockMap map[string]*QuestUnlockInfo `protobuf:"bytes,3,rep,name=quest_unlock_map,json=questUnlockMap,proto3" json:"quest_unlock_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// repeated QuestCompleteInfo quest_completes = 4;
	QuestCompleteMap map[string]*QuestCompleteInfo `protobuf:"bytes,4,rep,name=quest_complete_map,json=questCompleteMap,proto3" json:"quest_complete_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// repeated QuestRewardInfo quest_rewards = 5;
	QuestRewardsMap map[string]*RewardInfo `protobuf:"bytes,5,rep,name=quest_rewards_map,json=questRewardsMap,proto3" json:"quest_rewards_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	QuestTypeValues []*QuestTypeValue      `protobuf:"bytes,6,rep,name=quest_type_values,json=questTypeValues,proto3" json:"quest_type_values,omitempty"` // quest值（用于一些特殊的配置)
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *QuestInfoList) Reset() {
	*x = QuestInfoList{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestInfoList) ProtoMessage() {}

func (x *QuestInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestInfoList.ProtoReflect.Descriptor instead.
func (*QuestInfoList) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{0}
}

func (x *QuestInfoList) GetQuestInfos() []*QuestInfo {
	if x != nil {
		return x.QuestInfos
	}
	return nil
}

func (x *QuestInfoList) GetQuestStricts() []*QuestStrictInfo {
	if x != nil {
		return x.QuestStricts
	}
	return nil
}

func (x *QuestInfoList) GetQuestUnlockMap() map[string]*QuestUnlockInfo {
	if x != nil {
		return x.QuestUnlockMap
	}
	return nil
}

func (x *QuestInfoList) GetQuestCompleteMap() map[string]*QuestCompleteInfo {
	if x != nil {
		return x.QuestCompleteMap
	}
	return nil
}

func (x *QuestInfoList) GetQuestRewardsMap() map[string]*RewardInfo {
	if x != nil {
		return x.QuestRewardsMap
	}
	return nil
}

func (x *QuestInfoList) GetQuestTypeValues() []*QuestTypeValue {
	if x != nil {
		return x.QuestTypeValues
	}
	return nil
}

// 这个是任务的描述 训练师接的任务信息请查看TrainerQuestInfo
type QuestInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	QuestId         string                 `protobuf:"bytes,1,opt,name=quest_id,json=questId,proto3" json:"quest_id,omitempty"`                                          // quest id
	QuestType       QuestType              `protobuf:"varint,2,opt,name=quest_type,json=questType,proto3,enum=MainServer.QuestType" json:"quest_type,omitempty"`         // 类型
	QuestLevel      int32                  `protobuf:"varint,3,opt,name=quest_level,json=questLevel,proto3" json:"quest_level,omitempty"`                                // 等级 （难度？）
	QuestStatus     QuestStatus            `protobuf:"varint,4,opt,name=quest_status,json=questStatus,proto3,enum=MainServer.QuestStatus" json:"quest_status,omitempty"` // 状态
	QuestUnlockInfo *QuestUnlockInfo       `protobuf:"bytes,5,opt,name=quest_unlock_info,json=questUnlockInfo,proto3" json:"quest_unlock_info,omitempty"`                //解锁条件
	LinearQuests    *QuestListInfo         `protobuf:"bytes,6,opt,name=linear_quests,json=linearQuests,proto3" json:"linear_quests,omitempty"`                           // 该任务的线性任务id列表
	SingleQuest     bool                   `protobuf:"varint,7,opt,name=single_quest,json=singleQuest,proto3" json:"single_quest,omitempty"`                             // 是否为单任务 //如果是单任务，但是current又有多个则随机 //或者指定（npc选择的时候应该）
	CurrentQuests   *QuestListInfo         `protobuf:"bytes,8,opt,name=current_quests,json=currentQuests,proto3" json:"current_quests,omitempty"`                        // 当前进度要完成的任务列表(非单任务)
	QuestStrict     *QuestStrictInfo       `protobuf:"bytes,9,opt,name=quest_strict,json=questStrict,proto3" json:"quest_strict,omitempty"`                              // 接下这个任务后对玩家的限制
	// QuestRewardInfo quest_reward = 9; // 奖励
	QuestRewardInfo      *RewardInfo         `protobuf:"bytes,10,opt,name=quest_reward_info,json=questRewardInfo,proto3" json:"quest_reward_info,omitempty"`                   // 奖励
	QuestStartTime       int64               `protobuf:"varint,11,opt,name=quest_start_time,json=questStartTime,proto3" json:"quest_start_time,omitempty"`                     // 开始时间(ts 秒级别)
	QuestEndTime         int64               `protobuf:"varint,12,opt,name=quest_end_time,json=questEndTime,proto3" json:"quest_end_time,omitempty"`                           // 结束时间(ts 秒级别)
	QuestRepeatLimit     int32               `protobuf:"varint,13,opt,name=quest_repeat_limit,json=questRepeatLimit,proto3" json:"quest_repeat_limit,omitempty"`               // 重复限制（一天最多只能完成这么多次）
	QuestRepeatInterval  int32               `protobuf:"varint,14,opt,name=quest_repeat_interval,json=questRepeatInterval,proto3" json:"quest_repeat_interval,omitempty"`      // 重复间隔
	QuestBroadcast       *QuestBroadcastInfo `protobuf:"bytes,15,opt,name=quest_broadcast,json=questBroadcast,proto3" json:"quest_broadcast,omitempty"`                        // 广播
	QuestCompleteInfo    *QuestCompleteInfo  `protobuf:"bytes,16,opt,name=quest_complete_info,json=questCompleteInfo,proto3" json:"quest_complete_info,omitempty"`             //完成条件id
	QuestRepeatLimitTime int32               `protobuf:"varint,17,opt,name=quest_repeat_limit_time,json=questRepeatLimitTime,proto3" json:"quest_repeat_limit_time,omitempty"` // 限制时间（训练家接受任务后必须要在这个时间内完成任务）
	Version              int32               `protobuf:"varint,18,opt,name=version,proto3" json:"version,omitempty"`                                                           // 版本号
	QuestCancelInfo      *QuestCancelInfo    `protobuf:"bytes,19,opt,name=quest_cancel_info,json=questCancelInfo,proto3" json:"quest_cancel_info,omitempty"`                   // 取消信息
	QuestDependParentId  string              `protobuf:"bytes,20,opt,name=quest_depend_parent_id,json=questDependParentId,proto3" json:"quest_depend_parent_id,omitempty"`     // 依赖的父任务id(非dbid)
	ParentQuestInfo      *QuestInfo          `protobuf:"bytes,21,opt,name=parent_quest_info,json=parentQuestInfo,proto3" json:"parent_quest_info,omitempty"`                   // 父任务 //不存入QuestInfo数据库表 //序列化的时候会存入jsonb
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *QuestInfo) Reset() {
	*x = QuestInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestInfo) ProtoMessage() {}

func (x *QuestInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestInfo.ProtoReflect.Descriptor instead.
func (*QuestInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{1}
}

func (x *QuestInfo) GetQuestId() string {
	if x != nil {
		return x.QuestId
	}
	return ""
}

func (x *QuestInfo) GetQuestType() QuestType {
	if x != nil {
		return x.QuestType
	}
	return QuestType_QuestType_once
}

func (x *QuestInfo) GetQuestLevel() int32 {
	if x != nil {
		return x.QuestLevel
	}
	return 0
}

func (x *QuestInfo) GetQuestStatus() QuestStatus {
	if x != nil {
		return x.QuestStatus
	}
	return QuestStatus_QuestStatus_close
}

func (x *QuestInfo) GetQuestUnlockInfo() *QuestUnlockInfo {
	if x != nil {
		return x.QuestUnlockInfo
	}
	return nil
}

func (x *QuestInfo) GetLinearQuests() *QuestListInfo {
	if x != nil {
		return x.LinearQuests
	}
	return nil
}

func (x *QuestInfo) GetSingleQuest() bool {
	if x != nil {
		return x.SingleQuest
	}
	return false
}

func (x *QuestInfo) GetCurrentQuests() *QuestListInfo {
	if x != nil {
		return x.CurrentQuests
	}
	return nil
}

func (x *QuestInfo) GetQuestStrict() *QuestStrictInfo {
	if x != nil {
		return x.QuestStrict
	}
	return nil
}

func (x *QuestInfo) GetQuestRewardInfo() *RewardInfo {
	if x != nil {
		return x.QuestRewardInfo
	}
	return nil
}

func (x *QuestInfo) GetQuestStartTime() int64 {
	if x != nil {
		return x.QuestStartTime
	}
	return 0
}

func (x *QuestInfo) GetQuestEndTime() int64 {
	if x != nil {
		return x.QuestEndTime
	}
	return 0
}

func (x *QuestInfo) GetQuestRepeatLimit() int32 {
	if x != nil {
		return x.QuestRepeatLimit
	}
	return 0
}

func (x *QuestInfo) GetQuestRepeatInterval() int32 {
	if x != nil {
		return x.QuestRepeatInterval
	}
	return 0
}

func (x *QuestInfo) GetQuestBroadcast() *QuestBroadcastInfo {
	if x != nil {
		return x.QuestBroadcast
	}
	return nil
}

func (x *QuestInfo) GetQuestCompleteInfo() *QuestCompleteInfo {
	if x != nil {
		return x.QuestCompleteInfo
	}
	return nil
}

func (x *QuestInfo) GetQuestRepeatLimitTime() int32 {
	if x != nil {
		return x.QuestRepeatLimitTime
	}
	return 0
}

func (x *QuestInfo) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *QuestInfo) GetQuestCancelInfo() *QuestCancelInfo {
	if x != nil {
		return x.QuestCancelInfo
	}
	return nil
}

func (x *QuestInfo) GetQuestDependParentId() string {
	if x != nil {
		return x.QuestDependParentId
	}
	return ""
}

func (x *QuestInfo) GetParentQuestInfo() *QuestInfo {
	if x != nil {
		return x.ParentQuestInfo
	}
	return nil
}

type QuestCancelInfo struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	CancelInterval       int32                  `protobuf:"varint,1,opt,name=cancel_interval,json=cancelInterval,proto3" json:"cancel_interval,omitempty"`                     //多久之后这个任务可以取消
	CancelAcceptInterval int32                  `protobuf:"varint,2,opt,name=cancel_accept_interval,json=cancelAcceptInterval,proto3" json:"cancel_accept_interval,omitempty"` // 取消之后过多久才能再次接受任务的间隔
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *QuestCancelInfo) Reset() {
	*x = QuestCancelInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestCancelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestCancelInfo) ProtoMessage() {}

func (x *QuestCancelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestCancelInfo.ProtoReflect.Descriptor instead.
func (*QuestCancelInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{2}
}

func (x *QuestCancelInfo) GetCancelInterval() int32 {
	if x != nil {
		return x.CancelInterval
	}
	return 0
}

func (x *QuestCancelInfo) GetCancelAcceptInterval() int32 {
	if x != nil {
		return x.CancelAcceptInterval
	}
	return 0
}

type QuestListInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HasList       bool                   `protobuf:"varint,1,opt,name=has_list,json=hasList,proto3" json:"has_list,omitempty"`         // 是否有线性任务
	QuestIds      []string               `protobuf:"bytes,2,rep,name=quest_ids,json=questIds,proto3" json:"quest_ids,omitempty"`       // 任务id列表
	QuestInfos    []*QuestInfo           `protobuf:"bytes,3,rep,name=quest_infos,json=questInfos,proto3" json:"quest_infos,omitempty"` // 任务信息列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuestListInfo) Reset() {
	*x = QuestListInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestListInfo) ProtoMessage() {}

func (x *QuestListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestListInfo.ProtoReflect.Descriptor instead.
func (*QuestListInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{3}
}

func (x *QuestListInfo) GetHasList() bool {
	if x != nil {
		return x.HasList
	}
	return false
}

func (x *QuestListInfo) GetQuestIds() []string {
	if x != nil {
		return x.QuestIds
	}
	return nil
}

func (x *QuestListInfo) GetQuestInfos() []*QuestInfo {
	if x != nil {
		return x.QuestInfos
	}
	return nil
}

type QuestStrictInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QuestStrictId string                 `protobuf:"bytes,1,opt,name=quest_strict_id,json=questStrictId,proto3" json:"quest_strict_id,omitempty"` // 限制id
	QuestStricts  []*TrainerStrict       `protobuf:"bytes,2,rep,name=quest_stricts,json=questStricts,proto3" json:"quest_stricts,omitempty"`      // 限制类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuestStrictInfo) Reset() {
	*x = QuestStrictInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestStrictInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestStrictInfo) ProtoMessage() {}

func (x *QuestStrictInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestStrictInfo.ProtoReflect.Descriptor instead.
func (*QuestStrictInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{4}
}

func (x *QuestStrictInfo) GetQuestStrictId() string {
	if x != nil {
		return x.QuestStrictId
	}
	return ""
}

func (x *QuestStrictInfo) GetQuestStricts() []*TrainerStrict {
	if x != nil {
		return x.QuestStricts
	}
	return nil
}

type QuestUnlockInfo struct {
	state                 protoimpl.MessageState      `protogen:"open.v1"`
	QuestUnlockId         string                      `protobuf:"bytes,1,opt,name=quest_unlock_id,json=questUnlockId,proto3" json:"quest_unlock_id,omitempty"`                         // 解锁id 配置表中配置
	QuestUnlockConditions []*QuestUnlockConditionInfo `protobuf:"bytes,2,rep,name=quest_unlock_conditions,json=questUnlockConditions,proto3" json:"quest_unlock_conditions,omitempty"` // 解锁条件
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *QuestUnlockInfo) Reset() {
	*x = QuestUnlockInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestUnlockInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestUnlockInfo) ProtoMessage() {}

func (x *QuestUnlockInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestUnlockInfo.ProtoReflect.Descriptor instead.
func (*QuestUnlockInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{5}
}

func (x *QuestUnlockInfo) GetQuestUnlockId() string {
	if x != nil {
		return x.QuestUnlockId
	}
	return ""
}

func (x *QuestUnlockInfo) GetQuestUnlockConditions() []*QuestUnlockConditionInfo {
	if x != nil {
		return x.QuestUnlockConditions
	}
	return nil
}

type QuestUnlockConditionInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	QuestUnlockType QuestUnlockType        `protobuf:"varint,1,opt,name=quest_unlock_type,json=questUnlockType,proto3,enum=MainServer.QuestUnlockType" json:"quest_unlock_type,omitempty"`
	QuestCondition  *QuestConditionInfo    `protobuf:"bytes,2,opt,name=quest_condition,json=questCondition,proto3" json:"quest_condition,omitempty"` // 解锁条件
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *QuestUnlockConditionInfo) Reset() {
	*x = QuestUnlockConditionInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestUnlockConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestUnlockConditionInfo) ProtoMessage() {}

func (x *QuestUnlockConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestUnlockConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestUnlockConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{6}
}

func (x *QuestUnlockConditionInfo) GetQuestUnlockType() QuestUnlockType {
	if x != nil {
		return x.QuestUnlockType
	}
	return QuestUnlockType_QuestUnlockType_none
}

func (x *QuestUnlockConditionInfo) GetQuestCondition() *QuestConditionInfo {
	if x != nil {
		return x.QuestCondition
	}
	return nil
}

type QuestConditionInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ConditionNameId string                 `protobuf:"bytes,1,opt,name=condition_name_id,json=conditionNameId,proto3" json:"condition_name_id,omitempty"` // 条件名称id(可能是poke的nameid或者item的nameid等等等)
	ConditionCount  int32                  `protobuf:"varint,2,opt,name=condition_count,json=conditionCount,proto3" json:"condition_count,omitempty"`     // 条件数量
	JsonValue       string                 `protobuf:"bytes,3,opt,name=json_value,json=jsonValue,proto3" json:"json_value,omitempty"`                     // json值 (一些特别的配置（）)
	TimeLimit       int32                  `protobuf:"varint,4,opt,name=time_limit,json=timeLimit,proto3" json:"time_limit,omitempty"`                    // 时间限制(秒) //比如任务是要在这个时间范围内的，超出后就无效了
	IsUsed          bool                   `protobuf:"varint,5,opt,name=is_used,json=isUsed,proto3" json:"is_used,omitempty"`                             // 是否使用
	IsAdd           bool                   `protobuf:"varint,6,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty"`                                // 是否为增加条件 (是否给予道具或poke，比如与某poke并肩作战)
	IsRandom        bool                   `protobuf:"varint,7,opt,name=is_random,json=isRandom,proto3" json:"is_random,omitempty"`                       // 是否为随机条件（随机条件会在接受任务的时候，根据random_count取相应数量的json_value中的数据）接受任务的时候使用
	RandomCount     int32                  `protobuf:"varint,8,opt,name=random_count,json=randomCount,proto3" json:"random_count,omitempty"`              //  随机数量 (0或者1)都是数量1 接受任务的时候使用
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *QuestConditionInfo) Reset() {
	*x = QuestConditionInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestConditionInfo) ProtoMessage() {}

func (x *QuestConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{7}
}

func (x *QuestConditionInfo) GetConditionNameId() string {
	if x != nil {
		return x.ConditionNameId
	}
	return ""
}

func (x *QuestConditionInfo) GetConditionCount() int32 {
	if x != nil {
		return x.ConditionCount
	}
	return 0
}

func (x *QuestConditionInfo) GetJsonValue() string {
	if x != nil {
		return x.JsonValue
	}
	return ""
}

func (x *QuestConditionInfo) GetTimeLimit() int32 {
	if x != nil {
		return x.TimeLimit
	}
	return 0
}

func (x *QuestConditionInfo) GetIsUsed() bool {
	if x != nil {
		return x.IsUsed
	}
	return false
}

func (x *QuestConditionInfo) GetIsAdd() bool {
	if x != nil {
		return x.IsAdd
	}
	return false
}

func (x *QuestConditionInfo) GetIsRandom() bool {
	if x != nil {
		return x.IsRandom
	}
	return false
}

func (x *QuestConditionInfo) GetRandomCount() int32 {
	if x != nil {
		return x.RandomCount
	}
	return 0
}

type QuestCompleteConditionInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	QuestCompleteType QuestCompleteType      `protobuf:"varint,1,opt,name=quest_complete_type,json=questCompleteType,proto3,enum=MainServer.QuestCompleteType" json:"quest_complete_type,omitempty"`
	QuestCondition    *QuestConditionInfo    `protobuf:"bytes,2,opt,name=quest_condition,json=questCondition,proto3" json:"quest_condition,omitempty"` // 完成条件
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *QuestCompleteConditionInfo) Reset() {
	*x = QuestCompleteConditionInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestCompleteConditionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestCompleteConditionInfo) ProtoMessage() {}

func (x *QuestCompleteConditionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestCompleteConditionInfo.ProtoReflect.Descriptor instead.
func (*QuestCompleteConditionInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{8}
}

func (x *QuestCompleteConditionInfo) GetQuestCompleteType() QuestCompleteType {
	if x != nil {
		return x.QuestCompleteType
	}
	return QuestCompleteType_QuestCompleteType_none
}

func (x *QuestCompleteConditionInfo) GetQuestCondition() *QuestConditionInfo {
	if x != nil {
		return x.QuestCondition
	}
	return nil
}

type QuestCompleteInfo struct {
	state                   protoimpl.MessageState        `protogen:"open.v1"`
	QuestCompleteId         string                        `protobuf:"bytes,1,opt,name=quest_complete_id,json=questCompleteId,proto3" json:"quest_complete_id,omitempty"`                           // 完成id 配置表中配置
	QuestCompleteConditions []*QuestCompleteConditionInfo `protobuf:"bytes,2,rep,name=quest_complete_conditions,json=questCompleteConditions,proto3" json:"quest_complete_conditions,omitempty"`   // 完成条件
	QuestCompleteReportName string                        `protobuf:"bytes,3,opt,name=quest_complete_report_name,json=questCompleteReportName,proto3" json:"quest_complete_report_name,omitempty"` //完成后汇报的目标
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *QuestCompleteInfo) Reset() {
	*x = QuestCompleteInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestCompleteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestCompleteInfo) ProtoMessage() {}

func (x *QuestCompleteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestCompleteInfo.ProtoReflect.Descriptor instead.
func (*QuestCompleteInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{9}
}

func (x *QuestCompleteInfo) GetQuestCompleteId() string {
	if x != nil {
		return x.QuestCompleteId
	}
	return ""
}

func (x *QuestCompleteInfo) GetQuestCompleteConditions() []*QuestCompleteConditionInfo {
	if x != nil {
		return x.QuestCompleteConditions
	}
	return nil
}

func (x *QuestCompleteInfo) GetQuestCompleteReportName() string {
	if x != nil {
		return x.QuestCompleteReportName
	}
	return ""
}

type QuestBroadcastInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	QuestBroadcastType  QuestBroadcastType     `protobuf:"varint,1,opt,name=quest_broadcast_type,json=questBroadcastType,proto3,enum=MainServer.QuestBroadcastType" json:"quest_broadcast_type,omitempty"` // 广播类型
	QuestBroadcastValue string                 `protobuf:"bytes,2,opt,name=quest_broadcast_value,json=questBroadcastValue,proto3" json:"quest_broadcast_value,omitempty"`                                  // 广播type对应的value (不是广播内容) 比如说什么地区
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *QuestBroadcastInfo) Reset() {
	*x = QuestBroadcastInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestBroadcastInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestBroadcastInfo) ProtoMessage() {}

func (x *QuestBroadcastInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestBroadcastInfo.ProtoReflect.Descriptor instead.
func (*QuestBroadcastInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{10}
}

func (x *QuestBroadcastInfo) GetQuestBroadcastType() QuestBroadcastType {
	if x != nil {
		return x.QuestBroadcastType
	}
	return QuestBroadcastType_QuestBroadcast_none
}

func (x *QuestBroadcastInfo) GetQuestBroadcastValue() string {
	if x != nil {
		return x.QuestBroadcastValue
	}
	return ""
}

type QuestTypeValue struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	QuestTypeValueId       int32                  `protobuf:"varint,1,opt,name=quest_type_value_id,json=questTypeValueId,proto3" json:"quest_type_value_id,omitempty"`                    // quest值id
	QuestTypeValue         int64                  `protobuf:"varint,2,opt,name=quest_type_value,json=questTypeValue,proto3" json:"quest_type_value,omitempty"`                            // quest值
	IsStringValueId        bool                   `protobuf:"varint,3,opt,name=is_string_value_id,json=isStringValueId,proto3" json:"is_string_value_id,omitempty"`                       // 是否为字符串id
	IsStringValue          bool                   `protobuf:"varint,4,opt,name=is_string_value,json=isStringValue,proto3" json:"is_string_value,omitempty"`                               // 是否为字符串value
	QuestTypeValueString   string                 `protobuf:"bytes,5,opt,name=quest_type_value_string,json=questTypeValueString,proto3" json:"quest_type_value_string,omitempty"`         // quest值字符串
	QuestTypeValueStringId string                 `protobuf:"bytes,6,opt,name=quest_type_value_string_id,json=questTypeValueStringId,proto3" json:"quest_type_value_string_id,omitempty"` // quest值字符串id
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *QuestTypeValue) Reset() {
	*x = QuestTypeValue{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuestTypeValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuestTypeValue) ProtoMessage() {}

func (x *QuestTypeValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuestTypeValue.ProtoReflect.Descriptor instead.
func (*QuestTypeValue) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{11}
}

func (x *QuestTypeValue) GetQuestTypeValueId() int32 {
	if x != nil {
		return x.QuestTypeValueId
	}
	return 0
}

func (x *QuestTypeValue) GetQuestTypeValue() int64 {
	if x != nil {
		return x.QuestTypeValue
	}
	return 0
}

func (x *QuestTypeValue) GetIsStringValueId() bool {
	if x != nil {
		return x.IsStringValueId
	}
	return false
}

func (x *QuestTypeValue) GetIsStringValue() bool {
	if x != nil {
		return x.IsStringValue
	}
	return false
}

func (x *QuestTypeValue) GetQuestTypeValueString() string {
	if x != nil {
		return x.QuestTypeValueString
	}
	return ""
}

func (x *QuestTypeValue) GetQuestTypeValueStringId() string {
	if x != nil {
		return x.QuestTypeValueStringId
	}
	return ""
}

// 生成的奖励
type GeneratedReward struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	RewardType     RewardType             `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=MainServer.RewardType" json:"reward_type,omitempty"`
	RewardValueStr string                 `protobuf:"bytes,2,opt,name=reward_value_str,json=rewardValueStr,proto3" json:"reward_value_str,omitempty"`
	Count          int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GeneratedReward) Reset() {
	*x = GeneratedReward{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeneratedReward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratedReward) ProtoMessage() {}

func (x *GeneratedReward) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratedReward.ProtoReflect.Descriptor instead.
func (*GeneratedReward) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{12}
}

func (x *GeneratedReward) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_RewardType_None
}

func (x *GeneratedReward) GetRewardValueStr() string {
	if x != nil {
		return x.RewardValueStr
	}
	return ""
}

func (x *GeneratedReward) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 奖励结果
type RewardResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RewardId      string                 `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`
	BaseMoney     int32                  `protobuf:"varint,2,opt,name=base_money,json=baseMoney,proto3" json:"base_money,omitempty"`
	Rewards       []*GeneratedReward     `protobuf:"bytes,3,rep,name=rewards,proto3" json:"rewards,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RewardResult) Reset() {
	*x = RewardResult{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RewardResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardResult) ProtoMessage() {}

func (x *RewardResult) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardResult.ProtoReflect.Descriptor instead.
func (*RewardResult) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{13}
}

func (x *RewardResult) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *RewardResult) GetBaseMoney() int32 {
	if x != nil {
		return x.BaseMoney
	}
	return 0
}

func (x *RewardResult) GetRewards() []*GeneratedReward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

type RewardInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	RewardId           string                 `protobuf:"bytes,1,opt,name=reward_id,json=rewardId,proto3" json:"reward_id,omitempty"`                                    // 奖励id 可以去配置表中读取
	RewardBaseMinMoney int32                  `protobuf:"varint,2,opt,name=reward_base_min_money,json=rewardBaseMinMoney,proto3" json:"reward_base_min_money,omitempty"` // 最低基础奖励金额
	RewardBaseMaxMoney int32                  `protobuf:"varint,3,opt,name=reward_base_max_money,json=rewardBaseMaxMoney,proto3" json:"reward_base_max_money,omitempty"` // 最高基础奖励金额
	AllRewards         []*RewardValue         `protobuf:"bytes,4,rep,name=all_rewards,json=allRewards,proto3" json:"all_rewards,omitempty"`                              // all组奖励
	OneRewards         []*RewardValue         `protobuf:"bytes,5,rep,name=one_rewards,json=oneRewards,proto3" json:"one_rewards,omitempty"`                              // one组奖励
	TwoRewards         []*RewardValue         `protobuf:"bytes,6,rep,name=two_rewards,json=twoRewards,proto3" json:"two_rewards,omitempty"`                              // two组奖励
	ThreeRewards       []*RewardValue         `protobuf:"bytes,7,rep,name=three_rewards,json=threeRewards,proto3" json:"three_rewards,omitempty"`                        // three组奖励
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RewardInfo) Reset() {
	*x = RewardInfo{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RewardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardInfo) ProtoMessage() {}

func (x *RewardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardInfo.ProtoReflect.Descriptor instead.
func (*RewardInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{14}
}

func (x *RewardInfo) GetRewardId() string {
	if x != nil {
		return x.RewardId
	}
	return ""
}

func (x *RewardInfo) GetRewardBaseMinMoney() int32 {
	if x != nil {
		return x.RewardBaseMinMoney
	}
	return 0
}

func (x *RewardInfo) GetRewardBaseMaxMoney() int32 {
	if x != nil {
		return x.RewardBaseMaxMoney
	}
	return 0
}

func (x *RewardInfo) GetAllRewards() []*RewardValue {
	if x != nil {
		return x.AllRewards
	}
	return nil
}

func (x *RewardInfo) GetOneRewards() []*RewardValue {
	if x != nil {
		return x.OneRewards
	}
	return nil
}

func (x *RewardInfo) GetTwoRewards() []*RewardValue {
	if x != nil {
		return x.TwoRewards
	}
	return nil
}

func (x *RewardInfo) GetThreeRewards() []*RewardValue {
	if x != nil {
		return x.ThreeRewards
	}
	return nil
}

//	message RewardValues {
//	    int32 count = 1; //0代表
//	    repeated RewardValue reward_values = 2;
//	}
type RewardValue struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	RewardType     RewardType             `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=MainServer.RewardType" json:"reward_type,omitempty"` // 奖励类型
	RewardValueStr string                 `protobuf:"bytes,2,opt,name=reward_value_str,json=rewardValueStr,proto3" json:"reward_value_str,omitempty"`
	RewardMinCount int32                  `protobuf:"varint,3,opt,name=reward_min_count,json=rewardMinCount,proto3" json:"reward_min_count,omitempty"` // 最低奖励数量
	RewardMaxCount int32                  `protobuf:"varint,4,opt,name=reward_max_count,json=rewardMaxCount,proto3" json:"reward_max_count,omitempty"` // 最高奖励数量
	DropRate       int32                  `protobuf:"varint,5,opt,name=drop_rate,json=dropRate,proto3" json:"drop_rate,omitempty"`                     // 掉落率 最高100
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RewardValue) Reset() {
	*x = RewardValue{}
	mi := &file_MainServer_QuestInfo_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RewardValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardValue) ProtoMessage() {}

func (x *RewardValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_QuestInfo_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardValue.ProtoReflect.Descriptor instead.
func (*RewardValue) Descriptor() ([]byte, []int) {
	return file_MainServer_QuestInfo_proto_rawDescGZIP(), []int{15}
}

func (x *RewardValue) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_RewardType_None
}

func (x *RewardValue) GetRewardValueStr() string {
	if x != nil {
		return x.RewardValueStr
	}
	return ""
}

func (x *RewardValue) GetRewardMinCount() int32 {
	if x != nil {
		return x.RewardMinCount
	}
	return 0
}

func (x *RewardValue) GetRewardMaxCount() int32 {
	if x != nil {
		return x.RewardMaxCount
	}
	return 0
}

func (x *RewardValue) GetDropRate() int32 {
	if x != nil {
		return x.DropRate
	}
	return 0
}

var File_MainServer_QuestInfo_proto protoreflect.FileDescriptor

const file_MainServer_QuestInfo_proto_rawDesc = "" +
	"\n" +
	"\x1aMainServer/QuestInfo.proto\x12\n" +
	"MainServer\x1a\x1aMainServer/QuestType.proto\x1a\x1eMainServer/TrainerStrict.proto\"\x85\x06\n" +
	"\rQuestInfoList\x126\n" +
	"\vquest_infos\x18\x01 \x03(\v2\x15.MainServer.QuestInfoR\n" +
	"questInfos\x12@\n" +
	"\rquest_stricts\x18\x02 \x03(\v2\x1b.MainServer.QuestStrictInfoR\fquestStricts\x12W\n" +
	"\x10quest_unlock_map\x18\x03 \x03(\v2-.MainServer.QuestInfoList.QuestUnlockMapEntryR\x0equestUnlockMap\x12]\n" +
	"\x12quest_complete_map\x18\x04 \x03(\v2/.MainServer.QuestInfoList.QuestCompleteMapEntryR\x10questCompleteMap\x12Z\n" +
	"\x11quest_rewards_map\x18\x05 \x03(\v2..MainServer.QuestInfoList.QuestRewardsMapEntryR\x0fquestRewardsMap\x12F\n" +
	"\x11quest_type_values\x18\x06 \x03(\v2\x1a.MainServer.QuestTypeValueR\x0fquestTypeValues\x1a^\n" +
	"\x13QuestUnlockMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x121\n" +
	"\x05value\x18\x02 \x01(\v2\x1b.MainServer.QuestUnlockInfoR\x05value:\x028\x01\x1ab\n" +
	"\x15QuestCompleteMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x123\n" +
	"\x05value\x18\x02 \x01(\v2\x1d.MainServer.QuestCompleteInfoR\x05value:\x028\x01\x1aZ\n" +
	"\x14QuestRewardsMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12,\n" +
	"\x05value\x18\x02 \x01(\v2\x16.MainServer.RewardInfoR\x05value:\x028\x01\"\x87\t\n" +
	"\tQuestInfo\x12\x19\n" +
	"\bquest_id\x18\x01 \x01(\tR\aquestId\x124\n" +
	"\n" +
	"quest_type\x18\x02 \x01(\x0e2\x15.MainServer.QuestTypeR\tquestType\x12\x1f\n" +
	"\vquest_level\x18\x03 \x01(\x05R\n" +
	"questLevel\x12:\n" +
	"\fquest_status\x18\x04 \x01(\x0e2\x17.MainServer.QuestStatusR\vquestStatus\x12G\n" +
	"\x11quest_unlock_info\x18\x05 \x01(\v2\x1b.MainServer.QuestUnlockInfoR\x0fquestUnlockInfo\x12>\n" +
	"\rlinear_quests\x18\x06 \x01(\v2\x19.MainServer.QuestListInfoR\flinearQuests\x12!\n" +
	"\fsingle_quest\x18\a \x01(\bR\vsingleQuest\x12@\n" +
	"\x0ecurrent_quests\x18\b \x01(\v2\x19.MainServer.QuestListInfoR\rcurrentQuests\x12>\n" +
	"\fquest_strict\x18\t \x01(\v2\x1b.MainServer.QuestStrictInfoR\vquestStrict\x12B\n" +
	"\x11quest_reward_info\x18\n" +
	" \x01(\v2\x16.MainServer.RewardInfoR\x0fquestRewardInfo\x12(\n" +
	"\x10quest_start_time\x18\v \x01(\x03R\x0equestStartTime\x12$\n" +
	"\x0equest_end_time\x18\f \x01(\x03R\fquestEndTime\x12,\n" +
	"\x12quest_repeat_limit\x18\r \x01(\x05R\x10questRepeatLimit\x122\n" +
	"\x15quest_repeat_interval\x18\x0e \x01(\x05R\x13questRepeatInterval\x12G\n" +
	"\x0fquest_broadcast\x18\x0f \x01(\v2\x1e.MainServer.QuestBroadcastInfoR\x0equestBroadcast\x12M\n" +
	"\x13quest_complete_info\x18\x10 \x01(\v2\x1d.MainServer.QuestCompleteInfoR\x11questCompleteInfo\x125\n" +
	"\x17quest_repeat_limit_time\x18\x11 \x01(\x05R\x14questRepeatLimitTime\x12\x18\n" +
	"\aversion\x18\x12 \x01(\x05R\aversion\x12G\n" +
	"\x11quest_cancel_info\x18\x13 \x01(\v2\x1b.MainServer.QuestCancelInfoR\x0fquestCancelInfo\x123\n" +
	"\x16quest_depend_parent_id\x18\x14 \x01(\tR\x13questDependParentId\x12A\n" +
	"\x11parent_quest_info\x18\x15 \x01(\v2\x15.MainServer.QuestInfoR\x0fparentQuestInfo\"p\n" +
	"\x0fQuestCancelInfo\x12'\n" +
	"\x0fcancel_interval\x18\x01 \x01(\x05R\x0ecancelInterval\x124\n" +
	"\x16cancel_accept_interval\x18\x02 \x01(\x05R\x14cancelAcceptInterval\"\x7f\n" +
	"\rQuestListInfo\x12\x19\n" +
	"\bhas_list\x18\x01 \x01(\bR\ahasList\x12\x1b\n" +
	"\tquest_ids\x18\x02 \x03(\tR\bquestIds\x126\n" +
	"\vquest_infos\x18\x03 \x03(\v2\x15.MainServer.QuestInfoR\n" +
	"questInfos\"y\n" +
	"\x0fQuestStrictInfo\x12&\n" +
	"\x0fquest_strict_id\x18\x01 \x01(\tR\rquestStrictId\x12>\n" +
	"\rquest_stricts\x18\x02 \x03(\v2\x19.MainServer.TrainerStrictR\fquestStricts\"\x97\x01\n" +
	"\x0fQuestUnlockInfo\x12&\n" +
	"\x0fquest_unlock_id\x18\x01 \x01(\tR\rquestUnlockId\x12\\\n" +
	"\x17quest_unlock_conditions\x18\x02 \x03(\v2$.MainServer.QuestUnlockConditionInfoR\x15questUnlockConditions\"\xac\x01\n" +
	"\x18QuestUnlockConditionInfo\x12G\n" +
	"\x11quest_unlock_type\x18\x01 \x01(\x0e2\x1b.MainServer.QuestUnlockTypeR\x0fquestUnlockType\x12G\n" +
	"\x0fquest_condition\x18\x02 \x01(\v2\x1e.MainServer.QuestConditionInfoR\x0equestCondition\"\x97\x02\n" +
	"\x12QuestConditionInfo\x12*\n" +
	"\x11condition_name_id\x18\x01 \x01(\tR\x0fconditionNameId\x12'\n" +
	"\x0fcondition_count\x18\x02 \x01(\x05R\x0econditionCount\x12\x1d\n" +
	"\n" +
	"json_value\x18\x03 \x01(\tR\tjsonValue\x12\x1d\n" +
	"\n" +
	"time_limit\x18\x04 \x01(\x05R\ttimeLimit\x12\x17\n" +
	"\ais_used\x18\x05 \x01(\bR\x06isUsed\x12\x15\n" +
	"\x06is_add\x18\x06 \x01(\bR\x05isAdd\x12\x1b\n" +
	"\tis_random\x18\a \x01(\bR\bisRandom\x12!\n" +
	"\frandom_count\x18\b \x01(\x05R\vrandomCount\"\xb4\x01\n" +
	"\x1aQuestCompleteConditionInfo\x12M\n" +
	"\x13quest_complete_type\x18\x01 \x01(\x0e2\x1d.MainServer.QuestCompleteTypeR\x11questCompleteType\x12G\n" +
	"\x0fquest_condition\x18\x02 \x01(\v2\x1e.MainServer.QuestConditionInfoR\x0equestCondition\"\xe0\x01\n" +
	"\x11QuestCompleteInfo\x12*\n" +
	"\x11quest_complete_id\x18\x01 \x01(\tR\x0fquestCompleteId\x12b\n" +
	"\x19quest_complete_conditions\x18\x02 \x03(\v2&.MainServer.QuestCompleteConditionInfoR\x17questCompleteConditions\x12;\n" +
	"\x1aquest_complete_report_name\x18\x03 \x01(\tR\x17questCompleteReportName\"\x9a\x01\n" +
	"\x12QuestBroadcastInfo\x12P\n" +
	"\x14quest_broadcast_type\x18\x01 \x01(\x0e2\x1e.MainServer.QuestBroadcastTypeR\x12questBroadcastType\x122\n" +
	"\x15quest_broadcast_value\x18\x02 \x01(\tR\x13questBroadcastValue\"\xb1\x02\n" +
	"\x0eQuestTypeValue\x12-\n" +
	"\x13quest_type_value_id\x18\x01 \x01(\x05R\x10questTypeValueId\x12(\n" +
	"\x10quest_type_value\x18\x02 \x01(\x03R\x0equestTypeValue\x12+\n" +
	"\x12is_string_value_id\x18\x03 \x01(\bR\x0fisStringValueId\x12&\n" +
	"\x0fis_string_value\x18\x04 \x01(\bR\risStringValue\x125\n" +
	"\x17quest_type_value_string\x18\x05 \x01(\tR\x14questTypeValueString\x12:\n" +
	"\x1aquest_type_value_string_id\x18\x06 \x01(\tR\x16questTypeValueStringId\"\x8a\x01\n" +
	"\x0fGeneratedReward\x127\n" +
	"\vreward_type\x18\x01 \x01(\x0e2\x16.MainServer.RewardTypeR\n" +
	"rewardType\x12(\n" +
	"\x10reward_value_str\x18\x02 \x01(\tR\x0erewardValueStr\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x05R\x05count\"\x81\x01\n" +
	"\fRewardResult\x12\x1b\n" +
	"\treward_id\x18\x01 \x01(\tR\brewardId\x12\x1d\n" +
	"\n" +
	"base_money\x18\x02 \x01(\x05R\tbaseMoney\x125\n" +
	"\arewards\x18\x03 \x03(\v2\x1b.MainServer.GeneratedRewardR\arewards\"\xfb\x02\n" +
	"\n" +
	"RewardInfo\x12\x1b\n" +
	"\treward_id\x18\x01 \x01(\tR\brewardId\x121\n" +
	"\x15reward_base_min_money\x18\x02 \x01(\x05R\x12rewardBaseMinMoney\x121\n" +
	"\x15reward_base_max_money\x18\x03 \x01(\x05R\x12rewardBaseMaxMoney\x128\n" +
	"\vall_rewards\x18\x04 \x03(\v2\x17.MainServer.RewardValueR\n" +
	"allRewards\x128\n" +
	"\vone_rewards\x18\x05 \x03(\v2\x17.MainServer.RewardValueR\n" +
	"oneRewards\x128\n" +
	"\vtwo_rewards\x18\x06 \x03(\v2\x17.MainServer.RewardValueR\n" +
	"twoRewards\x12<\n" +
	"\rthree_rewards\x18\a \x03(\v2\x17.MainServer.RewardValueR\fthreeRewards\"\xe1\x01\n" +
	"\vRewardValue\x127\n" +
	"\vreward_type\x18\x01 \x01(\x0e2\x16.MainServer.RewardTypeR\n" +
	"rewardType\x12(\n" +
	"\x10reward_value_str\x18\x02 \x01(\tR\x0erewardValueStr\x12(\n" +
	"\x10reward_min_count\x18\x03 \x01(\x05R\x0erewardMinCount\x12(\n" +
	"\x10reward_max_count\x18\x04 \x01(\x05R\x0erewardMaxCount\x12\x1b\n" +
	"\tdrop_rate\x18\x05 \x01(\x05R\bdropRateB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_QuestInfo_proto_rawDescOnce sync.Once
	file_MainServer_QuestInfo_proto_rawDescData []byte
)

func file_MainServer_QuestInfo_proto_rawDescGZIP() []byte {
	file_MainServer_QuestInfo_proto_rawDescOnce.Do(func() {
		file_MainServer_QuestInfo_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_QuestInfo_proto_rawDesc), len(file_MainServer_QuestInfo_proto_rawDesc)))
	})
	return file_MainServer_QuestInfo_proto_rawDescData
}

var file_MainServer_QuestInfo_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_MainServer_QuestInfo_proto_goTypes = []any{
	(*QuestInfoList)(nil),              // 0: MainServer.QuestInfoList
	(*QuestInfo)(nil),                  // 1: MainServer.QuestInfo
	(*QuestCancelInfo)(nil),            // 2: MainServer.QuestCancelInfo
	(*QuestListInfo)(nil),              // 3: MainServer.QuestListInfo
	(*QuestStrictInfo)(nil),            // 4: MainServer.QuestStrictInfo
	(*QuestUnlockInfo)(nil),            // 5: MainServer.QuestUnlockInfo
	(*QuestUnlockConditionInfo)(nil),   // 6: MainServer.QuestUnlockConditionInfo
	(*QuestConditionInfo)(nil),         // 7: MainServer.QuestConditionInfo
	(*QuestCompleteConditionInfo)(nil), // 8: MainServer.QuestCompleteConditionInfo
	(*QuestCompleteInfo)(nil),          // 9: MainServer.QuestCompleteInfo
	(*QuestBroadcastInfo)(nil),         // 10: MainServer.QuestBroadcastInfo
	(*QuestTypeValue)(nil),             // 11: MainServer.QuestTypeValue
	(*GeneratedReward)(nil),            // 12: MainServer.GeneratedReward
	(*RewardResult)(nil),               // 13: MainServer.RewardResult
	(*RewardInfo)(nil),                 // 14: MainServer.RewardInfo
	(*RewardValue)(nil),                // 15: MainServer.RewardValue
	nil,                                // 16: MainServer.QuestInfoList.QuestUnlockMapEntry
	nil,                                // 17: MainServer.QuestInfoList.QuestCompleteMapEntry
	nil,                                // 18: MainServer.QuestInfoList.QuestRewardsMapEntry
	(QuestType)(0),                     // 19: MainServer.QuestType
	(QuestStatus)(0),                   // 20: MainServer.QuestStatus
	(*TrainerStrict)(nil),              // 21: MainServer.TrainerStrict
	(QuestUnlockType)(0),               // 22: MainServer.QuestUnlockType
	(QuestCompleteType)(0),             // 23: MainServer.QuestCompleteType
	(QuestBroadcastType)(0),            // 24: MainServer.QuestBroadcastType
	(RewardType)(0),                    // 25: MainServer.RewardType
}
var file_MainServer_QuestInfo_proto_depIdxs = []int32{
	1,  // 0: MainServer.QuestInfoList.quest_infos:type_name -> MainServer.QuestInfo
	4,  // 1: MainServer.QuestInfoList.quest_stricts:type_name -> MainServer.QuestStrictInfo
	16, // 2: MainServer.QuestInfoList.quest_unlock_map:type_name -> MainServer.QuestInfoList.QuestUnlockMapEntry
	17, // 3: MainServer.QuestInfoList.quest_complete_map:type_name -> MainServer.QuestInfoList.QuestCompleteMapEntry
	18, // 4: MainServer.QuestInfoList.quest_rewards_map:type_name -> MainServer.QuestInfoList.QuestRewardsMapEntry
	11, // 5: MainServer.QuestInfoList.quest_type_values:type_name -> MainServer.QuestTypeValue
	19, // 6: MainServer.QuestInfo.quest_type:type_name -> MainServer.QuestType
	20, // 7: MainServer.QuestInfo.quest_status:type_name -> MainServer.QuestStatus
	5,  // 8: MainServer.QuestInfo.quest_unlock_info:type_name -> MainServer.QuestUnlockInfo
	3,  // 9: MainServer.QuestInfo.linear_quests:type_name -> MainServer.QuestListInfo
	3,  // 10: MainServer.QuestInfo.current_quests:type_name -> MainServer.QuestListInfo
	4,  // 11: MainServer.QuestInfo.quest_strict:type_name -> MainServer.QuestStrictInfo
	14, // 12: MainServer.QuestInfo.quest_reward_info:type_name -> MainServer.RewardInfo
	10, // 13: MainServer.QuestInfo.quest_broadcast:type_name -> MainServer.QuestBroadcastInfo
	9,  // 14: MainServer.QuestInfo.quest_complete_info:type_name -> MainServer.QuestCompleteInfo
	2,  // 15: MainServer.QuestInfo.quest_cancel_info:type_name -> MainServer.QuestCancelInfo
	1,  // 16: MainServer.QuestInfo.parent_quest_info:type_name -> MainServer.QuestInfo
	1,  // 17: MainServer.QuestListInfo.quest_infos:type_name -> MainServer.QuestInfo
	21, // 18: MainServer.QuestStrictInfo.quest_stricts:type_name -> MainServer.TrainerStrict
	6,  // 19: MainServer.QuestUnlockInfo.quest_unlock_conditions:type_name -> MainServer.QuestUnlockConditionInfo
	22, // 20: MainServer.QuestUnlockConditionInfo.quest_unlock_type:type_name -> MainServer.QuestUnlockType
	7,  // 21: MainServer.QuestUnlockConditionInfo.quest_condition:type_name -> MainServer.QuestConditionInfo
	23, // 22: MainServer.QuestCompleteConditionInfo.quest_complete_type:type_name -> MainServer.QuestCompleteType
	7,  // 23: MainServer.QuestCompleteConditionInfo.quest_condition:type_name -> MainServer.QuestConditionInfo
	8,  // 24: MainServer.QuestCompleteInfo.quest_complete_conditions:type_name -> MainServer.QuestCompleteConditionInfo
	24, // 25: MainServer.QuestBroadcastInfo.quest_broadcast_type:type_name -> MainServer.QuestBroadcastType
	25, // 26: MainServer.GeneratedReward.reward_type:type_name -> MainServer.RewardType
	12, // 27: MainServer.RewardResult.rewards:type_name -> MainServer.GeneratedReward
	15, // 28: MainServer.RewardInfo.all_rewards:type_name -> MainServer.RewardValue
	15, // 29: MainServer.RewardInfo.one_rewards:type_name -> MainServer.RewardValue
	15, // 30: MainServer.RewardInfo.two_rewards:type_name -> MainServer.RewardValue
	15, // 31: MainServer.RewardInfo.three_rewards:type_name -> MainServer.RewardValue
	25, // 32: MainServer.RewardValue.reward_type:type_name -> MainServer.RewardType
	5,  // 33: MainServer.QuestInfoList.QuestUnlockMapEntry.value:type_name -> MainServer.QuestUnlockInfo
	9,  // 34: MainServer.QuestInfoList.QuestCompleteMapEntry.value:type_name -> MainServer.QuestCompleteInfo
	14, // 35: MainServer.QuestInfoList.QuestRewardsMapEntry.value:type_name -> MainServer.RewardInfo
	36, // [36:36] is the sub-list for method output_type
	36, // [36:36] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_MainServer_QuestInfo_proto_init() }
func file_MainServer_QuestInfo_proto_init() {
	if File_MainServer_QuestInfo_proto != nil {
		return
	}
	file_MainServer_QuestType_proto_init()
	file_MainServer_TrainerStrict_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_QuestInfo_proto_rawDesc), len(file_MainServer_QuestInfo_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_QuestInfo_proto_goTypes,
		DependencyIndexes: file_MainServer_QuestInfo_proto_depIdxs,
		MessageInfos:      file_MainServer_QuestInfo_proto_msgTypes,
	}.Build()
	File_MainServer_QuestInfo_proto = out.File
	file_MainServer_QuestInfo_proto_goTypes = nil
	file_MainServer_QuestInfo_proto_depIdxs = nil
}

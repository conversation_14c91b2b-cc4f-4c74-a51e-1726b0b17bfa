// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/ChatMessage.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 枚举：消息类型
type ChatMessageType int32

const (
	ChatMessageType_TEXT  ChatMessageType = 0
	ChatMessageType_IMAGE ChatMessageType = 1
	ChatMessageType_VIDEO ChatMessageType = 2
)

// Enum value maps for ChatMessageType.
var (
	ChatMessageType_name = map[int32]string{
		0: "TEXT",
		1: "IMAGE",
		2: "VIDEO",
	}
	ChatMessageType_value = map[string]int32{
		"TEXT":  0,
		"IMAGE": 1,
		"VIDEO": 2,
	}
)

func (x ChatMessageType) Enum() *ChatMessageType {
	p := new(ChatMessageType)
	*p = x
	return p
}

func (x ChatMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ChatMessage_proto_enumTypes[0].Descriptor()
}

func (ChatMessageType) Type() protoreflect.EnumType {
	return &file_MainServer_ChatMessage_proto_enumTypes[0]
}

func (x ChatMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatMessageType.Descriptor instead.
func (ChatMessageType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{0}
}

// 消息作者定义
type ChatMessageAuthor struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Icon          string                 `protobuf:"bytes,2,opt,name=icon,proto3" json:"icon,omitempty"`
	Tid           int64                  `protobuf:"varint,3,opt,name=tid,proto3" json:"tid,omitempty"` // 注意：isSelf 是派生字段，不应在 protobuf 中定义，因为它是运行时计算得出的。
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatMessageAuthor) Reset() {
	*x = ChatMessageAuthor{}
	mi := &file_MainServer_ChatMessage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessageAuthor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessageAuthor) ProtoMessage() {}

func (x *ChatMessageAuthor) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ChatMessage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessageAuthor.ProtoReflect.Descriptor instead.
func (*ChatMessageAuthor) Descriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{0}
}

func (x *ChatMessageAuthor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChatMessageAuthor) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *ChatMessageAuthor) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

// 聊天消息定义
type ChatMessageModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	Time          string                 `protobuf:"bytes,2,opt,name=time,proto3" json:"time,omitempty"`
	Type          ChatMessageType        `protobuf:"varint,3,opt,name=type,proto3,enum=ChatMessage.ChatMessageType" json:"type,omitempty"` // 默认值为 TEXT，可在服务端/客户端处理
	Author        *ChatMessageAuthor     `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatMessageModel) Reset() {
	*x = ChatMessageModel{}
	mi := &file_MainServer_ChatMessage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessageModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessageModel) ProtoMessage() {}

func (x *ChatMessageModel) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ChatMessage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessageModel.ProtoReflect.Descriptor instead.
func (*ChatMessageModel) Descriptor() ([]byte, []int) {
	return file_MainServer_ChatMessage_proto_rawDescGZIP(), []int{1}
}

func (x *ChatMessageModel) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *ChatMessageModel) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *ChatMessageModel) GetType() ChatMessageType {
	if x != nil {
		return x.Type
	}
	return ChatMessageType_TEXT
}

func (x *ChatMessageModel) GetAuthor() *ChatMessageAuthor {
	if x != nil {
		return x.Author
	}
	return nil
}

var File_MainServer_ChatMessage_proto protoreflect.FileDescriptor

const file_MainServer_ChatMessage_proto_rawDesc = "" +
	"\n" +
	"\x1cMainServer/ChatMessage.proto\x12\vChatMessage\"M\n" +
	"\x11ChatMessageAuthor\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04icon\x18\x02 \x01(\tR\x04icon\x12\x10\n" +
	"\x03tid\x18\x03 \x01(\x03R\x03tid\"\xa4\x01\n" +
	"\x10ChatMessageModel\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x12\n" +
	"\x04time\x18\x02 \x01(\tR\x04time\x120\n" +
	"\x04type\x18\x03 \x01(\x0e2\x1c.ChatMessage.ChatMessageTypeR\x04type\x126\n" +
	"\x06author\x18\x04 \x01(\v2\x1e.ChatMessage.ChatMessageAuthorR\x06author*1\n" +
	"\x0fChatMessageType\x12\b\n" +
	"\x04TEXT\x10\x00\x12\t\n" +
	"\x05IMAGE\x10\x01\x12\t\n" +
	"\x05VIDEO\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_ChatMessage_proto_rawDescOnce sync.Once
	file_MainServer_ChatMessage_proto_rawDescData []byte
)

func file_MainServer_ChatMessage_proto_rawDescGZIP() []byte {
	file_MainServer_ChatMessage_proto_rawDescOnce.Do(func() {
		file_MainServer_ChatMessage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_ChatMessage_proto_rawDesc), len(file_MainServer_ChatMessage_proto_rawDesc)))
	})
	return file_MainServer_ChatMessage_proto_rawDescData
}

var file_MainServer_ChatMessage_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_ChatMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_ChatMessage_proto_goTypes = []any{
	(ChatMessageType)(0),      // 0: ChatMessage.ChatMessageType
	(*ChatMessageAuthor)(nil), // 1: ChatMessage.ChatMessageAuthor
	(*ChatMessageModel)(nil),  // 2: ChatMessage.ChatMessageModel
}
var file_MainServer_ChatMessage_proto_depIdxs = []int32{
	0, // 0: ChatMessage.ChatMessageModel.type:type_name -> ChatMessage.ChatMessageType
	1, // 1: ChatMessage.ChatMessageModel.author:type_name -> ChatMessage.ChatMessageAuthor
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_ChatMessage_proto_init() }
func file_MainServer_ChatMessage_proto_init() {
	if File_MainServer_ChatMessage_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_ChatMessage_proto_rawDesc), len(file_MainServer_ChatMessage_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ChatMessage_proto_goTypes,
		DependencyIndexes: file_MainServer_ChatMessage_proto_depIdxs,
		EnumInfos:         file_MainServer_ChatMessage_proto_enumTypes,
		MessageInfos:      file_MainServer_ChatMessage_proto_msgTypes,
	}.Build()
	File_MainServer_ChatMessage_proto = out.File
	file_MainServer_ChatMessage_proto_goTypes = nil
	file_MainServer_ChatMessage_proto_depIdxs = nil
}

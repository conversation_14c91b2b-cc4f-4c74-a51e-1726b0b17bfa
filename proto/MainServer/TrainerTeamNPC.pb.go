// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerTeamNPC.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerTeamNpcId int32

const (
	TrainerTeamNpcId_TEAM_NPC_NONE           TrainerTeamNpcId = 0
	TrainerTeamNpcId_TEAM_NPC_Boss           TrainerTeamNpcId = 1
	TrainerTeamNpcId_TEAM_NPC_receptionist_1 TrainerTeamNpcId = 11 //接待员
	TrainerTeamNpcId_TEAM_NPC_receptionist_2 TrainerTeamNpcId = 12 //接待员
	TrainerTeamNpcId_TEAM_NPC_receptionist_3 TrainerTeamNpcId = 13 //接待员
	TrainerTeamNpcId_TEAM_NPC_receptionist_4 TrainerTeamNpcId = 14 //接待员
	TrainerTeamNpcId_TEAM_NPC_receptionist_5 TrainerTeamNpcId = 15 //接待员
	TrainerTeamNpcId_TEAM_NPC_1              TrainerTeamNpcId = 101
	TrainerTeamNpcId_TEAM_NPC_2              TrainerTeamNpcId = 102
	TrainerTeamNpcId_TEAM_NPC_3              TrainerTeamNpcId = 103
	TrainerTeamNpcId_TEAM_NPC_4              TrainerTeamNpcId = 104
	TrainerTeamNpcId_TEAM_NPC_5              TrainerTeamNpcId = 105
	TrainerTeamNpcId_TEAM_NPC_6              TrainerTeamNpcId = 106
	TrainerTeamNpcId_TEAM_NPC_7              TrainerTeamNpcId = 107
	TrainerTeamNpcId_TEAM_NPC_8              TrainerTeamNpcId = 108
	TrainerTeamNpcId_TEAM_NPC_9              TrainerTeamNpcId = 109
	TrainerTeamNpcId_TEAM_NPC_10             TrainerTeamNpcId = 110
)

// Enum value maps for TrainerTeamNpcId.
var (
	TrainerTeamNpcId_name = map[int32]string{
		0:   "TEAM_NPC_NONE",
		1:   "TEAM_NPC_Boss",
		11:  "TEAM_NPC_receptionist_1",
		12:  "TEAM_NPC_receptionist_2",
		13:  "TEAM_NPC_receptionist_3",
		14:  "TEAM_NPC_receptionist_4",
		15:  "TEAM_NPC_receptionist_5",
		101: "TEAM_NPC_1",
		102: "TEAM_NPC_2",
		103: "TEAM_NPC_3",
		104: "TEAM_NPC_4",
		105: "TEAM_NPC_5",
		106: "TEAM_NPC_6",
		107: "TEAM_NPC_7",
		108: "TEAM_NPC_8",
		109: "TEAM_NPC_9",
		110: "TEAM_NPC_10",
	}
	TrainerTeamNpcId_value = map[string]int32{
		"TEAM_NPC_NONE":           0,
		"TEAM_NPC_Boss":           1,
		"TEAM_NPC_receptionist_1": 11,
		"TEAM_NPC_receptionist_2": 12,
		"TEAM_NPC_receptionist_3": 13,
		"TEAM_NPC_receptionist_4": 14,
		"TEAM_NPC_receptionist_5": 15,
		"TEAM_NPC_1":              101,
		"TEAM_NPC_2":              102,
		"TEAM_NPC_3":              103,
		"TEAM_NPC_4":              104,
		"TEAM_NPC_5":              105,
		"TEAM_NPC_6":              106,
		"TEAM_NPC_7":              107,
		"TEAM_NPC_8":              108,
		"TEAM_NPC_9":              109,
		"TEAM_NPC_10":             110,
	}
)

func (x TrainerTeamNpcId) Enum() *TrainerTeamNpcId {
	p := new(TrainerTeamNpcId)
	*p = x
	return p
}

func (x TrainerTeamNpcId) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerTeamNpcId) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerTeamNPC_proto_enumTypes[0].Descriptor()
}

func (TrainerTeamNpcId) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerTeamNPC_proto_enumTypes[0]
}

func (x TrainerTeamNpcId) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerTeamNpcId.Descriptor instead.
func (TrainerTeamNpcId) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeamNPC_proto_rawDescGZIP(), []int{0}
}

type TrainerTeamNpcInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            TrainerTeamNpcId       `protobuf:"varint,1,opt,name=id,proto3,enum=MainServer.TrainerTeamNpcId" json:"id,omitempty"`
	Config        *NpcRoleConfig         `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerTeamNpcInfo) Reset() {
	*x = TrainerTeamNpcInfo{}
	mi := &file_MainServer_TrainerTeamNPC_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerTeamNpcInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerTeamNpcInfo) ProtoMessage() {}

func (x *TrainerTeamNpcInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerTeamNPC_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerTeamNpcInfo.ProtoReflect.Descriptor instead.
func (*TrainerTeamNpcInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerTeamNPC_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerTeamNpcInfo) GetId() TrainerTeamNpcId {
	if x != nil {
		return x.Id
	}
	return TrainerTeamNpcId_TEAM_NPC_NONE
}

func (x *TrainerTeamNpcInfo) GetConfig() *NpcRoleConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

var File_MainServer_TrainerTeamNPC_proto protoreflect.FileDescriptor

const file_MainServer_TrainerTeamNPC_proto_rawDesc = "" +
	"\n" +
	"\x1fMainServer/TrainerTeamNPC.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Role.proto\"u\n" +
	"\x12TrainerTeamNpcInfo\x12,\n" +
	"\x02id\x18\x01 \x01(\x0e2\x1c.MainServer.TrainerTeamNpcIdR\x02id\x121\n" +
	"\x06config\x18\x02 \x01(\v2\x19.MainServer.NpcRoleConfigR\x06config*\xea\x02\n" +
	"\x10TrainerTeamNpcId\x12\x11\n" +
	"\rTEAM_NPC_NONE\x10\x00\x12\x11\n" +
	"\rTEAM_NPC_Boss\x10\x01\x12\x1b\n" +
	"\x17TEAM_NPC_receptionist_1\x10\v\x12\x1b\n" +
	"\x17TEAM_NPC_receptionist_2\x10\f\x12\x1b\n" +
	"\x17TEAM_NPC_receptionist_3\x10\r\x12\x1b\n" +
	"\x17TEAM_NPC_receptionist_4\x10\x0e\x12\x1b\n" +
	"\x17TEAM_NPC_receptionist_5\x10\x0f\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_1\x10e\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_2\x10f\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_3\x10g\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_4\x10h\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_5\x10i\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_6\x10j\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_7\x10k\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_8\x10l\x12\x0e\n" +
	"\n" +
	"TEAM_NPC_9\x10m\x12\x0f\n" +
	"\vTEAM_NPC_10\x10nB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerTeamNPC_proto_rawDescOnce sync.Once
	file_MainServer_TrainerTeamNPC_proto_rawDescData []byte
)

func file_MainServer_TrainerTeamNPC_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerTeamNPC_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerTeamNPC_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerTeamNPC_proto_rawDesc), len(file_MainServer_TrainerTeamNPC_proto_rawDesc)))
	})
	return file_MainServer_TrainerTeamNPC_proto_rawDescData
}

var file_MainServer_TrainerTeamNPC_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_TrainerTeamNPC_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_TrainerTeamNPC_proto_goTypes = []any{
	(TrainerTeamNpcId)(0),      // 0: MainServer.TrainerTeamNpcId
	(*TrainerTeamNpcInfo)(nil), // 1: MainServer.TrainerTeamNpcInfo
	(*NpcRoleConfig)(nil),      // 2: MainServer.NpcRoleConfig
}
var file_MainServer_TrainerTeamNPC_proto_depIdxs = []int32{
	0, // 0: MainServer.TrainerTeamNpcInfo.id:type_name -> MainServer.TrainerTeamNpcId
	2, // 1: MainServer.TrainerTeamNpcInfo.config:type_name -> MainServer.NpcRoleConfig
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerTeamNPC_proto_init() }
func file_MainServer_TrainerTeamNPC_proto_init() {
	if File_MainServer_TrainerTeamNPC_proto != nil {
		return
	}
	file_MainServer_Role_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerTeamNPC_proto_rawDesc), len(file_MainServer_TrainerTeamNPC_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerTeamNPC_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerTeamNPC_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerTeamNPC_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerTeamNPC_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerTeamNPC_proto = out.File
	file_MainServer_TrainerTeamNPC_proto_goTypes = nil
	file_MainServer_TrainerTeamNPC_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/TrainerStrict.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrainerStrictSource int32

const (
	TrainerStrictSource_TrainerStrictSource_None                TrainerStrictSource = 0
	TrainerStrictSource_TrainerStrictSource_Quest               TrainerStrictSource = 1
	TrainerStrictSource_TrainerStrictSource_Battle_Force_Invite TrainerStrictSource = 2 //战斗强制邀请
)

// Enum value maps for TrainerStrictSource.
var (
	TrainerStrictSource_name = map[int32]string{
		0: "TrainerStrictSource_None",
		1: "TrainerStrictSource_Quest",
		2: "TrainerStrictSource_Battle_Force_Invite",
	}
	TrainerStrictSource_value = map[string]int32{
		"TrainerStrictSource_None":                0,
		"TrainerStrictSource_Quest":               1,
		"TrainerStrictSource_Battle_Force_Invite": 2,
	}
)

func (x TrainerStrictSource) Enum() *TrainerStrictSource {
	p := new(TrainerStrictSource)
	*p = x
	return p
}

func (x TrainerStrictSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerStrictSource) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerStrict_proto_enumTypes[0].Descriptor()
}

func (TrainerStrictSource) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerStrict_proto_enumTypes[0]
}

func (x TrainerStrictSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerStrictSource.Descriptor instead.
func (TrainerStrictSource) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerStrict_proto_rawDescGZIP(), []int{0}
}

type TrainerStrictType int32

const (
	TrainerStrictType_TrainerStrictType_None                  TrainerStrictType = 0
	TrainerStrictType_TrainerStrictType_CantFly               TrainerStrictType = 1 //不能飞行
	TrainerStrictType_TrainerStrictType_CantRide              TrainerStrictType = 2 //不能乘骑
	TrainerStrictType_TrainerStrictType_CantCatch             TrainerStrictType = 3 //不能捕捉
	TrainerStrictType_TrainerStrictType_CantBattle            TrainerStrictType = 4 //不能战斗
	TrainerStrictType_TrainerStrictType_CantMove              TrainerStrictType = 5 //不能移动
	TrainerStrictType_TrainerStrictType_CantRun               TrainerStrictType = 6 //不能跑
	TrainerStrictType_TrainerStrictType_CantUpdateAroundPokes TrainerStrictType = 7 //不能更新周围宝可梦
	TrainerStrictType_TrainerStrictType_FirstPoke             TrainerStrictType = 8 //限制第一个宝可梦为xx
)

// Enum value maps for TrainerStrictType.
var (
	TrainerStrictType_name = map[int32]string{
		0: "TrainerStrictType_None",
		1: "TrainerStrictType_CantFly",
		2: "TrainerStrictType_CantRide",
		3: "TrainerStrictType_CantCatch",
		4: "TrainerStrictType_CantBattle",
		5: "TrainerStrictType_CantMove",
		6: "TrainerStrictType_CantRun",
		7: "TrainerStrictType_CantUpdateAroundPokes",
		8: "TrainerStrictType_FirstPoke",
	}
	TrainerStrictType_value = map[string]int32{
		"TrainerStrictType_None":                  0,
		"TrainerStrictType_CantFly":               1,
		"TrainerStrictType_CantRide":              2,
		"TrainerStrictType_CantCatch":             3,
		"TrainerStrictType_CantBattle":            4,
		"TrainerStrictType_CantMove":              5,
		"TrainerStrictType_CantRun":               6,
		"TrainerStrictType_CantUpdateAroundPokes": 7,
		"TrainerStrictType_FirstPoke":             8,
	}
)

func (x TrainerStrictType) Enum() *TrainerStrictType {
	p := new(TrainerStrictType)
	*p = x
	return p
}

func (x TrainerStrictType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerStrictType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_TrainerStrict_proto_enumTypes[1].Descriptor()
}

func (TrainerStrictType) Type() protoreflect.EnumType {
	return &file_MainServer_TrainerStrict_proto_enumTypes[1]
}

func (x TrainerStrictType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerStrictType.Descriptor instead.
func (TrainerStrictType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_TrainerStrict_proto_rawDescGZIP(), []int{1}
}

type TrainerStrictInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// int64 source_id = 2;
	Stricts       []*TrainerStrict `protobuf:"bytes,3,rep,name=stricts,proto3" json:"stricts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrainerStrictInfo) Reset() {
	*x = TrainerStrictInfo{}
	mi := &file_MainServer_TrainerStrict_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerStrictInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerStrictInfo) ProtoMessage() {}

func (x *TrainerStrictInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerStrict_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerStrictInfo.ProtoReflect.Descriptor instead.
func (*TrainerStrictInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerStrict_proto_rawDescGZIP(), []int{0}
}

func (x *TrainerStrictInfo) GetStricts() []*TrainerStrict {
	if x != nil {
		return x.Stricts
	}
	return nil
}

type TrainerStrict struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Source         TrainerStrictSource    `protobuf:"varint,1,opt,name=source,proto3,enum=MainServer.TrainerStrictSource" json:"source,omitempty"`
	StrictType     TrainerStrictType      `protobuf:"varint,2,opt,name=strict_type,json=strictType,proto3,enum=MainServer.TrainerStrictType" json:"strict_type,omitempty"`
	StrictIntValue int64                  `protobuf:"varint,3,opt,name=strict_int_value,json=strictIntValue,proto3" json:"strict_int_value,omitempty"`
	StrictStrValue string                 `protobuf:"bytes,4,opt,name=strict_str_value,json=strictStrValue,proto3" json:"strict_str_value,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TrainerStrict) Reset() {
	*x = TrainerStrict{}
	mi := &file_MainServer_TrainerStrict_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerStrict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerStrict) ProtoMessage() {}

func (x *TrainerStrict) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_TrainerStrict_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerStrict.ProtoReflect.Descriptor instead.
func (*TrainerStrict) Descriptor() ([]byte, []int) {
	return file_MainServer_TrainerStrict_proto_rawDescGZIP(), []int{1}
}

func (x *TrainerStrict) GetSource() TrainerStrictSource {
	if x != nil {
		return x.Source
	}
	return TrainerStrictSource_TrainerStrictSource_None
}

func (x *TrainerStrict) GetStrictType() TrainerStrictType {
	if x != nil {
		return x.StrictType
	}
	return TrainerStrictType_TrainerStrictType_None
}

func (x *TrainerStrict) GetStrictIntValue() int64 {
	if x != nil {
		return x.StrictIntValue
	}
	return 0
}

func (x *TrainerStrict) GetStrictStrValue() string {
	if x != nil {
		return x.StrictStrValue
	}
	return ""
}

var File_MainServer_TrainerStrict_proto protoreflect.FileDescriptor

const file_MainServer_TrainerStrict_proto_rawDesc = "" +
	"\n" +
	"\x1eMainServer/TrainerStrict.proto\x12\n" +
	"MainServer\"H\n" +
	"\x11TrainerStrictInfo\x123\n" +
	"\astricts\x18\x03 \x03(\v2\x19.MainServer.TrainerStrictR\astricts\"\xdc\x01\n" +
	"\rTrainerStrict\x127\n" +
	"\x06source\x18\x01 \x01(\x0e2\x1f.MainServer.TrainerStrictSourceR\x06source\x12>\n" +
	"\vstrict_type\x18\x02 \x01(\x0e2\x1d.MainServer.TrainerStrictTypeR\n" +
	"strictType\x12(\n" +
	"\x10strict_int_value\x18\x03 \x01(\x03R\x0estrictIntValue\x12(\n" +
	"\x10strict_str_value\x18\x04 \x01(\tR\x0estrictStrValue*\x7f\n" +
	"\x13TrainerStrictSource\x12\x1c\n" +
	"\x18TrainerStrictSource_None\x10\x00\x12\x1d\n" +
	"\x19TrainerStrictSource_Quest\x10\x01\x12+\n" +
	"'TrainerStrictSource_Battle_Force_Invite\x10\x02*\xbe\x02\n" +
	"\x11TrainerStrictType\x12\x1a\n" +
	"\x16TrainerStrictType_None\x10\x00\x12\x1d\n" +
	"\x19TrainerStrictType_CantFly\x10\x01\x12\x1e\n" +
	"\x1aTrainerStrictType_CantRide\x10\x02\x12\x1f\n" +
	"\x1bTrainerStrictType_CantCatch\x10\x03\x12 \n" +
	"\x1cTrainerStrictType_CantBattle\x10\x04\x12\x1e\n" +
	"\x1aTrainerStrictType_CantMove\x10\x05\x12\x1d\n" +
	"\x19TrainerStrictType_CantRun\x10\x06\x12+\n" +
	"'TrainerStrictType_CantUpdateAroundPokes\x10\a\x12\x1f\n" +
	"\x1bTrainerStrictType_FirstPoke\x10\bB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_TrainerStrict_proto_rawDescOnce sync.Once
	file_MainServer_TrainerStrict_proto_rawDescData []byte
)

func file_MainServer_TrainerStrict_proto_rawDescGZIP() []byte {
	file_MainServer_TrainerStrict_proto_rawDescOnce.Do(func() {
		file_MainServer_TrainerStrict_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_TrainerStrict_proto_rawDesc), len(file_MainServer_TrainerStrict_proto_rawDesc)))
	})
	return file_MainServer_TrainerStrict_proto_rawDescData
}

var file_MainServer_TrainerStrict_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_TrainerStrict_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_TrainerStrict_proto_goTypes = []any{
	(TrainerStrictSource)(0),  // 0: MainServer.TrainerStrictSource
	(TrainerStrictType)(0),    // 1: MainServer.TrainerStrictType
	(*TrainerStrictInfo)(nil), // 2: MainServer.TrainerStrictInfo
	(*TrainerStrict)(nil),     // 3: MainServer.TrainerStrict
}
var file_MainServer_TrainerStrict_proto_depIdxs = []int32{
	3, // 0: MainServer.TrainerStrictInfo.stricts:type_name -> MainServer.TrainerStrict
	0, // 1: MainServer.TrainerStrict.source:type_name -> MainServer.TrainerStrictSource
	1, // 2: MainServer.TrainerStrict.strict_type:type_name -> MainServer.TrainerStrictType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_MainServer_TrainerStrict_proto_init() }
func file_MainServer_TrainerStrict_proto_init() {
	if File_MainServer_TrainerStrict_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_TrainerStrict_proto_rawDesc), len(file_MainServer_TrainerStrict_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_TrainerStrict_proto_goTypes,
		DependencyIndexes: file_MainServer_TrainerStrict_proto_depIdxs,
		EnumInfos:         file_MainServer_TrainerStrict_proto_enumTypes,
		MessageInfos:      file_MainServer_TrainerStrict_proto_msgTypes,
	}.Build()
	File_MainServer_TrainerStrict_proto = out.File
	file_MainServer_TrainerStrict_proto_goTypes = nil
	file_MainServer_TrainerStrict_proto_depIdxs = nil
}

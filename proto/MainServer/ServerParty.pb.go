// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/ServerParty.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PartyMessageType int32

const (
	PartyMessageType_party       PartyMessageType = 0
	PartyMessageType_partyBattle PartyMessageType = 1
)

// Enum value maps for PartyMessageType.
var (
	PartyMessageType_name = map[int32]string{
		0: "party",
		1: "partyBattle",
	}
	PartyMessageType_value = map[string]int32{
		"party":       0,
		"partyBattle": 1,
	}
)

func (x PartyMessageType) Enum() *PartyMessageType {
	p := new(PartyMessageType)
	*p = x
	return p
}

func (x PartyMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PartyMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerParty_proto_enumTypes[0].Descriptor()
}

func (PartyMessageType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerParty_proto_enumTypes[0]
}

func (x PartyMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PartyMessageType.Descriptor instead.
func (PartyMessageType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerParty_proto_rawDescGZIP(), []int{0}
}

type PartyMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sender        *Trainer               `protobuf:"bytes,1,opt,name=sender,proto3" json:"sender,omitempty"`
	MessaegType   PartyMessageType       `protobuf:"varint,2,opt,name=messaegType,proto3,enum=MainServer.PartyMessageType" json:"messaegType,omitempty"`
	SenderPokes   []*Poke                `protobuf:"bytes,3,rep,name=senderPokes,proto3" json:"senderPokes,omitempty"`
	Prepare       *BattlePrepare         `protobuf:"bytes,4,opt,name=prepare,proto3" json:"prepare,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PartyMessage) Reset() {
	*x = PartyMessage{}
	mi := &file_MainServer_ServerParty_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PartyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PartyMessage) ProtoMessage() {}

func (x *PartyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerParty_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PartyMessage.ProtoReflect.Descriptor instead.
func (*PartyMessage) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerParty_proto_rawDescGZIP(), []int{0}
}

func (x *PartyMessage) GetSender() *Trainer {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *PartyMessage) GetMessaegType() PartyMessageType {
	if x != nil {
		return x.MessaegType
	}
	return PartyMessageType_party
}

func (x *PartyMessage) GetSenderPokes() []*Poke {
	if x != nil {
		return x.SenderPokes
	}
	return nil
}

func (x *PartyMessage) GetPrepare() *BattlePrepare {
	if x != nil {
		return x.Prepare
	}
	return nil
}

var File_MainServer_ServerParty_proto protoreflect.FileDescriptor

const file_MainServer_ServerParty_proto_rawDesc = "" +
	"\n" +
	"\x1cMainServer/ServerParty.proto\x12\n" +
	"MainServer\x1a\x1bMainServer/BattleInfo.proto\x1a\x18MainServer/Trainer.proto\x1a\x15MainServer/Poke.proto\"\xe4\x01\n" +
	"\fPartyMessage\x12+\n" +
	"\x06sender\x18\x01 \x01(\v2\x13.MainServer.TrainerR\x06sender\x12>\n" +
	"\vmessaegType\x18\x02 \x01(\x0e2\x1c.MainServer.PartyMessageTypeR\vmessaegType\x122\n" +
	"\vsenderPokes\x18\x03 \x03(\v2\x10.MainServer.PokeR\vsenderPokes\x123\n" +
	"\aprepare\x18\x04 \x01(\v2\x19.MainServer.BattlePrepareR\aprepare*.\n" +
	"\x10PartyMessageType\x12\t\n" +
	"\x05party\x10\x00\x12\x0f\n" +
	"\vpartyBattle\x10\x01B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_ServerParty_proto_rawDescOnce sync.Once
	file_MainServer_ServerParty_proto_rawDescData []byte
)

func file_MainServer_ServerParty_proto_rawDescGZIP() []byte {
	file_MainServer_ServerParty_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerParty_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_ServerParty_proto_rawDesc), len(file_MainServer_ServerParty_proto_rawDesc)))
	})
	return file_MainServer_ServerParty_proto_rawDescData
}

var file_MainServer_ServerParty_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_ServerParty_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_MainServer_ServerParty_proto_goTypes = []any{
	(PartyMessageType)(0), // 0: MainServer.PartyMessageType
	(*PartyMessage)(nil),  // 1: MainServer.PartyMessage
	(*Trainer)(nil),       // 2: MainServer.Trainer
	(*Poke)(nil),          // 3: MainServer.Poke
	(*BattlePrepare)(nil), // 4: MainServer.BattlePrepare
}
var file_MainServer_ServerParty_proto_depIdxs = []int32{
	2, // 0: MainServer.PartyMessage.sender:type_name -> MainServer.Trainer
	0, // 1: MainServer.PartyMessage.messaegType:type_name -> MainServer.PartyMessageType
	3, // 2: MainServer.PartyMessage.senderPokes:type_name -> MainServer.Poke
	4, // 3: MainServer.PartyMessage.prepare:type_name -> MainServer.BattlePrepare
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_ServerParty_proto_init() }
func file_MainServer_ServerParty_proto_init() {
	if File_MainServer_ServerParty_proto != nil {
		return
	}
	file_MainServer_BattleInfo_proto_init()
	file_MainServer_Trainer_proto_init()
	file_MainServer_Poke_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_ServerParty_proto_rawDesc), len(file_MainServer_ServerParty_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerParty_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerParty_proto_depIdxs,
		EnumInfos:         file_MainServer_ServerParty_proto_enumTypes,
		MessageInfos:      file_MainServer_ServerParty_proto_msgTypes,
	}.Build()
	File_MainServer_ServerParty_proto = out.File
	file_MainServer_ServerParty_proto_goTypes = nil
	file_MainServer_ServerParty_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: BattleServer/PackageBattleResult.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MajorActionType int32

const (
	MajorActionType_majorNone     MajorActionType = 0
	MajorActionType_move          MajorActionType = 1
	MajorActionType_switch        MajorActionType = 2
	MajorActionType_drag          MajorActionType = 3
	MajorActionType_detailschange MajorActionType = 4
	MajorActionType_formechange   MajorActionType = 5
	MajorActionType_replace       MajorActionType = 6
	MajorActionType_swap          MajorActionType = 7
	MajorActionType_cant          MajorActionType = 8
	MajorActionType_faint         MajorActionType = 9
	MajorActionType_titem         MajorActionType = 10
)

// Enum value maps for MajorActionType.
var (
	MajorActionType_name = map[int32]string{
		0:  "majorNone",
		1:  "move",
		2:  "switch",
		3:  "drag",
		4:  "detailschange",
		5:  "formechange",
		6:  "replace",
		7:  "swap",
		8:  "cant",
		9:  "faint",
		10: "titem",
	}
	MajorActionType_value = map[string]int32{
		"majorNone":     0,
		"move":          1,
		"switch":        2,
		"drag":          3,
		"detailschange": 4,
		"formechange":   5,
		"replace":       6,
		"swap":          7,
		"cant":          8,
		"faint":         9,
		"titem":         10,
	}
)

func (x MajorActionType) Enum() *MajorActionType {
	p := new(MajorActionType)
	*p = x
	return p
}

func (x MajorActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MajorActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_BattleServer_PackageBattleResult_proto_enumTypes[0].Descriptor()
}

func (MajorActionType) Type() protoreflect.EnumType {
	return &file_BattleServer_PackageBattleResult_proto_enumTypes[0]
}

func (x MajorActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MajorActionType.Descriptor instead.
func (MajorActionType) EnumDescriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{0}
}

type MinorActionType int32

const (
	MinorActionType_minorNone MinorActionType = 0
	// 指定的ACTION针对目标失败POKEMON。 ACTION应为由于自身机制而失败的招式。
	// 应-block改为使用由于受到其他效果阻挡而失败的招式（或效果激活）。
	MinorActionType_fail MinorActionType = 54
	// 针对 的效果POKEMON被阻挡EFFECT。这可以选择指定效果来自MOVE。
	// ATTACKER如果不是，[of]SOURCE则会记录 的所有者（例如，与 Aroma Veil 的盟友）。
	MinorActionType_block MinorActionType = 1
	// 由于没有目标宝可梦 ，因此移动失败POKEMON。POKEMON不存在于第 1 代中。
	// 此操作特定于第 1-4 代，因为在后续的世代中，失败的移动将使用 来显示-fail。
	MinorActionType_notarget MinorActionType = 2
	// 神奇宝贝使用的招式SOURCE错过了（甚至没有出现）该TARGET神奇宝贝。
	MinorActionType_miss MinorActionType = 3
	// 指定的神奇宝贝POKEMON已受到伤害，现在为HP STATUS。
	MinorActionType_damage MinorActionType = 4
	// 与 -damage 相同，但是该宝可梦已治愈伤害。
	MinorActionType_heal MinorActionType = 5
	// 指定的神奇宝贝POKEMON现在有HP生命值。
	MinorActionType_sethp MinorActionType = 6
	// 这只神奇宝贝POKEMON已受到 的影响STATUS。
	MinorActionType_status MinorActionType = 7
	// 这只神奇宝贝POKEMON已从 中康复STATUS。
	MinorActionType_curestatus MinorActionType = 8
	// 这只神奇宝贝POKEMON使用了一种可以治愈其队伍状态效果的招式，例如治疗铃。
	MinorActionType_cureteam MinorActionType = 9
	// 指定的神奇宝贝POKEMON获得了AMOUNT，STAT使用神奇宝贝战斗中状态变化的标准规则。
	MinorActionType_boost MinorActionType = 10
	// 与 -boost 相同，但用于改变负面状态。
	MinorActionType_unboost MinorActionType = 11
	// 与 -boost 和 -unboost 相同，但STAT设置为而AMOUNT不是 增强。
	MinorActionType_setboost MinorActionType = 12
	// 在神奇宝贝与神奇宝贝STATS之间交换增强 。
	MinorActionType_swapboost MinorActionType = 13
	// 反转目标神奇宝贝的增强效果POKEMON。（例如：Topsy-Turvy）。
	MinorActionType_invertboost MinorActionType = 14
	// 清除目标的所有增强效果POKEMON。（例如：清除烟雾）。
	MinorActionType_clearboost MinorActionType = 15
	// 清除双方所有宝可梦的增强效果。（例如：Haze）。
	MinorActionType_clearallboost MinorActionType = 16
	// TARGET清除由于该宝可梦的某个EFFECT技能而对该宝可梦产生的正面提升POKEMON。
	MinorActionType_clearpositiveboost MinorActionType = 17
	// 清除目标神奇宝贝的负面增强POKEMON。（例如：通常是的结果[zeffect]）。
	MinorActionType_clearnegativeboost MinorActionType = 18
	// 将增强功能从SOURCEPokémon 复制到TARGETPokémon（例如：Psych Up）。
	MinorActionType_copyboost MinorActionType = 19
	// 表示当前生效的天气。
	MinorActionType_weather MinorActionType = 20
	// 场地条件CONDITION已启动。
	MinorActionType_fieldstart MinorActionType = 21
	// 表示现场条件CONDITION已经结束。
	MinorActionType_fieldend MinorActionType = 22
	// CONDITION已开始出现附带条件SIDE。
	MinorActionType_sidestart MinorActionType = 23
	// 表明CONDITION给定的边条件已经结束SIDE。
	MinorActionType_sideend MinorActionType = 24
	// 交换双方的条件。用于法院变更。
	MinorActionType_swapsideconditions MinorActionType = 25
	// 宝可梦因 EFFECT 而受到不稳定状态的影响。
	MinorActionType_start MinorActionType = 26
	// EFFECT对神奇宝贝造成的不稳定状态POKEMON已经结束。
	MinorActionType_end MinorActionType = 27
	// 一招对 造成了致命一击POKEMON。
	MinorActionType_crit MinorActionType = 28
	// 这一招对 超级有效POKEMON。
	MinorActionType_supereffective MinorActionType = 29
	// 此举对 并没有什么效果POKEMON。
	MinorActionType_resisted MinorActionType = 30
	// 不受POKEMON任何动作影响。
	MinorActionType_immune MinorActionType = 31
	// 所持ITEM有的POKEMON因移动或能力 而改变或显现EFFECT。
	MinorActionType_item MinorActionType = 32
	// POKEMON刚刚切换，其物品ITEM被宣布具有长期效果（不会使用[from]）。
	MinorActionType_enditem MinorActionType = 33
	// 的ABILITY已POKEMON因 移动/能力 而改变EFFECT。
	MinorActionType_ability MinorActionType = 34
	// POKEMON刚刚切换，并且其能力ABILITY被宣布具有长期效果。
	MinorActionType_endability MinorActionType = 35
	// 该神奇宝贝已通过招式“变身”或能力“冒名顶替者”POKEMON变身成为SPECIES。
	MinorActionType_transform MinorActionType = 36
	// POKEMON曾经进行过超进化 (Mega Evolved)的宝可梦MEGASTONE。
	MinorActionType_mega MinorActionType = 37
	// 这只神奇宝贝POKEMON已恢复其原始形态。
	MinorActionType_primal MinorActionType = 38
	// 这只神奇宝贝POKEMON已经习惯了ITEM超级爆发SPECIES。
	MinorActionType_burst MinorActionType = 39
	// 这只神奇宝贝POKEMON使用了 z 招式版本。
	MinorActionType_zpower MinorActionType = 40
	// Z 招式突破了防护并击中了POKEMON。
	MinorActionType_zbroken MinorActionType = 41
	// 杂项效果已激活。
	MinorActionType_activate MinorActionType = 42
	// 在括号中向客户端显示一条消息。
	MinorActionType_hint MinorActionType = 43
	// 在三重战斗中，宝可梦已经自动居中。
	MinorActionType_center MinorActionType = 44
	// 向客户端显示杂项消息。
	MinorActionType_message MinorActionType = 45
	// 一个动作已与另一个动作相结合。
	MinorActionType_combine MinorActionType = 46
	// 该SOURCE宝可梦已使用招式并且正在等待该TARGET宝可梦。
	MinorActionType_waiting MinorActionType = 47
	// 该ATTACKER宝可梦正准备对未知目标发动冲锋MOVE。
	MinorActionType_prepare MinorActionType = 48
	// 神奇宝贝POKEMON必须花费一回合的时间从上一次的动作中恢复过来。
	MinorActionType_mustrecharge MinorActionType = 49
	// 此动作完全没有作用（已弃用）。
	MinorActionType_nothing MinorActionType = 50
	// 一招多招，震撼时代POKEMON。
	MinorActionType_hitcount MinorActionType = 51
	// 宝可梦POKEMON使用的招式MOVE会产生暂时效果。
	MinorActionType_singlemove MinorActionType = 52
	// 宝可梦POKEMON使用的招式MOVE会产生持续整个回合的暂时效果。
	MinorActionType_singleturn    MinorActionType = 53
	MinorActionType_fieldactivate MinorActionType = 55
)

// Enum value maps for MinorActionType.
var (
	MinorActionType_name = map[int32]string{
		0:  "minorNone",
		54: "fail",
		1:  "block",
		2:  "notarget",
		3:  "miss",
		4:  "damage",
		5:  "heal",
		6:  "sethp",
		7:  "status",
		8:  "curestatus",
		9:  "cureteam",
		10: "boost",
		11: "unboost",
		12: "setboost",
		13: "swapboost",
		14: "invertboost",
		15: "clearboost",
		16: "clearallboost",
		17: "clearpositiveboost",
		18: "clearnegativeboost",
		19: "copyboost",
		20: "weather",
		21: "fieldstart",
		22: "fieldend",
		23: "sidestart",
		24: "sideend",
		25: "swapsideconditions",
		26: "start",
		27: "end",
		28: "crit",
		29: "supereffective",
		30: "resisted",
		31: "immune",
		32: "item",
		33: "enditem",
		34: "ability",
		35: "endability",
		36: "transform",
		37: "mega",
		38: "primal",
		39: "burst",
		40: "zpower",
		41: "zbroken",
		42: "activate",
		43: "hint",
		44: "center",
		45: "message",
		46: "combine",
		47: "waiting",
		48: "prepare",
		49: "mustrecharge",
		50: "nothing",
		51: "hitcount",
		52: "singlemove",
		53: "singleturn",
		55: "fieldactivate",
	}
	MinorActionType_value = map[string]int32{
		"minorNone":          0,
		"fail":               54,
		"block":              1,
		"notarget":           2,
		"miss":               3,
		"damage":             4,
		"heal":               5,
		"sethp":              6,
		"status":             7,
		"curestatus":         8,
		"cureteam":           9,
		"boost":              10,
		"unboost":            11,
		"setboost":           12,
		"swapboost":          13,
		"invertboost":        14,
		"clearboost":         15,
		"clearallboost":      16,
		"clearpositiveboost": 17,
		"clearnegativeboost": 18,
		"copyboost":          19,
		"weather":            20,
		"fieldstart":         21,
		"fieldend":           22,
		"sidestart":          23,
		"sideend":            24,
		"swapsideconditions": 25,
		"start":              26,
		"end":                27,
		"crit":               28,
		"supereffective":     29,
		"resisted":           30,
		"immune":             31,
		"item":               32,
		"enditem":            33,
		"ability":            34,
		"endability":         35,
		"transform":          36,
		"mega":               37,
		"primal":             38,
		"burst":              39,
		"zpower":             40,
		"zbroken":            41,
		"activate":           42,
		"hint":               43,
		"center":             44,
		"message":            45,
		"combine":            46,
		"waiting":            47,
		"prepare":            48,
		"mustrecharge":       49,
		"nothing":            50,
		"hitcount":           51,
		"singlemove":         52,
		"singleturn":         53,
		"fieldactivate":      55,
	}
)

func (x MinorActionType) Enum() *MinorActionType {
	p := new(MinorActionType)
	*p = x
	return p
}

func (x MinorActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MinorActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_BattleServer_PackageBattleResult_proto_enumTypes[1].Descriptor()
}

func (MinorActionType) Type() protoreflect.EnumType {
	return &file_BattleServer_PackageBattleResult_proto_enumTypes[1]
}

func (x MinorActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MinorActionType.Descriptor instead.
func (MinorActionType) EnumDescriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{1}
}

type PackageBattleMessageValue struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Actions       []*PackageBattleMessageValueAction `protobuf:"bytes,1,rep,name=actions,proto3" json:"actions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PackageBattleMessageValue) Reset() {
	*x = PackageBattleMessageValue{}
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageBattleMessageValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageBattleMessageValue) ProtoMessage() {}

func (x *PackageBattleMessageValue) ProtoReflect() protoreflect.Message {
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageBattleMessageValue.ProtoReflect.Descriptor instead.
func (*PackageBattleMessageValue) Descriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{0}
}

func (x *PackageBattleMessageValue) GetActions() []*PackageBattleMessageValueAction {
	if x != nil {
		return x.Actions
	}
	return nil
}

type PackageBattleMessageValueAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Actions       []string               `protobuf:"bytes,1,rep,name=actions,proto3" json:"actions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PackageBattleMessageValueAction) Reset() {
	*x = PackageBattleMessageValueAction{}
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageBattleMessageValueAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageBattleMessageValueAction) ProtoMessage() {}

func (x *PackageBattleMessageValueAction) ProtoReflect() protoreflect.Message {
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageBattleMessageValueAction.ProtoReflect.Descriptor instead.
func (*PackageBattleMessageValueAction) Descriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{1}
}

func (x *PackageBattleMessageValueAction) GetActions() []string {
	if x != nil {
		return x.Actions
	}
	return nil
}

type PackageBattleMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Actions       []*PackageBattleAction `protobuf:"bytes,1,rep,name=actions,proto3" json:"actions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PackageBattleMessage) Reset() {
	*x = PackageBattleMessage{}
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageBattleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageBattleMessage) ProtoMessage() {}

func (x *PackageBattleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageBattleMessage.ProtoReflect.Descriptor instead.
func (*PackageBattleMessage) Descriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{2}
}

func (x *PackageBattleMessage) GetActions() []*PackageBattleAction {
	if x != nil {
		return x.Actions
	}
	return nil
}

type PackageBattleAction struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	MajorActionType MajorActionType        `protobuf:"varint,1,opt,name=majorActionType,proto3,enum=PetsBattleServerProto.MajorActionType" json:"majorActionType,omitempty"`
	// MinorActionType minorActionType = 2;
	MajorAction   *MajorAction   `protobuf:"bytes,3,opt,name=majorAction,proto3" json:"majorAction,omitempty"`
	MinorActions  []*MinorAction `protobuf:"bytes,4,rep,name=minorActions,proto3" json:"minorActions,omitempty"` // BattleResultRequest request = 5;
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PackageBattleAction) Reset() {
	*x = PackageBattleAction{}
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageBattleAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageBattleAction) ProtoMessage() {}

func (x *PackageBattleAction) ProtoReflect() protoreflect.Message {
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageBattleAction.ProtoReflect.Descriptor instead.
func (*PackageBattleAction) Descriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{3}
}

func (x *PackageBattleAction) GetMajorActionType() MajorActionType {
	if x != nil {
		return x.MajorActionType
	}
	return MajorActionType_majorNone
}

func (x *PackageBattleAction) GetMajorAction() *MajorAction {
	if x != nil {
		return x.MajorAction
	}
	return nil
}

func (x *PackageBattleAction) GetMinorActions() []*MinorAction {
	if x != nil {
		return x.MinorActions
	}
	return nil
}

type MajorAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          MajorActionType        `protobuf:"varint,1,opt,name=type,proto3,enum=PetsBattleServerProto.MajorActionType" json:"type,omitempty"`
	PetId         string                 `protobuf:"bytes,2,opt,name=petId,proto3" json:"petId,omitempty"`
	Loc           string                 `protobuf:"bytes,3,opt,name=loc,proto3" json:"loc,omitempty"`
	MoveId        string                 `protobuf:"bytes,4,opt,name=moveId,proto3" json:"moveId,omitempty"`
	Target        string                 `protobuf:"bytes,5,opt,name=target,proto3" json:"target,omitempty"`
	TargetLoc     string                 `protobuf:"bytes,6,opt,name=targetLoc,proto3" json:"targetLoc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MajorAction) Reset() {
	*x = MajorAction{}
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MajorAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MajorAction) ProtoMessage() {}

func (x *MajorAction) ProtoReflect() protoreflect.Message {
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MajorAction.ProtoReflect.Descriptor instead.
func (*MajorAction) Descriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{4}
}

func (x *MajorAction) GetType() MajorActionType {
	if x != nil {
		return x.Type
	}
	return MajorActionType_majorNone
}

func (x *MajorAction) GetPetId() string {
	if x != nil {
		return x.PetId
	}
	return ""
}

func (x *MajorAction) GetLoc() string {
	if x != nil {
		return x.Loc
	}
	return ""
}

func (x *MajorAction) GetMoveId() string {
	if x != nil {
		return x.MoveId
	}
	return ""
}

func (x *MajorAction) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *MajorAction) GetTargetLoc() string {
	if x != nil {
		return x.TargetLoc
	}
	return ""
}

type MinorAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          MinorActionType        `protobuf:"varint,1,opt,name=type,proto3,enum=PetsBattleServerProto.MinorActionType" json:"type,omitempty"`
	PetId         string                 `protobuf:"bytes,2,opt,name=petId,proto3" json:"petId,omitempty"`
	Loc           string                 `protobuf:"bytes,3,opt,name=loc,proto3" json:"loc,omitempty"`
	ActionValue   string                 `protobuf:"bytes,4,opt,name=action_value,json=actionValue,proto3" json:"action_value,omitempty"`
	HpStatus      string                 `protobuf:"bytes,5,opt,name=hpStatus,proto3" json:"hpStatus,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MinorAction) Reset() {
	*x = MinorAction{}
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MinorAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MinorAction) ProtoMessage() {}

func (x *MinorAction) ProtoReflect() protoreflect.Message {
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MinorAction.ProtoReflect.Descriptor instead.
func (*MinorAction) Descriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{5}
}

func (x *MinorAction) GetType() MinorActionType {
	if x != nil {
		return x.Type
	}
	return MinorActionType_minorNone
}

func (x *MinorAction) GetPetId() string {
	if x != nil {
		return x.PetId
	}
	return ""
}

func (x *MinorAction) GetLoc() string {
	if x != nil {
		return x.Loc
	}
	return ""
}

func (x *MinorAction) GetActionValue() string {
	if x != nil {
		return x.ActionValue
	}
	return ""
}

func (x *MinorAction) GetHpStatus() string {
	if x != nil {
		return x.HpStatus
	}
	return ""
}

type ActiveMove struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Move          string                 `protobuf:"bytes,1,opt,name=move,proto3" json:"move,omitempty"`          // 动作名称
	Id            string                 `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`              // 动作 ID
	Pp            int32                  `protobuf:"varint,3,opt,name=pp,proto3" json:"pp,omitempty"`             // 剩余 PP 值
	Maxpp         int32                  `protobuf:"varint,4,opt,name=maxpp,proto3" json:"maxpp,omitempty"`       // 最大 PP 值
	Target        string                 `protobuf:"bytes,5,opt,name=target,proto3" json:"target,omitempty"`      // 动作目标
	Disabled      bool                   `protobuf:"varint,6,opt,name=disabled,proto3" json:"disabled,omitempty"` // 是否禁用
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActiveMove) Reset() {
	*x = ActiveMove{}
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveMove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveMove) ProtoMessage() {}

func (x *ActiveMove) ProtoReflect() protoreflect.Message {
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveMove.ProtoReflect.Descriptor instead.
func (*ActiveMove) Descriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{6}
}

func (x *ActiveMove) GetMove() string {
	if x != nil {
		return x.Move
	}
	return ""
}

func (x *ActiveMove) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ActiveMove) GetPp() int32 {
	if x != nil {
		return x.Pp
	}
	return 0
}

func (x *ActiveMove) GetMaxpp() int32 {
	if x != nil {
		return x.Maxpp
	}
	return 0
}

func (x *ActiveMove) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *ActiveMove) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

type Stats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Atk           int32                  `protobuf:"varint,1,opt,name=atk,proto3" json:"atk,omitempty"` // 攻击力
	Def           int32                  `protobuf:"varint,2,opt,name=def,proto3" json:"def,omitempty"` // 防御力
	Spa           int32                  `protobuf:"varint,3,opt,name=spa,proto3" json:"spa,omitempty"` // 特殊攻击力
	Spd           int32                  `protobuf:"varint,4,opt,name=spd,proto3" json:"spd,omitempty"` // 特殊防御力
	Spe           int32                  `protobuf:"varint,5,opt,name=spe,proto3" json:"spe,omitempty"` // 速度
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Stats) Reset() {
	*x = Stats{}
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Stats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stats) ProtoMessage() {}

func (x *Stats) ProtoReflect() protoreflect.Message {
	mi := &file_BattleServer_PackageBattleResult_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stats.ProtoReflect.Descriptor instead.
func (*Stats) Descriptor() ([]byte, []int) {
	return file_BattleServer_PackageBattleResult_proto_rawDescGZIP(), []int{7}
}

func (x *Stats) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *Stats) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *Stats) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *Stats) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *Stats) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

var File_BattleServer_PackageBattleResult_proto protoreflect.FileDescriptor

const file_BattleServer_PackageBattleResult_proto_rawDesc = "" +
	"\n" +
	"&BattleServer/PackageBattleResult.proto\x12\x15PetsBattleServerProto\"m\n" +
	"\x19PackageBattleMessageValue\x12P\n" +
	"\aactions\x18\x01 \x03(\v26.PetsBattleServerProto.PackageBattleMessageValueActionR\aactions\";\n" +
	"\x1fPackageBattleMessageValueAction\x12\x18\n" +
	"\aactions\x18\x01 \x03(\tR\aactions\"\\\n" +
	"\x14PackageBattleMessage\x12D\n" +
	"\aactions\x18\x01 \x03(\v2*.PetsBattleServerProto.PackageBattleActionR\aactions\"\xf5\x01\n" +
	"\x13PackageBattleAction\x12P\n" +
	"\x0fmajorActionType\x18\x01 \x01(\x0e2&.PetsBattleServerProto.MajorActionTypeR\x0fmajorActionType\x12D\n" +
	"\vmajorAction\x18\x03 \x01(\v2\".PetsBattleServerProto.MajorActionR\vmajorAction\x12F\n" +
	"\fminorActions\x18\x04 \x03(\v2\".PetsBattleServerProto.MinorActionR\fminorActions\"\xbf\x01\n" +
	"\vMajorAction\x12:\n" +
	"\x04type\x18\x01 \x01(\x0e2&.PetsBattleServerProto.MajorActionTypeR\x04type\x12\x14\n" +
	"\x05petId\x18\x02 \x01(\tR\x05petId\x12\x10\n" +
	"\x03loc\x18\x03 \x01(\tR\x03loc\x12\x16\n" +
	"\x06moveId\x18\x04 \x01(\tR\x06moveId\x12\x16\n" +
	"\x06target\x18\x05 \x01(\tR\x06target\x12\x1c\n" +
	"\ttargetLoc\x18\x06 \x01(\tR\ttargetLoc\"\xb0\x01\n" +
	"\vMinorAction\x12:\n" +
	"\x04type\x18\x01 \x01(\x0e2&.PetsBattleServerProto.MinorActionTypeR\x04type\x12\x14\n" +
	"\x05petId\x18\x02 \x01(\tR\x05petId\x12\x10\n" +
	"\x03loc\x18\x03 \x01(\tR\x03loc\x12!\n" +
	"\faction_value\x18\x04 \x01(\tR\vactionValue\x12\x1a\n" +
	"\bhpStatus\x18\x05 \x01(\tR\bhpStatus\"\x8a\x01\n" +
	"\n" +
	"ActiveMove\x12\x12\n" +
	"\x04move\x18\x01 \x01(\tR\x04move\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x0e\n" +
	"\x02pp\x18\x03 \x01(\x05R\x02pp\x12\x14\n" +
	"\x05maxpp\x18\x04 \x01(\x05R\x05maxpp\x12\x16\n" +
	"\x06target\x18\x05 \x01(\tR\x06target\x12\x1a\n" +
	"\bdisabled\x18\x06 \x01(\bR\bdisabled\"a\n" +
	"\x05Stats\x12\x10\n" +
	"\x03atk\x18\x01 \x01(\x05R\x03atk\x12\x10\n" +
	"\x03def\x18\x02 \x01(\x05R\x03def\x12\x10\n" +
	"\x03spa\x18\x03 \x01(\x05R\x03spa\x12\x10\n" +
	"\x03spd\x18\x04 \x01(\x05R\x03spd\x12\x10\n" +
	"\x03spe\x18\x05 \x01(\x05R\x03spe*\x9b\x01\n" +
	"\x0fMajorActionType\x12\r\n" +
	"\tmajorNone\x10\x00\x12\b\n" +
	"\x04move\x10\x01\x12\n" +
	"\n" +
	"\x06switch\x10\x02\x12\b\n" +
	"\x04drag\x10\x03\x12\x11\n" +
	"\rdetailschange\x10\x04\x12\x0f\n" +
	"\vformechange\x10\x05\x12\v\n" +
	"\areplace\x10\x06\x12\b\n" +
	"\x04swap\x10\a\x12\b\n" +
	"\x04cant\x10\b\x12\t\n" +
	"\x05faint\x10\t\x12\t\n" +
	"\x05titem\x10\n" +
	"*\xa0\x06\n" +
	"\x0fMinorActionType\x12\r\n" +
	"\tminorNone\x10\x00\x12\b\n" +
	"\x04fail\x106\x12\t\n" +
	"\x05block\x10\x01\x12\f\n" +
	"\bnotarget\x10\x02\x12\b\n" +
	"\x04miss\x10\x03\x12\n" +
	"\n" +
	"\x06damage\x10\x04\x12\b\n" +
	"\x04heal\x10\x05\x12\t\n" +
	"\x05sethp\x10\x06\x12\n" +
	"\n" +
	"\x06status\x10\a\x12\x0e\n" +
	"\n" +
	"curestatus\x10\b\x12\f\n" +
	"\bcureteam\x10\t\x12\t\n" +
	"\x05boost\x10\n" +
	"\x12\v\n" +
	"\aunboost\x10\v\x12\f\n" +
	"\bsetboost\x10\f\x12\r\n" +
	"\tswapboost\x10\r\x12\x0f\n" +
	"\vinvertboost\x10\x0e\x12\x0e\n" +
	"\n" +
	"clearboost\x10\x0f\x12\x11\n" +
	"\rclearallboost\x10\x10\x12\x16\n" +
	"\x12clearpositiveboost\x10\x11\x12\x16\n" +
	"\x12clearnegativeboost\x10\x12\x12\r\n" +
	"\tcopyboost\x10\x13\x12\v\n" +
	"\aweather\x10\x14\x12\x0e\n" +
	"\n" +
	"fieldstart\x10\x15\x12\f\n" +
	"\bfieldend\x10\x16\x12\r\n" +
	"\tsidestart\x10\x17\x12\v\n" +
	"\asideend\x10\x18\x12\x16\n" +
	"\x12swapsideconditions\x10\x19\x12\t\n" +
	"\x05start\x10\x1a\x12\a\n" +
	"\x03end\x10\x1b\x12\b\n" +
	"\x04crit\x10\x1c\x12\x12\n" +
	"\x0esupereffective\x10\x1d\x12\f\n" +
	"\bresisted\x10\x1e\x12\n" +
	"\n" +
	"\x06immune\x10\x1f\x12\b\n" +
	"\x04item\x10 \x12\v\n" +
	"\aenditem\x10!\x12\v\n" +
	"\aability\x10\"\x12\x0e\n" +
	"\n" +
	"endability\x10#\x12\r\n" +
	"\ttransform\x10$\x12\b\n" +
	"\x04mega\x10%\x12\n" +
	"\n" +
	"\x06primal\x10&\x12\t\n" +
	"\x05burst\x10'\x12\n" +
	"\n" +
	"\x06zpower\x10(\x12\v\n" +
	"\azbroken\x10)\x12\f\n" +
	"\bactivate\x10*\x12\b\n" +
	"\x04hint\x10+\x12\n" +
	"\n" +
	"\x06center\x10,\x12\v\n" +
	"\amessage\x10-\x12\v\n" +
	"\acombine\x10.\x12\v\n" +
	"\awaiting\x10/\x12\v\n" +
	"\aprepare\x100\x12\x10\n" +
	"\fmustrecharge\x101\x12\v\n" +
	"\anothing\x102\x12\f\n" +
	"\bhitcount\x103\x12\x0e\n" +
	"\n" +
	"singlemove\x104\x12\x0e\n" +
	"\n" +
	"singleturn\x105\x12\x11\n" +
	"\rfieldactivate\x107B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_BattleServer_PackageBattleResult_proto_rawDescOnce sync.Once
	file_BattleServer_PackageBattleResult_proto_rawDescData []byte
)

func file_BattleServer_PackageBattleResult_proto_rawDescGZIP() []byte {
	file_BattleServer_PackageBattleResult_proto_rawDescOnce.Do(func() {
		file_BattleServer_PackageBattleResult_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_BattleServer_PackageBattleResult_proto_rawDesc), len(file_BattleServer_PackageBattleResult_proto_rawDesc)))
	})
	return file_BattleServer_PackageBattleResult_proto_rawDescData
}

var file_BattleServer_PackageBattleResult_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_BattleServer_PackageBattleResult_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_BattleServer_PackageBattleResult_proto_goTypes = []any{
	(MajorActionType)(0),                    // 0: PetsBattleServerProto.MajorActionType
	(MinorActionType)(0),                    // 1: PetsBattleServerProto.MinorActionType
	(*PackageBattleMessageValue)(nil),       // 2: PetsBattleServerProto.PackageBattleMessageValue
	(*PackageBattleMessageValueAction)(nil), // 3: PetsBattleServerProto.PackageBattleMessageValueAction
	(*PackageBattleMessage)(nil),            // 4: PetsBattleServerProto.PackageBattleMessage
	(*PackageBattleAction)(nil),             // 5: PetsBattleServerProto.PackageBattleAction
	(*MajorAction)(nil),                     // 6: PetsBattleServerProto.MajorAction
	(*MinorAction)(nil),                     // 7: PetsBattleServerProto.MinorAction
	(*ActiveMove)(nil),                      // 8: PetsBattleServerProto.ActiveMove
	(*Stats)(nil),                           // 9: PetsBattleServerProto.Stats
}
var file_BattleServer_PackageBattleResult_proto_depIdxs = []int32{
	3, // 0: PetsBattleServerProto.PackageBattleMessageValue.actions:type_name -> PetsBattleServerProto.PackageBattleMessageValueAction
	5, // 1: PetsBattleServerProto.PackageBattleMessage.actions:type_name -> PetsBattleServerProto.PackageBattleAction
	0, // 2: PetsBattleServerProto.PackageBattleAction.majorActionType:type_name -> PetsBattleServerProto.MajorActionType
	6, // 3: PetsBattleServerProto.PackageBattleAction.majorAction:type_name -> PetsBattleServerProto.MajorAction
	7, // 4: PetsBattleServerProto.PackageBattleAction.minorActions:type_name -> PetsBattleServerProto.MinorAction
	0, // 5: PetsBattleServerProto.MajorAction.type:type_name -> PetsBattleServerProto.MajorActionType
	1, // 6: PetsBattleServerProto.MinorAction.type:type_name -> PetsBattleServerProto.MinorActionType
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_BattleServer_PackageBattleResult_proto_init() }
func file_BattleServer_PackageBattleResult_proto_init() {
	if File_BattleServer_PackageBattleResult_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_BattleServer_PackageBattleResult_proto_rawDesc), len(file_BattleServer_PackageBattleResult_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_BattleServer_PackageBattleResult_proto_goTypes,
		DependencyIndexes: file_BattleServer_PackageBattleResult_proto_depIdxs,
		EnumInfos:         file_BattleServer_PackageBattleResult_proto_enumTypes,
		MessageInfos:      file_BattleServer_PackageBattleResult_proto_msgTypes,
	}.Build()
	File_BattleServer_PackageBattleResult_proto = out.File
	file_BattleServer_PackageBattleResult_proto_goTypes = nil
	file_BattleServer_PackageBattleResult_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/ServerNotification.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerNotificationType int32

const (
	ServerNotificationType_ServerNotificationType_None          ServerNotificationType = 0
	ServerNotificationType_ServerNotificationType_NewVersion    ServerNotificationType = 1
	ServerNotificationType_ServerNotificationType_InviteBattle  ServerNotificationType = 2
	ServerNotificationType_ServerNotificationType_MatchJoin     ServerNotificationType = 3
	ServerNotificationType_ServerNotificationType_SwopInfo      ServerNotificationType = 4
	ServerNotificationType_ServerNotificationType_UpdateTrainer ServerNotificationType = 5
	ServerNotificationType_ServerNotificationType_BattleMessage ServerNotificationType = 100
	ServerNotificationType_ServerNotificationType_BattlePrepare ServerNotificationType = 101
	ServerNotificationType_ServerNotificationType_BattleInit    ServerNotificationType = 102
	ServerNotificationType_ServerNotificationType_BattleChoice  ServerNotificationType = 103
	ServerNotificationType_ServerNotificationType_BattleResult  ServerNotificationType = 104
	ServerNotificationType_ServerNotificationType_BattleUrge    ServerNotificationType = 105
	ServerNotificationType_ServerNotificationType_NewEmail      ServerNotificationType = 200
)

// Enum value maps for ServerNotificationType.
var (
	ServerNotificationType_name = map[int32]string{
		0:   "ServerNotificationType_None",
		1:   "ServerNotificationType_NewVersion",
		2:   "ServerNotificationType_InviteBattle",
		3:   "ServerNotificationType_MatchJoin",
		4:   "ServerNotificationType_SwopInfo",
		5:   "ServerNotificationType_UpdateTrainer",
		100: "ServerNotificationType_BattleMessage",
		101: "ServerNotificationType_BattlePrepare",
		102: "ServerNotificationType_BattleInit",
		103: "ServerNotificationType_BattleChoice",
		104: "ServerNotificationType_BattleResult",
		105: "ServerNotificationType_BattleUrge",
		200: "ServerNotificationType_NewEmail",
	}
	ServerNotificationType_value = map[string]int32{
		"ServerNotificationType_None":          0,
		"ServerNotificationType_NewVersion":    1,
		"ServerNotificationType_InviteBattle":  2,
		"ServerNotificationType_MatchJoin":     3,
		"ServerNotificationType_SwopInfo":      4,
		"ServerNotificationType_UpdateTrainer": 5,
		"ServerNotificationType_BattleMessage": 100,
		"ServerNotificationType_BattlePrepare": 101,
		"ServerNotificationType_BattleInit":    102,
		"ServerNotificationType_BattleChoice":  103,
		"ServerNotificationType_BattleResult":  104,
		"ServerNotificationType_BattleUrge":    105,
		"ServerNotificationType_NewEmail":      200,
	}
)

func (x ServerNotificationType) Enum() *ServerNotificationType {
	p := new(ServerNotificationType)
	*p = x
	return p
}

func (x ServerNotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServerNotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerNotification_proto_enumTypes[0].Descriptor()
}

func (ServerNotificationType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerNotification_proto_enumTypes[0]
}

func (x ServerNotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServerNotificationType.Descriptor instead.
func (ServerNotificationType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{0}
}

type InviteBattleType int32

const (
	InviteBattleType_InviteBattleType_None   InviteBattleType = 0
	InviteBattleType_InviteBattleType_Normal InviteBattleType = 1
	InviteBattleType_InviteBattleType_Force  InviteBattleType = 2
)

// Enum value maps for InviteBattleType.
var (
	InviteBattleType_name = map[int32]string{
		0: "InviteBattleType_None",
		1: "InviteBattleType_Normal",
		2: "InviteBattleType_Force",
	}
	InviteBattleType_value = map[string]int32{
		"InviteBattleType_None":   0,
		"InviteBattleType_Normal": 1,
		"InviteBattleType_Force":  2,
	}
)

func (x InviteBattleType) Enum() *InviteBattleType {
	p := new(InviteBattleType)
	*p = x
	return p
}

func (x InviteBattleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InviteBattleType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_ServerNotification_proto_enumTypes[1].Descriptor()
}

func (InviteBattleType) Type() protoreflect.EnumType {
	return &file_MainServer_ServerNotification_proto_enumTypes[1]
}

func (x InviteBattleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InviteBattleType.Descriptor instead.
func (InviteBattleType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{1}
}

type InviteBattleNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proposer      *Trainer               `protobuf:"bytes,1,opt,name=proposer,proto3" json:"proposer,omitempty"`
	InviteType    InviteBattleType       `protobuf:"varint,2,opt,name=inviteType,proto3,enum=MainServer.InviteBattleType" json:"inviteType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InviteBattleNotification) Reset() {
	*x = InviteBattleNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InviteBattleNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviteBattleNotification) ProtoMessage() {}

func (x *InviteBattleNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviteBattleNotification.ProtoReflect.Descriptor instead.
func (*InviteBattleNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{0}
}

func (x *InviteBattleNotification) GetProposer() *Trainer {
	if x != nil {
		return x.Proposer
	}
	return nil
}

func (x *InviteBattleNotification) GetInviteType() InviteBattleType {
	if x != nil {
		return x.InviteType
	}
	return InviteBattleType_InviteBattleType_None
}

type MatchJoinNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MatchId       string                 `protobuf:"bytes,1,opt,name=matchId,proto3" json:"matchId,omitempty"`
	Proposer      *Trainer               `protobuf:"bytes,2,opt,name=proposer,proto3" json:"proposer,omitempty"`
	Target        *Trainer               `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MatchJoinNotification) Reset() {
	*x = MatchJoinNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MatchJoinNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchJoinNotification) ProtoMessage() {}

func (x *MatchJoinNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchJoinNotification.ProtoReflect.Descriptor instead.
func (*MatchJoinNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{1}
}

func (x *MatchJoinNotification) GetMatchId() string {
	if x != nil {
		return x.MatchId
	}
	return ""
}

func (x *MatchJoinNotification) GetProposer() *Trainer {
	if x != nil {
		return x.Proposer
	}
	return nil
}

func (x *MatchJoinNotification) GetTarget() *Trainer {
	if x != nil {
		return x.Target
	}
	return nil
}

type SwopInfoNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Swop          *SwopInfo              `protobuf:"bytes,1,opt,name=swop,proto3" json:"swop,omitempty"`
	Sender        *Trainer               `protobuf:"bytes,2,opt,name=sender,proto3" json:"sender,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwopInfoNotification) Reset() {
	*x = SwopInfoNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwopInfoNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopInfoNotification) ProtoMessage() {}

func (x *SwopInfoNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopInfoNotification.ProtoReflect.Descriptor instead.
func (*SwopInfoNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{2}
}

func (x *SwopInfoNotification) GetSwop() *SwopInfo {
	if x != nil {
		return x.Swop
	}
	return nil
}

func (x *SwopInfoNotification) GetSender() *Trainer {
	if x != nil {
		return x.Sender
	}
	return nil
}

type UpdateTrainerNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Trainer       *Trainer               `protobuf:"bytes,1,opt,name=trainer,proto3" json:"trainer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTrainerNotification) Reset() {
	*x = UpdateTrainerNotification{}
	mi := &file_MainServer_ServerNotification_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTrainerNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTrainerNotification) ProtoMessage() {}

func (x *UpdateTrainerNotification) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_ServerNotification_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTrainerNotification.ProtoReflect.Descriptor instead.
func (*UpdateTrainerNotification) Descriptor() ([]byte, []int) {
	return file_MainServer_ServerNotification_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateTrainerNotification) GetTrainer() *Trainer {
	if x != nil {
		return x.Trainer
	}
	return nil
}

var File_MainServer_ServerNotification_proto protoreflect.FileDescriptor

const file_MainServer_ServerNotification_proto_rawDesc = "" +
	"\n" +
	"#MainServer/ServerNotification.proto\x12\n" +
	"MainServer\x1a\x18MainServer/Trainer.proto\x1a\x15MainServer/Swop.proto\"\x89\x01\n" +
	"\x18InviteBattleNotification\x12/\n" +
	"\bproposer\x18\x01 \x01(\v2\x13.MainServer.TrainerR\bproposer\x12<\n" +
	"\n" +
	"inviteType\x18\x02 \x01(\x0e2\x1c.MainServer.InviteBattleTypeR\n" +
	"inviteType\"\x8f\x01\n" +
	"\x15MatchJoinNotification\x12\x18\n" +
	"\amatchId\x18\x01 \x01(\tR\amatchId\x12/\n" +
	"\bproposer\x18\x02 \x01(\v2\x13.MainServer.TrainerR\bproposer\x12+\n" +
	"\x06target\x18\x03 \x01(\v2\x13.MainServer.TrainerR\x06target\"m\n" +
	"\x14SwopInfoNotification\x12(\n" +
	"\x04swop\x18\x01 \x01(\v2\x14.MainServer.SwopInfoR\x04swop\x12+\n" +
	"\x06sender\x18\x02 \x01(\v2\x13.MainServer.TrainerR\x06sender\"J\n" +
	"\x19UpdateTrainerNotification\x12-\n" +
	"\atrainer\x18\x01 \x01(\v2\x13.MainServer.TrainerR\atrainer*\x98\x04\n" +
	"\x16ServerNotificationType\x12\x1f\n" +
	"\x1bServerNotificationType_None\x10\x00\x12%\n" +
	"!ServerNotificationType_NewVersion\x10\x01\x12'\n" +
	"#ServerNotificationType_InviteBattle\x10\x02\x12$\n" +
	" ServerNotificationType_MatchJoin\x10\x03\x12#\n" +
	"\x1fServerNotificationType_SwopInfo\x10\x04\x12(\n" +
	"$ServerNotificationType_UpdateTrainer\x10\x05\x12(\n" +
	"$ServerNotificationType_BattleMessage\x10d\x12(\n" +
	"$ServerNotificationType_BattlePrepare\x10e\x12%\n" +
	"!ServerNotificationType_BattleInit\x10f\x12'\n" +
	"#ServerNotificationType_BattleChoice\x10g\x12'\n" +
	"#ServerNotificationType_BattleResult\x10h\x12%\n" +
	"!ServerNotificationType_BattleUrge\x10i\x12$\n" +
	"\x1fServerNotificationType_NewEmail\x10\xc8\x01*f\n" +
	"\x10InviteBattleType\x12\x19\n" +
	"\x15InviteBattleType_None\x10\x00\x12\x1b\n" +
	"\x17InviteBattleType_Normal\x10\x01\x12\x1a\n" +
	"\x16InviteBattleType_Force\x10\x02B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_ServerNotification_proto_rawDescOnce sync.Once
	file_MainServer_ServerNotification_proto_rawDescData []byte
)

func file_MainServer_ServerNotification_proto_rawDescGZIP() []byte {
	file_MainServer_ServerNotification_proto_rawDescOnce.Do(func() {
		file_MainServer_ServerNotification_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_ServerNotification_proto_rawDesc), len(file_MainServer_ServerNotification_proto_rawDesc)))
	})
	return file_MainServer_ServerNotification_proto_rawDescData
}

var file_MainServer_ServerNotification_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_MainServer_ServerNotification_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_MainServer_ServerNotification_proto_goTypes = []any{
	(ServerNotificationType)(0),       // 0: MainServer.ServerNotificationType
	(InviteBattleType)(0),             // 1: MainServer.InviteBattleType
	(*InviteBattleNotification)(nil),  // 2: MainServer.InviteBattleNotification
	(*MatchJoinNotification)(nil),     // 3: MainServer.MatchJoinNotification
	(*SwopInfoNotification)(nil),      // 4: MainServer.SwopInfoNotification
	(*UpdateTrainerNotification)(nil), // 5: MainServer.UpdateTrainerNotification
	(*Trainer)(nil),                   // 6: MainServer.Trainer
	(*SwopInfo)(nil),                  // 7: MainServer.SwopInfo
}
var file_MainServer_ServerNotification_proto_depIdxs = []int32{
	6, // 0: MainServer.InviteBattleNotification.proposer:type_name -> MainServer.Trainer
	1, // 1: MainServer.InviteBattleNotification.inviteType:type_name -> MainServer.InviteBattleType
	6, // 2: MainServer.MatchJoinNotification.proposer:type_name -> MainServer.Trainer
	6, // 3: MainServer.MatchJoinNotification.target:type_name -> MainServer.Trainer
	7, // 4: MainServer.SwopInfoNotification.swop:type_name -> MainServer.SwopInfo
	6, // 5: MainServer.SwopInfoNotification.sender:type_name -> MainServer.Trainer
	6, // 6: MainServer.UpdateTrainerNotification.trainer:type_name -> MainServer.Trainer
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_MainServer_ServerNotification_proto_init() }
func file_MainServer_ServerNotification_proto_init() {
	if File_MainServer_ServerNotification_proto != nil {
		return
	}
	file_MainServer_Trainer_proto_init()
	file_MainServer_Swop_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_ServerNotification_proto_rawDesc), len(file_MainServer_ServerNotification_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_ServerNotification_proto_goTypes,
		DependencyIndexes: file_MainServer_ServerNotification_proto_depIdxs,
		EnumInfos:         file_MainServer_ServerNotification_proto_enumTypes,
		MessageInfos:      file_MainServer_ServerNotification_proto_msgTypes,
	}.Build()
	File_MainServer_ServerNotification_proto = out.File
	file_MainServer_ServerNotification_proto_goTypes = nil
	file_MainServer_ServerNotification_proto_depIdxs = nil
}

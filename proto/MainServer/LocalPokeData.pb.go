// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/LocalPokeData.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LocalPokeData struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	NameId        string                          `protobuf:"bytes,1,opt,name=name_id,json=nameId,proto3" json:"name_id,omitempty"`
	PsData        *PSPokemonData                  `protobuf:"bytes,2,opt,name=psData,proto3" json:"psData,omitempty"`
	Localization  *PokeDataLocalizationStoreValue `protobuf:"bytes,3,opt,name=localization,proto3" json:"localization,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalPokeData) Reset() {
	*x = LocalPokeData{}
	mi := &file_MainServer_LocalPokeData_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalPokeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalPokeData) ProtoMessage() {}

func (x *LocalPokeData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocalPokeData_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalPokeData.ProtoReflect.Descriptor instead.
func (*LocalPokeData) Descriptor() ([]byte, []int) {
	return file_MainServer_LocalPokeData_proto_rawDescGZIP(), []int{0}
}

func (x *LocalPokeData) GetNameId() string {
	if x != nil {
		return x.NameId
	}
	return ""
}

func (x *LocalPokeData) GetPsData() *PSPokemonData {
	if x != nil {
		return x.PsData
	}
	return nil
}

func (x *LocalPokeData) GetLocalization() *PokeDataLocalizationStoreValue {
	if x != nil {
		return x.Localization
	}
	return nil
}

type LocalMoveData struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	NameId        string                          `protobuf:"bytes,1,opt,name=name_id,json=nameId,proto3" json:"name_id,omitempty"`
	PsData        *PSMMoveData                    `protobuf:"bytes,2,opt,name=psData,proto3" json:"psData,omitempty"`
	Localization  *PokeDataLocalizationStoreValue `protobuf:"bytes,3,opt,name=localization,proto3" json:"localization,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalMoveData) Reset() {
	*x = LocalMoveData{}
	mi := &file_MainServer_LocalPokeData_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalMoveData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalMoveData) ProtoMessage() {}

func (x *LocalMoveData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_LocalPokeData_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalMoveData.ProtoReflect.Descriptor instead.
func (*LocalMoveData) Descriptor() ([]byte, []int) {
	return file_MainServer_LocalPokeData_proto_rawDescGZIP(), []int{1}
}

func (x *LocalMoveData) GetNameId() string {
	if x != nil {
		return x.NameId
	}
	return ""
}

func (x *LocalMoveData) GetPsData() *PSMMoveData {
	if x != nil {
		return x.PsData
	}
	return nil
}

func (x *LocalMoveData) GetLocalization() *PokeDataLocalizationStoreValue {
	if x != nil {
		return x.Localization
	}
	return nil
}

var File_MainServer_LocalPokeData_proto protoreflect.FileDescriptor

const file_MainServer_LocalPokeData_proto_rawDesc = "" +
	"\n" +
	"\x1eMainServer/LocalPokeData.proto\x12\n" +
	"MainServer\x1a\x18MainServer/Pokedex.proto\x1a\x17MainServer/PSMove.proto\x1a\x19MainServer/PokeName.proto\"\xab\x01\n" +
	"\rLocalPokeData\x12\x17\n" +
	"\aname_id\x18\x01 \x01(\tR\x06nameId\x121\n" +
	"\x06psData\x18\x02 \x01(\v2\x19.MainServer.PSPokemonDataR\x06psData\x12N\n" +
	"\flocalization\x18\x03 \x01(\v2*.MainServer.PokeDataLocalizationStoreValueR\flocalization\"\xa9\x01\n" +
	"\rLocalMoveData\x12\x17\n" +
	"\aname_id\x18\x01 \x01(\tR\x06nameId\x12/\n" +
	"\x06psData\x18\x02 \x01(\v2\x17.MainServer.PSMMoveDataR\x06psData\x12N\n" +
	"\flocalization\x18\x03 \x01(\v2*.MainServer.PokeDataLocalizationStoreValueR\flocalizationB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_LocalPokeData_proto_rawDescOnce sync.Once
	file_MainServer_LocalPokeData_proto_rawDescData []byte
)

func file_MainServer_LocalPokeData_proto_rawDescGZIP() []byte {
	file_MainServer_LocalPokeData_proto_rawDescOnce.Do(func() {
		file_MainServer_LocalPokeData_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_LocalPokeData_proto_rawDesc), len(file_MainServer_LocalPokeData_proto_rawDesc)))
	})
	return file_MainServer_LocalPokeData_proto_rawDescData
}

var file_MainServer_LocalPokeData_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_MainServer_LocalPokeData_proto_goTypes = []any{
	(*LocalPokeData)(nil),                  // 0: MainServer.LocalPokeData
	(*LocalMoveData)(nil),                  // 1: MainServer.LocalMoveData
	(*PSPokemonData)(nil),                  // 2: MainServer.PSPokemonData
	(*PokeDataLocalizationStoreValue)(nil), // 3: MainServer.PokeDataLocalizationStoreValue
	(*PSMMoveData)(nil),                    // 4: MainServer.PSMMoveData
}
var file_MainServer_LocalPokeData_proto_depIdxs = []int32{
	2, // 0: MainServer.LocalPokeData.psData:type_name -> MainServer.PSPokemonData
	3, // 1: MainServer.LocalPokeData.localization:type_name -> MainServer.PokeDataLocalizationStoreValue
	4, // 2: MainServer.LocalMoveData.psData:type_name -> MainServer.PSMMoveData
	3, // 3: MainServer.LocalMoveData.localization:type_name -> MainServer.PokeDataLocalizationStoreValue
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_MainServer_LocalPokeData_proto_init() }
func file_MainServer_LocalPokeData_proto_init() {
	if File_MainServer_LocalPokeData_proto != nil {
		return
	}
	file_MainServer_Pokedex_proto_init()
	file_MainServer_PSMove_proto_init()
	file_MainServer_PokeName_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_LocalPokeData_proto_rawDesc), len(file_MainServer_LocalPokeData_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_LocalPokeData_proto_goTypes,
		DependencyIndexes: file_MainServer_LocalPokeData_proto_depIdxs,
		MessageInfos:      file_MainServer_LocalPokeData_proto_msgTypes,
	}.Build()
	File_MainServer_LocalPokeData_proto = out.File
	file_MainServer_LocalPokeData_proto_goTypes = nil
	file_MainServer_LocalPokeData_proto_depIdxs = nil
}

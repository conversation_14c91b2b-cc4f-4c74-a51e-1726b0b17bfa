// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Pokedex.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PSPokemonDataMap struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Data          map[string]*PSPokemonData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSPokemonDataMap) Reset() {
	*x = PSPokemonDataMap{}
	mi := &file_MainServer_Pokedex_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSPokemonDataMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSPokemonDataMap) ProtoMessage() {}

func (x *PSPokemonDataMap) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSPokemonDataMap.ProtoReflect.Descriptor instead.
func (*PSPokemonDataMap) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{0}
}

func (x *PSPokemonDataMap) GetData() map[string]*PSPokemonData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PSPokemonData struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BaseForme       string                 `protobuf:"bytes,1,opt,name=baseForme,proto3" json:"baseForme,omitempty"`
	BaseSpecies     string                 `protobuf:"bytes,2,opt,name=baseSpecies,proto3" json:"baseSpecies,omitempty"`
	BattleOnly      []string               `protobuf:"bytes,3,rep,name=battleOnly,proto3" json:"battleOnly,omitempty"`
	CanGigantamax   string                 `protobuf:"bytes,4,opt,name=canGigantamax,proto3" json:"canGigantamax,omitempty"`
	CanHatch        bool                   `protobuf:"varint,5,opt,name=canHatch,proto3" json:"canHatch,omitempty"`
	CannotDynamax   bool                   `protobuf:"varint,6,opt,name=cannotDynamax,proto3" json:"cannotDynamax,omitempty"`
	ChangesFrom     string                 `protobuf:"bytes,7,opt,name=changesFrom,proto3" json:"changesFrom,omitempty"`
	Color           string                 `protobuf:"bytes,8,opt,name=color,proto3" json:"color,omitempty"`
	CosmeticFormes  []string               `protobuf:"bytes,9,rep,name=cosmeticFormes,proto3" json:"cosmeticFormes,omitempty"`
	EggGroups       []string               `protobuf:"bytes,10,rep,name=eggGroups,proto3" json:"eggGroups,omitempty"`
	EvoCondition    string                 `protobuf:"bytes,11,opt,name=evoCondition,proto3" json:"evoCondition,omitempty"`
	EvoItem         string                 `protobuf:"bytes,12,opt,name=evoItem,proto3" json:"evoItem,omitempty"`
	EvoLevel        int32                  `protobuf:"varint,13,opt,name=evoLevel,proto3" json:"evoLevel,omitempty"`
	EvoMove         string                 `protobuf:"bytes,14,opt,name=evoMove,proto3" json:"evoMove,omitempty"`
	EvoRegion       string                 `protobuf:"bytes,15,opt,name=evoRegion,proto3" json:"evoRegion,omitempty"`
	EvoType         string                 `protobuf:"bytes,16,opt,name=evoType,proto3" json:"evoType,omitempty"`
	Evos            []string               `protobuf:"bytes,17,rep,name=evos,proto3" json:"evos,omitempty"`
	ForceTeraType   string                 `protobuf:"bytes,18,opt,name=forceTeraType,proto3" json:"forceTeraType,omitempty"`
	Forme           string                 `protobuf:"bytes,19,opt,name=forme,proto3" json:"forme,omitempty"`
	FormeOrder      []string               `protobuf:"bytes,20,rep,name=formeOrder,proto3" json:"formeOrder,omitempty"`
	Gen             int32                  `protobuf:"varint,21,opt,name=gen,proto3" json:"gen,omitempty"`
	Gender          string                 `protobuf:"bytes,22,opt,name=gender,proto3" json:"gender,omitempty"`
	Heightm         float32                `protobuf:"fixed32,23,opt,name=heightm,proto3" json:"heightm,omitempty"`
	IsNonstandard   string                 `protobuf:"bytes,24,opt,name=isNonstandard,proto3" json:"isNonstandard,omitempty"`
	MaxHP           int32                  `protobuf:"varint,25,opt,name=maxHP,proto3" json:"maxHP,omitempty"`
	Mother          string                 `protobuf:"bytes,26,opt,name=mother,proto3" json:"mother,omitempty"`
	Name            string                 `protobuf:"bytes,27,opt,name=name,proto3" json:"name,omitempty"`
	Num             int32                  `protobuf:"varint,28,opt,name=num,proto3" json:"num,omitempty"`
	OtherFormes     []string               `protobuf:"bytes,29,rep,name=otherFormes,proto3" json:"otherFormes,omitempty"`
	Prevo           string                 `protobuf:"bytes,30,opt,name=prevo,proto3" json:"prevo,omitempty"`
	RequiredAbility string                 `protobuf:"bytes,31,opt,name=requiredAbility,proto3" json:"requiredAbility,omitempty"`
	RequiredItem    string                 `protobuf:"bytes,32,opt,name=requiredItem,proto3" json:"requiredItem,omitempty"`
	RequiredItems   []string               `protobuf:"bytes,33,rep,name=requiredItems,proto3" json:"requiredItems,omitempty"`
	RequiredMove    string                 `protobuf:"bytes,34,opt,name=requiredMove,proto3" json:"requiredMove,omitempty"`
	Tags            []string               `protobuf:"bytes,35,rep,name=tags,proto3" json:"tags,omitempty"`
	Tier            string                 `protobuf:"bytes,36,opt,name=tier,proto3" json:"tier,omitempty"`
	Types           []string               `protobuf:"bytes,37,rep,name=types,proto3" json:"types,omitempty"`
	Weightkg        float32                `protobuf:"fixed32,38,opt,name=weightkg,proto3" json:"weightkg,omitempty"`
	CaptureRate     int32                  `protobuf:"varint,39,opt,name=captureRate,proto3" json:"captureRate,omitempty"`
	GrowthRate      string                 `protobuf:"bytes,40,opt,name=growthRate,proto3" json:"growthRate,omitempty"`
	FormsSwitchable bool                   `protobuf:"varint,41,opt,name=formsSwitchable,proto3" json:"formsSwitchable,omitempty"`
	HatchCounter    int32                  `protobuf:"varint,42,opt,name=hatchCounter,proto3" json:"hatchCounter,omitempty"`
	Abilities       *PSAbilities           `protobuf:"bytes,43,opt,name=abilities,proto3" json:"abilities,omitempty"`
	BaseStats       *PSBasestats           `protobuf:"bytes,44,opt,name=baseStats,proto3" json:"baseStats,omitempty"`
	GenderRatio     *PSGenderratio         `protobuf:"bytes,45,opt,name=genderRatio,proto3" json:"genderRatio,omitempty"`
	Efforts         *PSBasestats           `protobuf:"bytes,46,opt,name=efforts,proto3" json:"efforts,omitempty"`
	BaseExperience  int32                  `protobuf:"varint,47,opt,name=baseExperience,proto3" json:"baseExperience,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PSPokemonData) Reset() {
	*x = PSPokemonData{}
	mi := &file_MainServer_Pokedex_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSPokemonData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSPokemonData) ProtoMessage() {}

func (x *PSPokemonData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSPokemonData.ProtoReflect.Descriptor instead.
func (*PSPokemonData) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{1}
}

func (x *PSPokemonData) GetBaseForme() string {
	if x != nil {
		return x.BaseForme
	}
	return ""
}

func (x *PSPokemonData) GetBaseSpecies() string {
	if x != nil {
		return x.BaseSpecies
	}
	return ""
}

func (x *PSPokemonData) GetBattleOnly() []string {
	if x != nil {
		return x.BattleOnly
	}
	return nil
}

func (x *PSPokemonData) GetCanGigantamax() string {
	if x != nil {
		return x.CanGigantamax
	}
	return ""
}

func (x *PSPokemonData) GetCanHatch() bool {
	if x != nil {
		return x.CanHatch
	}
	return false
}

func (x *PSPokemonData) GetCannotDynamax() bool {
	if x != nil {
		return x.CannotDynamax
	}
	return false
}

func (x *PSPokemonData) GetChangesFrom() string {
	if x != nil {
		return x.ChangesFrom
	}
	return ""
}

func (x *PSPokemonData) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *PSPokemonData) GetCosmeticFormes() []string {
	if x != nil {
		return x.CosmeticFormes
	}
	return nil
}

func (x *PSPokemonData) GetEggGroups() []string {
	if x != nil {
		return x.EggGroups
	}
	return nil
}

func (x *PSPokemonData) GetEvoCondition() string {
	if x != nil {
		return x.EvoCondition
	}
	return ""
}

func (x *PSPokemonData) GetEvoItem() string {
	if x != nil {
		return x.EvoItem
	}
	return ""
}

func (x *PSPokemonData) GetEvoLevel() int32 {
	if x != nil {
		return x.EvoLevel
	}
	return 0
}

func (x *PSPokemonData) GetEvoMove() string {
	if x != nil {
		return x.EvoMove
	}
	return ""
}

func (x *PSPokemonData) GetEvoRegion() string {
	if x != nil {
		return x.EvoRegion
	}
	return ""
}

func (x *PSPokemonData) GetEvoType() string {
	if x != nil {
		return x.EvoType
	}
	return ""
}

func (x *PSPokemonData) GetEvos() []string {
	if x != nil {
		return x.Evos
	}
	return nil
}

func (x *PSPokemonData) GetForceTeraType() string {
	if x != nil {
		return x.ForceTeraType
	}
	return ""
}

func (x *PSPokemonData) GetForme() string {
	if x != nil {
		return x.Forme
	}
	return ""
}

func (x *PSPokemonData) GetFormeOrder() []string {
	if x != nil {
		return x.FormeOrder
	}
	return nil
}

func (x *PSPokemonData) GetGen() int32 {
	if x != nil {
		return x.Gen
	}
	return 0
}

func (x *PSPokemonData) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *PSPokemonData) GetHeightm() float32 {
	if x != nil {
		return x.Heightm
	}
	return 0
}

func (x *PSPokemonData) GetIsNonstandard() string {
	if x != nil {
		return x.IsNonstandard
	}
	return ""
}

func (x *PSPokemonData) GetMaxHP() int32 {
	if x != nil {
		return x.MaxHP
	}
	return 0
}

func (x *PSPokemonData) GetMother() string {
	if x != nil {
		return x.Mother
	}
	return ""
}

func (x *PSPokemonData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PSPokemonData) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *PSPokemonData) GetOtherFormes() []string {
	if x != nil {
		return x.OtherFormes
	}
	return nil
}

func (x *PSPokemonData) GetPrevo() string {
	if x != nil {
		return x.Prevo
	}
	return ""
}

func (x *PSPokemonData) GetRequiredAbility() string {
	if x != nil {
		return x.RequiredAbility
	}
	return ""
}

func (x *PSPokemonData) GetRequiredItem() string {
	if x != nil {
		return x.RequiredItem
	}
	return ""
}

func (x *PSPokemonData) GetRequiredItems() []string {
	if x != nil {
		return x.RequiredItems
	}
	return nil
}

func (x *PSPokemonData) GetRequiredMove() string {
	if x != nil {
		return x.RequiredMove
	}
	return ""
}

func (x *PSPokemonData) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *PSPokemonData) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *PSPokemonData) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *PSPokemonData) GetWeightkg() float32 {
	if x != nil {
		return x.Weightkg
	}
	return 0
}

func (x *PSPokemonData) GetCaptureRate() int32 {
	if x != nil {
		return x.CaptureRate
	}
	return 0
}

func (x *PSPokemonData) GetGrowthRate() string {
	if x != nil {
		return x.GrowthRate
	}
	return ""
}

func (x *PSPokemonData) GetFormsSwitchable() bool {
	if x != nil {
		return x.FormsSwitchable
	}
	return false
}

func (x *PSPokemonData) GetHatchCounter() int32 {
	if x != nil {
		return x.HatchCounter
	}
	return 0
}

func (x *PSPokemonData) GetAbilities() *PSAbilities {
	if x != nil {
		return x.Abilities
	}
	return nil
}

func (x *PSPokemonData) GetBaseStats() *PSBasestats {
	if x != nil {
		return x.BaseStats
	}
	return nil
}

func (x *PSPokemonData) GetGenderRatio() *PSGenderratio {
	if x != nil {
		return x.GenderRatio
	}
	return nil
}

func (x *PSPokemonData) GetEfforts() *PSBasestats {
	if x != nil {
		return x.Efforts
	}
	return nil
}

func (x *PSPokemonData) GetBaseExperience() int32 {
	if x != nil {
		return x.BaseExperience
	}
	return 0
}

type PSAbilities struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	H             string                 `protobuf:"bytes,1,opt,name=H,proto3" json:"H,omitempty"`
	S             string                 `protobuf:"bytes,2,opt,name=S,proto3" json:"S,omitempty"`
	Key0          string                 `protobuf:"bytes,3,opt,name=key0,proto3" json:"key0,omitempty"`
	Key1          string                 `protobuf:"bytes,4,opt,name=key1,proto3" json:"key1,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSAbilities) Reset() {
	*x = PSAbilities{}
	mi := &file_MainServer_Pokedex_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSAbilities) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSAbilities) ProtoMessage() {}

func (x *PSAbilities) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSAbilities.ProtoReflect.Descriptor instead.
func (*PSAbilities) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{2}
}

func (x *PSAbilities) GetH() string {
	if x != nil {
		return x.H
	}
	return ""
}

func (x *PSAbilities) GetS() string {
	if x != nil {
		return x.S
	}
	return ""
}

func (x *PSAbilities) GetKey0() string {
	if x != nil {
		return x.Key0
	}
	return ""
}

func (x *PSAbilities) GetKey1() string {
	if x != nil {
		return x.Key1
	}
	return ""
}

type PSBasestats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Atk           int32                  `protobuf:"varint,1,opt,name=atk,proto3" json:"atk,omitempty"`
	Def           int32                  `protobuf:"varint,2,opt,name=def,proto3" json:"def,omitempty"`
	Hp            int32                  `protobuf:"varint,3,opt,name=hp,proto3" json:"hp,omitempty"`
	Spa           int32                  `protobuf:"varint,4,opt,name=spa,proto3" json:"spa,omitempty"`
	Spd           int32                  `protobuf:"varint,5,opt,name=spd,proto3" json:"spd,omitempty"`
	Spe           int32                  `protobuf:"varint,6,opt,name=spe,proto3" json:"spe,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSBasestats) Reset() {
	*x = PSBasestats{}
	mi := &file_MainServer_Pokedex_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSBasestats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSBasestats) ProtoMessage() {}

func (x *PSBasestats) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSBasestats.ProtoReflect.Descriptor instead.
func (*PSBasestats) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{3}
}

func (x *PSBasestats) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *PSBasestats) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *PSBasestats) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *PSBasestats) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *PSBasestats) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *PSBasestats) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

type PSGenderratio struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	F             float32                `protobuf:"fixed32,1,opt,name=F,proto3" json:"F,omitempty"`
	M             float32                `protobuf:"fixed32,2,opt,name=M,proto3" json:"M,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSGenderratio) Reset() {
	*x = PSGenderratio{}
	mi := &file_MainServer_Pokedex_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSGenderratio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSGenderratio) ProtoMessage() {}

func (x *PSGenderratio) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Pokedex_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSGenderratio.ProtoReflect.Descriptor instead.
func (*PSGenderratio) Descriptor() ([]byte, []int) {
	return file_MainServer_Pokedex_proto_rawDescGZIP(), []int{4}
}

func (x *PSGenderratio) GetF() float32 {
	if x != nil {
		return x.F
	}
	return 0
}

func (x *PSGenderratio) GetM() float32 {
	if x != nil {
		return x.M
	}
	return 0
}

var File_MainServer_Pokedex_proto protoreflect.FileDescriptor

const file_MainServer_Pokedex_proto_rawDesc = "" +
	"\n" +
	"\x18MainServer/Pokedex.proto\x12\n" +
	"MainServer\"\xa2\x01\n" +
	"\x10PSPokemonDataMap\x12:\n" +
	"\x04data\x18\x01 \x03(\v2&.MainServer.PSPokemonDataMap.DataEntryR\x04data\x1aR\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12/\n" +
	"\x05value\x18\x02 \x01(\v2\x19.MainServer.PSPokemonDataR\x05value:\x028\x01\"\xef\v\n" +
	"\rPSPokemonData\x12\x1c\n" +
	"\tbaseForme\x18\x01 \x01(\tR\tbaseForme\x12 \n" +
	"\vbaseSpecies\x18\x02 \x01(\tR\vbaseSpecies\x12\x1e\n" +
	"\n" +
	"battleOnly\x18\x03 \x03(\tR\n" +
	"battleOnly\x12$\n" +
	"\rcanGigantamax\x18\x04 \x01(\tR\rcanGigantamax\x12\x1a\n" +
	"\bcanHatch\x18\x05 \x01(\bR\bcanHatch\x12$\n" +
	"\rcannotDynamax\x18\x06 \x01(\bR\rcannotDynamax\x12 \n" +
	"\vchangesFrom\x18\a \x01(\tR\vchangesFrom\x12\x14\n" +
	"\x05color\x18\b \x01(\tR\x05color\x12&\n" +
	"\x0ecosmeticFormes\x18\t \x03(\tR\x0ecosmeticFormes\x12\x1c\n" +
	"\teggGroups\x18\n" +
	" \x03(\tR\teggGroups\x12\"\n" +
	"\fevoCondition\x18\v \x01(\tR\fevoCondition\x12\x18\n" +
	"\aevoItem\x18\f \x01(\tR\aevoItem\x12\x1a\n" +
	"\bevoLevel\x18\r \x01(\x05R\bevoLevel\x12\x18\n" +
	"\aevoMove\x18\x0e \x01(\tR\aevoMove\x12\x1c\n" +
	"\tevoRegion\x18\x0f \x01(\tR\tevoRegion\x12\x18\n" +
	"\aevoType\x18\x10 \x01(\tR\aevoType\x12\x12\n" +
	"\x04evos\x18\x11 \x03(\tR\x04evos\x12$\n" +
	"\rforceTeraType\x18\x12 \x01(\tR\rforceTeraType\x12\x14\n" +
	"\x05forme\x18\x13 \x01(\tR\x05forme\x12\x1e\n" +
	"\n" +
	"formeOrder\x18\x14 \x03(\tR\n" +
	"formeOrder\x12\x10\n" +
	"\x03gen\x18\x15 \x01(\x05R\x03gen\x12\x16\n" +
	"\x06gender\x18\x16 \x01(\tR\x06gender\x12\x18\n" +
	"\aheightm\x18\x17 \x01(\x02R\aheightm\x12$\n" +
	"\risNonstandard\x18\x18 \x01(\tR\risNonstandard\x12\x14\n" +
	"\x05maxHP\x18\x19 \x01(\x05R\x05maxHP\x12\x16\n" +
	"\x06mother\x18\x1a \x01(\tR\x06mother\x12\x12\n" +
	"\x04name\x18\x1b \x01(\tR\x04name\x12\x10\n" +
	"\x03num\x18\x1c \x01(\x05R\x03num\x12 \n" +
	"\votherFormes\x18\x1d \x03(\tR\votherFormes\x12\x14\n" +
	"\x05prevo\x18\x1e \x01(\tR\x05prevo\x12(\n" +
	"\x0frequiredAbility\x18\x1f \x01(\tR\x0frequiredAbility\x12\"\n" +
	"\frequiredItem\x18  \x01(\tR\frequiredItem\x12$\n" +
	"\rrequiredItems\x18! \x03(\tR\rrequiredItems\x12\"\n" +
	"\frequiredMove\x18\" \x01(\tR\frequiredMove\x12\x12\n" +
	"\x04tags\x18# \x03(\tR\x04tags\x12\x12\n" +
	"\x04tier\x18$ \x01(\tR\x04tier\x12\x14\n" +
	"\x05types\x18% \x03(\tR\x05types\x12\x1a\n" +
	"\bweightkg\x18& \x01(\x02R\bweightkg\x12 \n" +
	"\vcaptureRate\x18' \x01(\x05R\vcaptureRate\x12\x1e\n" +
	"\n" +
	"growthRate\x18( \x01(\tR\n" +
	"growthRate\x12(\n" +
	"\x0fformsSwitchable\x18) \x01(\bR\x0fformsSwitchable\x12\"\n" +
	"\fhatchCounter\x18* \x01(\x05R\fhatchCounter\x125\n" +
	"\tabilities\x18+ \x01(\v2\x17.MainServer.PSAbilitiesR\tabilities\x125\n" +
	"\tbaseStats\x18, \x01(\v2\x17.MainServer.PSBasestatsR\tbaseStats\x12;\n" +
	"\vgenderRatio\x18- \x01(\v2\x19.MainServer.PSGenderratioR\vgenderRatio\x121\n" +
	"\aefforts\x18. \x01(\v2\x17.MainServer.PSBasestatsR\aefforts\x12&\n" +
	"\x0ebaseExperience\x18/ \x01(\x05R\x0ebaseExperience\"Q\n" +
	"\vPSAbilities\x12\f\n" +
	"\x01H\x18\x01 \x01(\tR\x01H\x12\f\n" +
	"\x01S\x18\x02 \x01(\tR\x01S\x12\x12\n" +
	"\x04key0\x18\x03 \x01(\tR\x04key0\x12\x12\n" +
	"\x04key1\x18\x04 \x01(\tR\x04key1\"w\n" +
	"\vPSBasestats\x12\x10\n" +
	"\x03atk\x18\x01 \x01(\x05R\x03atk\x12\x10\n" +
	"\x03def\x18\x02 \x01(\x05R\x03def\x12\x0e\n" +
	"\x02hp\x18\x03 \x01(\x05R\x02hp\x12\x10\n" +
	"\x03spa\x18\x04 \x01(\x05R\x03spa\x12\x10\n" +
	"\x03spd\x18\x05 \x01(\x05R\x03spd\x12\x10\n" +
	"\x03spe\x18\x06 \x01(\x05R\x03spe\"+\n" +
	"\rPSGenderratio\x12\f\n" +
	"\x01F\x18\x01 \x01(\x02R\x01F\x12\f\n" +
	"\x01M\x18\x02 \x01(\x02R\x01MB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Pokedex_proto_rawDescOnce sync.Once
	file_MainServer_Pokedex_proto_rawDescData []byte
)

func file_MainServer_Pokedex_proto_rawDescGZIP() []byte {
	file_MainServer_Pokedex_proto_rawDescOnce.Do(func() {
		file_MainServer_Pokedex_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Pokedex_proto_rawDesc), len(file_MainServer_Pokedex_proto_rawDesc)))
	})
	return file_MainServer_Pokedex_proto_rawDescData
}

var file_MainServer_Pokedex_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_MainServer_Pokedex_proto_goTypes = []any{
	(*PSPokemonDataMap)(nil), // 0: MainServer.PSPokemonDataMap
	(*PSPokemonData)(nil),    // 1: MainServer.PSPokemonData
	(*PSAbilities)(nil),      // 2: MainServer.PSAbilities
	(*PSBasestats)(nil),      // 3: MainServer.PSBasestats
	(*PSGenderratio)(nil),    // 4: MainServer.PSGenderratio
	nil,                      // 5: MainServer.PSPokemonDataMap.DataEntry
}
var file_MainServer_Pokedex_proto_depIdxs = []int32{
	5, // 0: MainServer.PSPokemonDataMap.data:type_name -> MainServer.PSPokemonDataMap.DataEntry
	2, // 1: MainServer.PSPokemonData.abilities:type_name -> MainServer.PSAbilities
	3, // 2: MainServer.PSPokemonData.baseStats:type_name -> MainServer.PSBasestats
	4, // 3: MainServer.PSPokemonData.genderRatio:type_name -> MainServer.PSGenderratio
	3, // 4: MainServer.PSPokemonData.efforts:type_name -> MainServer.PSBasestats
	1, // 5: MainServer.PSPokemonDataMap.DataEntry.value:type_name -> MainServer.PSPokemonData
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_MainServer_Pokedex_proto_init() }
func file_MainServer_Pokedex_proto_init() {
	if File_MainServer_Pokedex_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Pokedex_proto_rawDesc), len(file_MainServer_Pokedex_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Pokedex_proto_goTypes,
		DependencyIndexes: file_MainServer_Pokedex_proto_depIdxs,
		MessageInfos:      file_MainServer_Pokedex_proto_msgTypes,
	}.Build()
	File_MainServer_Pokedex_proto = out.File
	file_MainServer_Pokedex_proto_goTypes = nil
	file_MainServer_Pokedex_proto_depIdxs = nil
}

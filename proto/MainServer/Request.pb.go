// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Request.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_MainServer_Request_proto protoreflect.FileDescriptor

const file_MainServer_Request_proto_rawDesc = "" +
	"\n" +
	"\x18MainServer/Request.proto\x12\n" +
	"MainServerB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var file_MainServer_Request_proto_goTypes = []any{}
var file_MainServer_Request_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_MainServer_Request_proto_init() }
func file_MainServer_Request_proto_init() {
	if File_MainServer_Request_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Request_proto_rawDesc), len(file_MainServer_Request_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Request_proto_goTypes,
		DependencyIndexes: file_MainServer_Request_proto_depIdxs,
	}.Build()
	File_MainServer_Request_proto = out.File
	file_MainServer_Request_proto_goTypes = nil
	file_MainServer_Request_proto_depIdxs = nil
}

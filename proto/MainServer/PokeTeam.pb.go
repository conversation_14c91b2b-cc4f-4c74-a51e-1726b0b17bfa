// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/PokeTeam.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeTeam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid           int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Pokes         []*Poke                `protobuf:"bytes,4,rep,name=pokes,proto3" json:"pokes,omitempty"`
	CreateTs      int64                  `protobuf:"varint,5,opt,name=createTs,proto3" json:"createTs,omitempty"`
	UpdateTs      int64                  `protobuf:"varint,6,opt,name=updateTs,proto3" json:"updateTs,omitempty"`
	HireTids      []int64                `protobuf:"varint,7,rep,packed,name=hireTids,proto3" json:"hireTids,omitempty"` //租赁人
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeTeam) Reset() {
	*x = PokeTeam{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeTeam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeTeam) ProtoMessage() {}

func (x *PokeTeam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeTeam.ProtoReflect.Descriptor instead.
func (*PokeTeam) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{0}
}

func (x *PokeTeam) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PokeTeam) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *PokeTeam) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PokeTeam) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *PokeTeam) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *PokeTeam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *PokeTeam) GetHireTids() []int64 {
	if x != nil {
		return x.HireTids
	}
	return nil
}

type PokeTeamConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// poke_team_id	team_name	poke_init_boost	poke_min_boost	poke_immunity_type	poke_name_1	poke_ivs_1	poke_name_2	poke_ivs_2	poke_name_3	poke_ivs_3	poke_name_4	poke_ivs_4	poke_name_5	poke_ivs_5	poke_name_6	poke_ivs_6
	PokeTeamId    string            `protobuf:"bytes,1,opt,name=pokeTeamId,proto3" json:"pokeTeamId,omitempty"`
	TeamName      string            `protobuf:"bytes,2,opt,name=teamName,proto3" json:"teamName,omitempty"`
	Pokes         []*BattlePokeInfo `protobuf:"bytes,3,rep,name=pokes,proto3" json:"pokes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeTeamConfig) Reset() {
	*x = PokeTeamConfig{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeTeamConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeTeamConfig) ProtoMessage() {}

func (x *PokeTeamConfig) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeTeamConfig.ProtoReflect.Descriptor instead.
func (*PokeTeamConfig) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{1}
}

func (x *PokeTeamConfig) GetPokeTeamId() string {
	if x != nil {
		return x.PokeTeamId
	}
	return ""
}

func (x *PokeTeamConfig) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *PokeTeamConfig) GetPokes() []*BattlePokeInfo {
	if x != nil {
		return x.Pokes
	}
	return nil
}

type BattlePokeInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Poke             *Poke                  `protobuf:"bytes,1,opt,name=poke,proto3" json:"poke,omitempty"`
	PokeBoostStat    *PokeBoostStat         `protobuf:"bytes,2,opt,name=pokeBoostStat,proto3" json:"pokeBoostStat,omitempty"`
	PokeBoostMinStat *PokeBoostStat         `protobuf:"bytes,3,opt,name=pokeBoostMinStat,proto3" json:"pokeBoostMinStat,omitempty"`
	ImmunityTypes    []PokeTypeEnum         `protobuf:"varint,4,rep,packed,name=immunityTypes,proto3,enum=MainServer.PokeTypeEnum" json:"immunityTypes,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BattlePokeInfo) Reset() {
	*x = BattlePokeInfo{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BattlePokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattlePokeInfo) ProtoMessage() {}

func (x *BattlePokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattlePokeInfo.ProtoReflect.Descriptor instead.
func (*BattlePokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{2}
}

func (x *BattlePokeInfo) GetPoke() *Poke {
	if x != nil {
		return x.Poke
	}
	return nil
}

func (x *BattlePokeInfo) GetPokeBoostStat() *PokeBoostStat {
	if x != nil {
		return x.PokeBoostStat
	}
	return nil
}

func (x *BattlePokeInfo) GetPokeBoostMinStat() *PokeBoostStat {
	if x != nil {
		return x.PokeBoostMinStat
	}
	return nil
}

func (x *BattlePokeInfo) GetImmunityTypes() []PokeTypeEnum {
	if x != nil {
		return x.ImmunityTypes
	}
	return nil
}

type PokeTeamConfigList struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Configs       map[string]*PokeTeamConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeTeamConfigList) Reset() {
	*x = PokeTeamConfigList{}
	mi := &file_MainServer_PokeTeam_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeTeamConfigList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeTeamConfigList) ProtoMessage() {}

func (x *PokeTeamConfigList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PokeTeam_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeTeamConfigList.ProtoReflect.Descriptor instead.
func (*PokeTeamConfigList) Descriptor() ([]byte, []int) {
	return file_MainServer_PokeTeam_proto_rawDescGZIP(), []int{3}
}

func (x *PokeTeamConfigList) GetConfigs() map[string]*PokeTeamConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

var File_MainServer_PokeTeam_proto protoreflect.FileDescriptor

const file_MainServer_PokeTeam_proto_rawDesc = "" +
	"\n" +
	"\x19MainServer/PokeTeam.proto\x12\n" +
	"MainServer\x1a\x15MainServer/Poke.proto\"\xbc\x01\n" +
	"\bPokeTeam\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12&\n" +
	"\x05pokes\x18\x04 \x03(\v2\x10.MainServer.PokeR\x05pokes\x12\x1a\n" +
	"\bcreateTs\x18\x05 \x01(\x03R\bcreateTs\x12\x1a\n" +
	"\bupdateTs\x18\x06 \x01(\x03R\bupdateTs\x12\x1a\n" +
	"\bhireTids\x18\a \x03(\x03R\bhireTids\"~\n" +
	"\x0ePokeTeamConfig\x12\x1e\n" +
	"\n" +
	"pokeTeamId\x18\x01 \x01(\tR\n" +
	"pokeTeamId\x12\x1a\n" +
	"\bteamName\x18\x02 \x01(\tR\bteamName\x120\n" +
	"\x05pokes\x18\x03 \x03(\v2\x1a.MainServer.BattlePokeInfoR\x05pokes\"\xfe\x01\n" +
	"\x0eBattlePokeInfo\x12$\n" +
	"\x04poke\x18\x01 \x01(\v2\x10.MainServer.PokeR\x04poke\x12?\n" +
	"\rpokeBoostStat\x18\x02 \x01(\v2\x19.MainServer.PokeBoostStatR\rpokeBoostStat\x12E\n" +
	"\x10pokeBoostMinStat\x18\x03 \x01(\v2\x19.MainServer.PokeBoostStatR\x10pokeBoostMinStat\x12>\n" +
	"\rimmunityTypes\x18\x04 \x03(\x0e2\x18.MainServer.PokeTypeEnumR\rimmunityTypes\"\xb3\x01\n" +
	"\x12PokeTeamConfigList\x12E\n" +
	"\aconfigs\x18\x01 \x03(\v2+.MainServer.PokeTeamConfigList.ConfigsEntryR\aconfigs\x1aV\n" +
	"\fConfigsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x120\n" +
	"\x05value\x18\x02 \x01(\v2\x1a.MainServer.PokeTeamConfigR\x05value:\x028\x01B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_PokeTeam_proto_rawDescOnce sync.Once
	file_MainServer_PokeTeam_proto_rawDescData []byte
)

func file_MainServer_PokeTeam_proto_rawDescGZIP() []byte {
	file_MainServer_PokeTeam_proto_rawDescOnce.Do(func() {
		file_MainServer_PokeTeam_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_PokeTeam_proto_rawDesc), len(file_MainServer_PokeTeam_proto_rawDesc)))
	})
	return file_MainServer_PokeTeam_proto_rawDescData
}

var file_MainServer_PokeTeam_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_MainServer_PokeTeam_proto_goTypes = []any{
	(*PokeTeam)(nil),           // 0: MainServer.PokeTeam
	(*PokeTeamConfig)(nil),     // 1: MainServer.PokeTeamConfig
	(*BattlePokeInfo)(nil),     // 2: MainServer.BattlePokeInfo
	(*PokeTeamConfigList)(nil), // 3: MainServer.PokeTeamConfigList
	nil,                        // 4: MainServer.PokeTeamConfigList.ConfigsEntry
	(*Poke)(nil),               // 5: MainServer.Poke
	(*PokeBoostStat)(nil),      // 6: MainServer.PokeBoostStat
	(PokeTypeEnum)(0),          // 7: MainServer.PokeTypeEnum
}
var file_MainServer_PokeTeam_proto_depIdxs = []int32{
	5, // 0: MainServer.PokeTeam.pokes:type_name -> MainServer.Poke
	2, // 1: MainServer.PokeTeamConfig.pokes:type_name -> MainServer.BattlePokeInfo
	5, // 2: MainServer.BattlePokeInfo.poke:type_name -> MainServer.Poke
	6, // 3: MainServer.BattlePokeInfo.pokeBoostStat:type_name -> MainServer.PokeBoostStat
	6, // 4: MainServer.BattlePokeInfo.pokeBoostMinStat:type_name -> MainServer.PokeBoostStat
	7, // 5: MainServer.BattlePokeInfo.immunityTypes:type_name -> MainServer.PokeTypeEnum
	4, // 6: MainServer.PokeTeamConfigList.configs:type_name -> MainServer.PokeTeamConfigList.ConfigsEntry
	1, // 7: MainServer.PokeTeamConfigList.ConfigsEntry.value:type_name -> MainServer.PokeTeamConfig
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_MainServer_PokeTeam_proto_init() }
func file_MainServer_PokeTeam_proto_init() {
	if File_MainServer_PokeTeam_proto != nil {
		return
	}
	file_MainServer_Poke_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_PokeTeam_proto_rawDesc), len(file_MainServer_PokeTeam_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PokeTeam_proto_goTypes,
		DependencyIndexes: file_MainServer_PokeTeam_proto_depIdxs,
		MessageInfos:      file_MainServer_PokeTeam_proto_msgTypes,
	}.Build()
	File_MainServer_PokeTeam_proto = out.File
	file_MainServer_PokeTeam_proto_goTypes = nil
	file_MainServer_PokeTeam_proto_depIdxs = nil
}

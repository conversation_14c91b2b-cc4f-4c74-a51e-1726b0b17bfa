// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/PSMove.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PSMMoveDataMap struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Data          map[string]*PSMMoveData `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSMMoveDataMap) Reset() {
	*x = PSMMoveDataMap{}
	mi := &file_MainServer_PSMove_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMMoveDataMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMMoveDataMap) ProtoMessage() {}

func (x *PSMMoveDataMap) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMMoveDataMap.ProtoReflect.Descriptor instead.
func (*PSMMoveDataMap) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{0}
}

func (x *PSMMoveDataMap) GetData() map[string]*PSMMoveData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PSMMoveData struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	Accuracy  int32                  `protobuf:"varint,1,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
	BasePower int32                  `protobuf:"varint,2,opt,name=basePower,proto3" json:"basePower,omitempty"`
	// bool basePowerCallback = 3;
	// bool breaksProtect = 4;
	// bool callsMove = 5;
	Category string `protobuf:"bytes,6,opt,name=category,proto3" json:"category,omitempty"`
	// string contestType = 7;
	// int32 critRatio = 8;
	// string damage = 9;
	// string desc = 10;
	// int32 drain = 11;
	// bool forceSwitch = 12;
	// bool hasCrashDamage = 13;
	// bool hasSheerForce = 14;
	// int32 heal = 15;
	// bool ignoreAbility = 16;
	// bool ignoreDefensive = 17;
	// bool ignoreEvasion = 18;
	// bool ignoreImmunity = 19;
	// string isMax = 20;
	IsNonstandard string `protobuf:"bytes,21,opt,name=isNonstandard,proto3" json:"isNonstandard,omitempty"`
	IsZ           string `protobuf:"bytes,22,opt,name=isZ,proto3" json:"isZ,omitempty"`
	// bool mindBlownRecoil = 23;
	// bool multiaccuracy = 24;
	// int32 multihit = 25;
	Name string `protobuf:"bytes,26,opt,name=name,proto3" json:"name,omitempty"`
	// bool noPPBoosts = 27;
	// string nonGhostTarget = 28;
	Num int32 `protobuf:"varint,29,opt,name=num,proto3" json:"num,omitempty"`
	// string ohko = 30;
	// int32 onDamagePriority = 31;
	// string overrideDefensiveStat = 32;
	// string overrideOffensivePokemon = 33;
	// string overrideOffensiveStat = 34;
	Pp       int32 `protobuf:"varint,35,opt,name=pp,proto3" json:"pp,omitempty"`
	Priority int32 `protobuf:"varint,36,opt,name=priority,proto3" json:"priority,omitempty"`
	// string pseudoWeather = 37;
	// string realMove = 38;
	// int32 recoil = 39;
	// string secondary = 40;
	// string selfSwitch = 41;
	// string selfdestruct = 42;
	// string shortDesc = 43;
	// string sideCondition = 44;
	// bool sleepUsable = 45;
	// string slotCondition = 46;
	// bool smartTarget = 47;
	// bool stallingMove = 48;
	// string status = 49;
	// bool stealsBoosts = 50;
	// bool struggleRecoil = 51;
	Target string `protobuf:"bytes,52,opt,name=target,proto3" json:"target,omitempty"`
	// string terrain = 53;
	// bool thawsTarget = 54;
	// bool tracksTarget = 55;
	Type          string `protobuf:"bytes,56,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSMMoveData) Reset() {
	*x = PSMMoveData{}
	mi := &file_MainServer_PSMove_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMMoveData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMMoveData) ProtoMessage() {}

func (x *PSMMoveData) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMMoveData.ProtoReflect.Descriptor instead.
func (*PSMMoveData) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{1}
}

func (x *PSMMoveData) GetAccuracy() int32 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

func (x *PSMMoveData) GetBasePower() int32 {
	if x != nil {
		return x.BasePower
	}
	return 0
}

func (x *PSMMoveData) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *PSMMoveData) GetIsNonstandard() string {
	if x != nil {
		return x.IsNonstandard
	}
	return ""
}

func (x *PSMMoveData) GetIsZ() string {
	if x != nil {
		return x.IsZ
	}
	return ""
}

func (x *PSMMoveData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PSMMoveData) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *PSMMoveData) GetPp() int32 {
	if x != nil {
		return x.Pp
	}
	return 0
}

func (x *PSMMoveData) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *PSMMoveData) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *PSMMoveData) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type PSMBoosts struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Accuracy      int32                  `protobuf:"varint,1,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
	Atk           int32                  `protobuf:"varint,2,opt,name=atk,proto3" json:"atk,omitempty"`
	Def           int32                  `protobuf:"varint,3,opt,name=def,proto3" json:"def,omitempty"`
	Evasion       int32                  `protobuf:"varint,4,opt,name=evasion,proto3" json:"evasion,omitempty"`
	Spa           int32                  `protobuf:"varint,5,opt,name=spa,proto3" json:"spa,omitempty"`
	Spd           int32                  `protobuf:"varint,6,opt,name=spd,proto3" json:"spd,omitempty"`
	Spe           int32                  `protobuf:"varint,7,opt,name=spe,proto3" json:"spe,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSMBoosts) Reset() {
	*x = PSMBoosts{}
	mi := &file_MainServer_PSMove_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMBoosts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMBoosts) ProtoMessage() {}

func (x *PSMBoosts) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMBoosts.ProtoReflect.Descriptor instead.
func (*PSMBoosts) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{2}
}

func (x *PSMBoosts) GetAccuracy() int32 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

func (x *PSMBoosts) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *PSMBoosts) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *PSMBoosts) GetEvasion() int32 {
	if x != nil {
		return x.Evasion
	}
	return 0
}

func (x *PSMBoosts) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *PSMBoosts) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *PSMBoosts) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

type PSMCondition struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	CounterMax                      int32                  `protobuf:"varint,1,opt,name=counterMax,proto3" json:"counterMax,omitempty"`
	Duration                        int32                  `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`
	EffectType                      string                 `protobuf:"bytes,3,opt,name=effectType,proto3" json:"effectType,omitempty"`
	NoCopy                          bool                   `protobuf:"varint,4,opt,name=noCopy,proto3" json:"noCopy,omitempty"`
	OnAccuracyPriority              int32                  `protobuf:"varint,5,opt,name=onAccuracyPriority,proto3" json:"onAccuracyPriority,omitempty"`
	OnAnyPrepareHitPriority         int32                  `protobuf:"varint,6,opt,name=onAnyPrepareHitPriority,proto3" json:"onAnyPrepareHitPriority,omitempty"`
	OnBasePowerPriority             int32                  `protobuf:"varint,7,opt,name=onBasePowerPriority,proto3" json:"onBasePowerPriority,omitempty"`
	OnBeforeMovePriority            int32                  `protobuf:"varint,8,opt,name=onBeforeMovePriority,proto3" json:"onBeforeMovePriority,omitempty"`
	OnCriticalHit                   bool                   `protobuf:"varint,9,opt,name=onCriticalHit,proto3" json:"onCriticalHit,omitempty"`
	OnDamagePriority                int32                  `protobuf:"varint,10,opt,name=onDamagePriority,proto3" json:"onDamagePriority,omitempty"`
	OnEffectivenessPriority         int32                  `protobuf:"varint,11,opt,name=onEffectivenessPriority,proto3" json:"onEffectivenessPriority,omitempty"`
	OnFieldResidualOrder            int32                  `protobuf:"varint,12,opt,name=onFieldResidualOrder,proto3" json:"onFieldResidualOrder,omitempty"`
	OnFieldResidualSubOrder         int32                  `protobuf:"varint,13,opt,name=onFieldResidualSubOrder,proto3" json:"onFieldResidualSubOrder,omitempty"`
	OnFoeBeforeMovePriority         int32                  `protobuf:"varint,14,opt,name=onFoeBeforeMovePriority,proto3" json:"onFoeBeforeMovePriority,omitempty"`
	OnFoeRedirectTargetPriority     int32                  `protobuf:"varint,15,opt,name=onFoeRedirectTargetPriority,proto3" json:"onFoeRedirectTargetPriority,omitempty"`
	OnFoeTrapPokemonPriority        int32                  `protobuf:"varint,16,opt,name=onFoeTrapPokemonPriority,proto3" json:"onFoeTrapPokemonPriority,omitempty"`
	OnInvulnerability               bool                   `protobuf:"varint,17,opt,name=onInvulnerability,proto3" json:"onInvulnerability,omitempty"`
	OnLockMove                      string                 `protobuf:"bytes,18,opt,name=onLockMove,proto3" json:"onLockMove,omitempty"`
	OnModifyTypePriority            int32                  `protobuf:"varint,19,opt,name=onModifyTypePriority,proto3" json:"onModifyTypePriority,omitempty"`
	OnRedirectTargetPriority        int32                  `protobuf:"varint,20,opt,name=onRedirectTargetPriority,proto3" json:"onRedirectTargetPriority,omitempty"`
	OnResidualOrder                 int32                  `protobuf:"varint,21,opt,name=onResidualOrder,proto3" json:"onResidualOrder,omitempty"`
	OnResidualSubOrder              int32                  `protobuf:"varint,22,opt,name=onResidualSubOrder,proto3" json:"onResidualSubOrder,omitempty"`
	OnSideResidualOrder             int32                  `protobuf:"varint,23,opt,name=onSideResidualOrder,proto3" json:"onSideResidualOrder,omitempty"`
	OnSideResidualSubOrder          int32                  `protobuf:"varint,24,opt,name=onSideResidualSubOrder,proto3" json:"onSideResidualSubOrder,omitempty"`
	OnSourceInvulnerabilityPriority int32                  `protobuf:"varint,25,opt,name=onSourceInvulnerabilityPriority,proto3" json:"onSourceInvulnerabilityPriority,omitempty"`
	OnTryHitPriority                int32                  `protobuf:"varint,26,opt,name=onTryHitPriority,proto3" json:"onTryHitPriority,omitempty"`
	OnTryMovePriority               int32                  `protobuf:"varint,27,opt,name=onTryMovePriority,proto3" json:"onTryMovePriority,omitempty"`
	OnTryPrimaryHitPriority         int32                  `protobuf:"varint,28,opt,name=onTryPrimaryHitPriority,proto3" json:"onTryPrimaryHitPriority,omitempty"`
	OnTypePriority                  int32                  `protobuf:"varint,29,opt,name=onTypePriority,proto3" json:"onTypePriority,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *PSMCondition) Reset() {
	*x = PSMCondition{}
	mi := &file_MainServer_PSMove_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMCondition) ProtoMessage() {}

func (x *PSMCondition) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMCondition.ProtoReflect.Descriptor instead.
func (*PSMCondition) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{3}
}

func (x *PSMCondition) GetCounterMax() int32 {
	if x != nil {
		return x.CounterMax
	}
	return 0
}

func (x *PSMCondition) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *PSMCondition) GetEffectType() string {
	if x != nil {
		return x.EffectType
	}
	return ""
}

func (x *PSMCondition) GetNoCopy() bool {
	if x != nil {
		return x.NoCopy
	}
	return false
}

func (x *PSMCondition) GetOnAccuracyPriority() int32 {
	if x != nil {
		return x.OnAccuracyPriority
	}
	return 0
}

func (x *PSMCondition) GetOnAnyPrepareHitPriority() int32 {
	if x != nil {
		return x.OnAnyPrepareHitPriority
	}
	return 0
}

func (x *PSMCondition) GetOnBasePowerPriority() int32 {
	if x != nil {
		return x.OnBasePowerPriority
	}
	return 0
}

func (x *PSMCondition) GetOnBeforeMovePriority() int32 {
	if x != nil {
		return x.OnBeforeMovePriority
	}
	return 0
}

func (x *PSMCondition) GetOnCriticalHit() bool {
	if x != nil {
		return x.OnCriticalHit
	}
	return false
}

func (x *PSMCondition) GetOnDamagePriority() int32 {
	if x != nil {
		return x.OnDamagePriority
	}
	return 0
}

func (x *PSMCondition) GetOnEffectivenessPriority() int32 {
	if x != nil {
		return x.OnEffectivenessPriority
	}
	return 0
}

func (x *PSMCondition) GetOnFieldResidualOrder() int32 {
	if x != nil {
		return x.OnFieldResidualOrder
	}
	return 0
}

func (x *PSMCondition) GetOnFieldResidualSubOrder() int32 {
	if x != nil {
		return x.OnFieldResidualSubOrder
	}
	return 0
}

func (x *PSMCondition) GetOnFoeBeforeMovePriority() int32 {
	if x != nil {
		return x.OnFoeBeforeMovePriority
	}
	return 0
}

func (x *PSMCondition) GetOnFoeRedirectTargetPriority() int32 {
	if x != nil {
		return x.OnFoeRedirectTargetPriority
	}
	return 0
}

func (x *PSMCondition) GetOnFoeTrapPokemonPriority() int32 {
	if x != nil {
		return x.OnFoeTrapPokemonPriority
	}
	return 0
}

func (x *PSMCondition) GetOnInvulnerability() bool {
	if x != nil {
		return x.OnInvulnerability
	}
	return false
}

func (x *PSMCondition) GetOnLockMove() string {
	if x != nil {
		return x.OnLockMove
	}
	return ""
}

func (x *PSMCondition) GetOnModifyTypePriority() int32 {
	if x != nil {
		return x.OnModifyTypePriority
	}
	return 0
}

func (x *PSMCondition) GetOnRedirectTargetPriority() int32 {
	if x != nil {
		return x.OnRedirectTargetPriority
	}
	return 0
}

func (x *PSMCondition) GetOnResidualOrder() int32 {
	if x != nil {
		return x.OnResidualOrder
	}
	return 0
}

func (x *PSMCondition) GetOnResidualSubOrder() int32 {
	if x != nil {
		return x.OnResidualSubOrder
	}
	return 0
}

func (x *PSMCondition) GetOnSideResidualOrder() int32 {
	if x != nil {
		return x.OnSideResidualOrder
	}
	return 0
}

func (x *PSMCondition) GetOnSideResidualSubOrder() int32 {
	if x != nil {
		return x.OnSideResidualSubOrder
	}
	return 0
}

func (x *PSMCondition) GetOnSourceInvulnerabilityPriority() int32 {
	if x != nil {
		return x.OnSourceInvulnerabilityPriority
	}
	return 0
}

func (x *PSMCondition) GetOnTryHitPriority() int32 {
	if x != nil {
		return x.OnTryHitPriority
	}
	return 0
}

func (x *PSMCondition) GetOnTryMovePriority() int32 {
	if x != nil {
		return x.OnTryMovePriority
	}
	return 0
}

func (x *PSMCondition) GetOnTryPrimaryHitPriority() int32 {
	if x != nil {
		return x.OnTryPrimaryHitPriority
	}
	return 0
}

func (x *PSMCondition) GetOnTypePriority() int32 {
	if x != nil {
		return x.OnTypePriority
	}
	return 0
}

type PSMFlags struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Allyanim       int32                  `protobuf:"varint,1,opt,name=allyanim,proto3" json:"allyanim,omitempty"`
	Bite           int32                  `protobuf:"varint,2,opt,name=bite,proto3" json:"bite,omitempty"`
	Bullet         int32                  `protobuf:"varint,3,opt,name=bullet,proto3" json:"bullet,omitempty"`
	Bypasssub      int32                  `protobuf:"varint,4,opt,name=bypasssub,proto3" json:"bypasssub,omitempty"`
	Cantusetwice   int32                  `protobuf:"varint,5,opt,name=cantusetwice,proto3" json:"cantusetwice,omitempty"`
	Charge         int32                  `protobuf:"varint,6,opt,name=charge,proto3" json:"charge,omitempty"`
	Contact        int32                  `protobuf:"varint,7,opt,name=contact,proto3" json:"contact,omitempty"`
	Dance          int32                  `protobuf:"varint,8,opt,name=dance,proto3" json:"dance,omitempty"`
	Defrost        int32                  `protobuf:"varint,9,opt,name=defrost,proto3" json:"defrost,omitempty"`
	Distance       int32                  `protobuf:"varint,10,opt,name=distance,proto3" json:"distance,omitempty"`
	Failcopycat    int32                  `protobuf:"varint,11,opt,name=failcopycat,proto3" json:"failcopycat,omitempty"`
	Failencore     int32                  `protobuf:"varint,12,opt,name=failencore,proto3" json:"failencore,omitempty"`
	Failinstruct   int32                  `protobuf:"varint,13,opt,name=failinstruct,proto3" json:"failinstruct,omitempty"`
	Failmefirst    int32                  `protobuf:"varint,14,opt,name=failmefirst,proto3" json:"failmefirst,omitempty"`
	Failmimic      int32                  `protobuf:"varint,15,opt,name=failmimic,proto3" json:"failmimic,omitempty"`
	Futuremove     int32                  `protobuf:"varint,16,opt,name=futuremove,proto3" json:"futuremove,omitempty"`
	Gravity        int32                  `protobuf:"varint,17,opt,name=gravity,proto3" json:"gravity,omitempty"`
	Heal           int32                  `protobuf:"varint,18,opt,name=heal,proto3" json:"heal,omitempty"`
	Metronome      int32                  `protobuf:"varint,19,opt,name=metronome,proto3" json:"metronome,omitempty"`
	Mirror         int32                  `protobuf:"varint,20,opt,name=mirror,proto3" json:"mirror,omitempty"`
	Mustpressure   int32                  `protobuf:"varint,21,opt,name=mustpressure,proto3" json:"mustpressure,omitempty"`
	Noassist       int32                  `protobuf:"varint,22,opt,name=noassist,proto3" json:"noassist,omitempty"`
	Nonsky         int32                  `protobuf:"varint,23,opt,name=nonsky,proto3" json:"nonsky,omitempty"`
	Noparentalbond int32                  `protobuf:"varint,24,opt,name=noparentalbond,proto3" json:"noparentalbond,omitempty"`
	Nosketch       int32                  `protobuf:"varint,25,opt,name=nosketch,proto3" json:"nosketch,omitempty"`
	Nosleeptalk    int32                  `protobuf:"varint,26,opt,name=nosleeptalk,proto3" json:"nosleeptalk,omitempty"`
	Pledgecombo    int32                  `protobuf:"varint,27,opt,name=pledgecombo,proto3" json:"pledgecombo,omitempty"`
	Powder         int32                  `protobuf:"varint,28,opt,name=powder,proto3" json:"powder,omitempty"`
	Protect        int32                  `protobuf:"varint,29,opt,name=protect,proto3" json:"protect,omitempty"`
	Pulse          int32                  `protobuf:"varint,30,opt,name=pulse,proto3" json:"pulse,omitempty"`
	Punch          int32                  `protobuf:"varint,31,opt,name=punch,proto3" json:"punch,omitempty"`
	Recharge       int32                  `protobuf:"varint,32,opt,name=recharge,proto3" json:"recharge,omitempty"`
	Reflectable    int32                  `protobuf:"varint,33,opt,name=reflectable,proto3" json:"reflectable,omitempty"`
	Slicing        int32                  `protobuf:"varint,34,opt,name=slicing,proto3" json:"slicing,omitempty"`
	Snatch         int32                  `protobuf:"varint,35,opt,name=snatch,proto3" json:"snatch,omitempty"`
	Sound          int32                  `protobuf:"varint,36,opt,name=sound,proto3" json:"sound,omitempty"`
	Wind           int32                  `protobuf:"varint,37,opt,name=wind,proto3" json:"wind,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PSMFlags) Reset() {
	*x = PSMFlags{}
	mi := &file_MainServer_PSMove_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMFlags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMFlags) ProtoMessage() {}

func (x *PSMFlags) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMFlags.ProtoReflect.Descriptor instead.
func (*PSMFlags) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{4}
}

func (x *PSMFlags) GetAllyanim() int32 {
	if x != nil {
		return x.Allyanim
	}
	return 0
}

func (x *PSMFlags) GetBite() int32 {
	if x != nil {
		return x.Bite
	}
	return 0
}

func (x *PSMFlags) GetBullet() int32 {
	if x != nil {
		return x.Bullet
	}
	return 0
}

func (x *PSMFlags) GetBypasssub() int32 {
	if x != nil {
		return x.Bypasssub
	}
	return 0
}

func (x *PSMFlags) GetCantusetwice() int32 {
	if x != nil {
		return x.Cantusetwice
	}
	return 0
}

func (x *PSMFlags) GetCharge() int32 {
	if x != nil {
		return x.Charge
	}
	return 0
}

func (x *PSMFlags) GetContact() int32 {
	if x != nil {
		return x.Contact
	}
	return 0
}

func (x *PSMFlags) GetDance() int32 {
	if x != nil {
		return x.Dance
	}
	return 0
}

func (x *PSMFlags) GetDefrost() int32 {
	if x != nil {
		return x.Defrost
	}
	return 0
}

func (x *PSMFlags) GetDistance() int32 {
	if x != nil {
		return x.Distance
	}
	return 0
}

func (x *PSMFlags) GetFailcopycat() int32 {
	if x != nil {
		return x.Failcopycat
	}
	return 0
}

func (x *PSMFlags) GetFailencore() int32 {
	if x != nil {
		return x.Failencore
	}
	return 0
}

func (x *PSMFlags) GetFailinstruct() int32 {
	if x != nil {
		return x.Failinstruct
	}
	return 0
}

func (x *PSMFlags) GetFailmefirst() int32 {
	if x != nil {
		return x.Failmefirst
	}
	return 0
}

func (x *PSMFlags) GetFailmimic() int32 {
	if x != nil {
		return x.Failmimic
	}
	return 0
}

func (x *PSMFlags) GetFuturemove() int32 {
	if x != nil {
		return x.Futuremove
	}
	return 0
}

func (x *PSMFlags) GetGravity() int32 {
	if x != nil {
		return x.Gravity
	}
	return 0
}

func (x *PSMFlags) GetHeal() int32 {
	if x != nil {
		return x.Heal
	}
	return 0
}

func (x *PSMFlags) GetMetronome() int32 {
	if x != nil {
		return x.Metronome
	}
	return 0
}

func (x *PSMFlags) GetMirror() int32 {
	if x != nil {
		return x.Mirror
	}
	return 0
}

func (x *PSMFlags) GetMustpressure() int32 {
	if x != nil {
		return x.Mustpressure
	}
	return 0
}

func (x *PSMFlags) GetNoassist() int32 {
	if x != nil {
		return x.Noassist
	}
	return 0
}

func (x *PSMFlags) GetNonsky() int32 {
	if x != nil {
		return x.Nonsky
	}
	return 0
}

func (x *PSMFlags) GetNoparentalbond() int32 {
	if x != nil {
		return x.Noparentalbond
	}
	return 0
}

func (x *PSMFlags) GetNosketch() int32 {
	if x != nil {
		return x.Nosketch
	}
	return 0
}

func (x *PSMFlags) GetNosleeptalk() int32 {
	if x != nil {
		return x.Nosleeptalk
	}
	return 0
}

func (x *PSMFlags) GetPledgecombo() int32 {
	if x != nil {
		return x.Pledgecombo
	}
	return 0
}

func (x *PSMFlags) GetPowder() int32 {
	if x != nil {
		return x.Powder
	}
	return 0
}

func (x *PSMFlags) GetProtect() int32 {
	if x != nil {
		return x.Protect
	}
	return 0
}

func (x *PSMFlags) GetPulse() int32 {
	if x != nil {
		return x.Pulse
	}
	return 0
}

func (x *PSMFlags) GetPunch() int32 {
	if x != nil {
		return x.Punch
	}
	return 0
}

func (x *PSMFlags) GetRecharge() int32 {
	if x != nil {
		return x.Recharge
	}
	return 0
}

func (x *PSMFlags) GetReflectable() int32 {
	if x != nil {
		return x.Reflectable
	}
	return 0
}

func (x *PSMFlags) GetSlicing() int32 {
	if x != nil {
		return x.Slicing
	}
	return 0
}

func (x *PSMFlags) GetSnatch() int32 {
	if x != nil {
		return x.Snatch
	}
	return 0
}

func (x *PSMFlags) GetSound() int32 {
	if x != nil {
		return x.Sound
	}
	return 0
}

func (x *PSMFlags) GetWind() int32 {
	if x != nil {
		return x.Wind
	}
	return 0
}

type PSMIgnoreimmunity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ground        bool                   `protobuf:"varint,1,opt,name=Ground,proto3" json:"Ground,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSMIgnoreimmunity) Reset() {
	*x = PSMIgnoreimmunity{}
	mi := &file_MainServer_PSMove_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMIgnoreimmunity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMIgnoreimmunity) ProtoMessage() {}

func (x *PSMIgnoreimmunity) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMIgnoreimmunity.ProtoReflect.Descriptor instead.
func (*PSMIgnoreimmunity) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{5}
}

func (x *PSMIgnoreimmunity) GetGround() bool {
	if x != nil {
		return x.Ground
	}
	return false
}

type PSMMaxmove struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BasePower     int32                  `protobuf:"varint,1,opt,name=basePower,proto3" json:"basePower,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSMMaxmove) Reset() {
	*x = PSMMaxmove{}
	mi := &file_MainServer_PSMove_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMMaxmove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMMaxmove) ProtoMessage() {}

func (x *PSMMaxmove) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMMaxmove.ProtoReflect.Descriptor instead.
func (*PSMMaxmove) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{6}
}

func (x *PSMMaxmove) GetBasePower() int32 {
	if x != nil {
		return x.BasePower
	}
	return 0
}

type PSMSecondaries struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Chance         int32                  `protobuf:"varint,1,opt,name=chance,proto3" json:"chance,omitempty"`
	Status         string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	VolatileStatus string                 `protobuf:"bytes,3,opt,name=volatileStatus,proto3" json:"volatileStatus,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PSMSecondaries) Reset() {
	*x = PSMSecondaries{}
	mi := &file_MainServer_PSMove_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMSecondaries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMSecondaries) ProtoMessage() {}

func (x *PSMSecondaries) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMSecondaries.ProtoReflect.Descriptor instead.
func (*PSMSecondaries) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{7}
}

func (x *PSMSecondaries) GetChance() int32 {
	if x != nil {
		return x.Chance
	}
	return 0
}

func (x *PSMSecondaries) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PSMSecondaries) GetVolatileStatus() string {
	if x != nil {
		return x.VolatileStatus
	}
	return ""
}

type PSMSecondary struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Chance         int32                  `protobuf:"varint,1,opt,name=chance,proto3" json:"chance,omitempty"`
	Status         string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	VolatileStatus string                 `protobuf:"bytes,3,opt,name=volatileStatus,proto3" json:"volatileStatus,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PSMSecondary) Reset() {
	*x = PSMSecondary{}
	mi := &file_MainServer_PSMove_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMSecondary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMSecondary) ProtoMessage() {}

func (x *PSMSecondary) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMSecondary.ProtoReflect.Descriptor instead.
func (*PSMSecondary) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{8}
}

func (x *PSMSecondary) GetChance() int32 {
	if x != nil {
		return x.Chance
	}
	return 0
}

func (x *PSMSecondary) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PSMSecondary) GetVolatileStatus() string {
	if x != nil {
		return x.VolatileStatus
	}
	return ""
}

type PSMSelf struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Chance         int32                  `protobuf:"varint,1,opt,name=chance,proto3" json:"chance,omitempty"`
	PseudoWeather  string                 `protobuf:"bytes,2,opt,name=pseudoWeather,proto3" json:"pseudoWeather,omitempty"`
	SideCondition  string                 `protobuf:"bytes,3,opt,name=sideCondition,proto3" json:"sideCondition,omitempty"`
	VolatileStatus string                 `protobuf:"bytes,4,opt,name=volatileStatus,proto3" json:"volatileStatus,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PSMSelf) Reset() {
	*x = PSMSelf{}
	mi := &file_MainServer_PSMove_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMSelf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMSelf) ProtoMessage() {}

func (x *PSMSelf) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMSelf.ProtoReflect.Descriptor instead.
func (*PSMSelf) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{9}
}

func (x *PSMSelf) GetChance() int32 {
	if x != nil {
		return x.Chance
	}
	return 0
}

func (x *PSMSelf) GetPseudoWeather() string {
	if x != nil {
		return x.PseudoWeather
	}
	return ""
}

func (x *PSMSelf) GetSideCondition() string {
	if x != nil {
		return x.SideCondition
	}
	return ""
}

func (x *PSMSelf) GetVolatileStatus() string {
	if x != nil {
		return x.VolatileStatus
	}
	return ""
}

type PSMZmove struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BasePower     int32                  `protobuf:"varint,1,opt,name=basePower,proto3" json:"basePower,omitempty"`
	Effect        string                 `protobuf:"bytes,2,opt,name=effect,proto3" json:"effect,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSMZmove) Reset() {
	*x = PSMZmove{}
	mi := &file_MainServer_PSMove_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMZmove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMZmove) ProtoMessage() {}

func (x *PSMZmove) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMZmove.ProtoReflect.Descriptor instead.
func (*PSMZmove) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{10}
}

func (x *PSMZmove) GetBasePower() int32 {
	if x != nil {
		return x.BasePower
	}
	return 0
}

func (x *PSMZmove) GetEffect() string {
	if x != nil {
		return x.Effect
	}
	return ""
}

type PSMBoost struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Accuracy      int32                  `protobuf:"varint,1,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
	Atk           int32                  `protobuf:"varint,2,opt,name=atk,proto3" json:"atk,omitempty"`
	Def           int32                  `protobuf:"varint,3,opt,name=def,proto3" json:"def,omitempty"`
	Evasion       int32                  `protobuf:"varint,4,opt,name=evasion,proto3" json:"evasion,omitempty"`
	Spa           int32                  `protobuf:"varint,5,opt,name=spa,proto3" json:"spa,omitempty"`
	Spd           int32                  `protobuf:"varint,6,opt,name=spd,proto3" json:"spd,omitempty"`
	Spe           int32                  `protobuf:"varint,7,opt,name=spe,proto3" json:"spe,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PSMBoost) Reset() {
	*x = PSMBoost{}
	mi := &file_MainServer_PSMove_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PSMBoost) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PSMBoost) ProtoMessage() {}

func (x *PSMBoost) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_PSMove_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PSMBoost.ProtoReflect.Descriptor instead.
func (*PSMBoost) Descriptor() ([]byte, []int) {
	return file_MainServer_PSMove_proto_rawDescGZIP(), []int{11}
}

func (x *PSMBoost) GetAccuracy() int32 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

func (x *PSMBoost) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *PSMBoost) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *PSMBoost) GetEvasion() int32 {
	if x != nil {
		return x.Evasion
	}
	return 0
}

func (x *PSMBoost) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *PSMBoost) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *PSMBoost) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

var File_MainServer_PSMove_proto protoreflect.FileDescriptor

const file_MainServer_PSMove_proto_rawDesc = "" +
	"\n" +
	"\x17MainServer/PSMove.proto\x12\n" +
	"MainServer\"\x9c\x01\n" +
	"\x0ePSMMoveDataMap\x128\n" +
	"\x04data\x18\x01 \x03(\v2$.MainServer.PSMMoveDataMap.DataEntryR\x04data\x1aP\n" +
	"\tDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12-\n" +
	"\x05value\x18\x02 \x01(\v2\x17.MainServer.PSMMoveDataR\x05value:\x028\x01\"\x99\x02\n" +
	"\vPSMMoveData\x12\x1a\n" +
	"\baccuracy\x18\x01 \x01(\x05R\baccuracy\x12\x1c\n" +
	"\tbasePower\x18\x02 \x01(\x05R\tbasePower\x12\x1a\n" +
	"\bcategory\x18\x06 \x01(\tR\bcategory\x12$\n" +
	"\risNonstandard\x18\x15 \x01(\tR\risNonstandard\x12\x10\n" +
	"\x03isZ\x18\x16 \x01(\tR\x03isZ\x12\x12\n" +
	"\x04name\x18\x1a \x01(\tR\x04name\x12\x10\n" +
	"\x03num\x18\x1d \x01(\x05R\x03num\x12\x0e\n" +
	"\x02pp\x18# \x01(\x05R\x02pp\x12\x1a\n" +
	"\bpriority\x18$ \x01(\x05R\bpriority\x12\x16\n" +
	"\x06target\x184 \x01(\tR\x06target\x12\x12\n" +
	"\x04type\x188 \x01(\tR\x04type\"\x9b\x01\n" +
	"\tPSMBoosts\x12\x1a\n" +
	"\baccuracy\x18\x01 \x01(\x05R\baccuracy\x12\x10\n" +
	"\x03atk\x18\x02 \x01(\x05R\x03atk\x12\x10\n" +
	"\x03def\x18\x03 \x01(\x05R\x03def\x12\x18\n" +
	"\aevasion\x18\x04 \x01(\x05R\aevasion\x12\x10\n" +
	"\x03spa\x18\x05 \x01(\x05R\x03spa\x12\x10\n" +
	"\x03spd\x18\x06 \x01(\x05R\x03spd\x12\x10\n" +
	"\x03spe\x18\a \x01(\x05R\x03spe\"\x8c\v\n" +
	"\fPSMCondition\x12\x1e\n" +
	"\n" +
	"counterMax\x18\x01 \x01(\x05R\n" +
	"counterMax\x12\x1a\n" +
	"\bduration\x18\x02 \x01(\x05R\bduration\x12\x1e\n" +
	"\n" +
	"effectType\x18\x03 \x01(\tR\n" +
	"effectType\x12\x16\n" +
	"\x06noCopy\x18\x04 \x01(\bR\x06noCopy\x12.\n" +
	"\x12onAccuracyPriority\x18\x05 \x01(\x05R\x12onAccuracyPriority\x128\n" +
	"\x17onAnyPrepareHitPriority\x18\x06 \x01(\x05R\x17onAnyPrepareHitPriority\x120\n" +
	"\x13onBasePowerPriority\x18\a \x01(\x05R\x13onBasePowerPriority\x122\n" +
	"\x14onBeforeMovePriority\x18\b \x01(\x05R\x14onBeforeMovePriority\x12$\n" +
	"\ronCriticalHit\x18\t \x01(\bR\ronCriticalHit\x12*\n" +
	"\x10onDamagePriority\x18\n" +
	" \x01(\x05R\x10onDamagePriority\x128\n" +
	"\x17onEffectivenessPriority\x18\v \x01(\x05R\x17onEffectivenessPriority\x122\n" +
	"\x14onFieldResidualOrder\x18\f \x01(\x05R\x14onFieldResidualOrder\x128\n" +
	"\x17onFieldResidualSubOrder\x18\r \x01(\x05R\x17onFieldResidualSubOrder\x128\n" +
	"\x17onFoeBeforeMovePriority\x18\x0e \x01(\x05R\x17onFoeBeforeMovePriority\x12@\n" +
	"\x1bonFoeRedirectTargetPriority\x18\x0f \x01(\x05R\x1bonFoeRedirectTargetPriority\x12:\n" +
	"\x18onFoeTrapPokemonPriority\x18\x10 \x01(\x05R\x18onFoeTrapPokemonPriority\x12,\n" +
	"\x11onInvulnerability\x18\x11 \x01(\bR\x11onInvulnerability\x12\x1e\n" +
	"\n" +
	"onLockMove\x18\x12 \x01(\tR\n" +
	"onLockMove\x122\n" +
	"\x14onModifyTypePriority\x18\x13 \x01(\x05R\x14onModifyTypePriority\x12:\n" +
	"\x18onRedirectTargetPriority\x18\x14 \x01(\x05R\x18onRedirectTargetPriority\x12(\n" +
	"\x0fonResidualOrder\x18\x15 \x01(\x05R\x0fonResidualOrder\x12.\n" +
	"\x12onResidualSubOrder\x18\x16 \x01(\x05R\x12onResidualSubOrder\x120\n" +
	"\x13onSideResidualOrder\x18\x17 \x01(\x05R\x13onSideResidualOrder\x126\n" +
	"\x16onSideResidualSubOrder\x18\x18 \x01(\x05R\x16onSideResidualSubOrder\x12H\n" +
	"\x1fonSourceInvulnerabilityPriority\x18\x19 \x01(\x05R\x1fonSourceInvulnerabilityPriority\x12*\n" +
	"\x10onTryHitPriority\x18\x1a \x01(\x05R\x10onTryHitPriority\x12,\n" +
	"\x11onTryMovePriority\x18\x1b \x01(\x05R\x11onTryMovePriority\x128\n" +
	"\x17onTryPrimaryHitPriority\x18\x1c \x01(\x05R\x17onTryPrimaryHitPriority\x12&\n" +
	"\x0eonTypePriority\x18\x1d \x01(\x05R\x0eonTypePriority\"\x94\b\n" +
	"\bPSMFlags\x12\x1a\n" +
	"\ballyanim\x18\x01 \x01(\x05R\ballyanim\x12\x12\n" +
	"\x04bite\x18\x02 \x01(\x05R\x04bite\x12\x16\n" +
	"\x06bullet\x18\x03 \x01(\x05R\x06bullet\x12\x1c\n" +
	"\tbypasssub\x18\x04 \x01(\x05R\tbypasssub\x12\"\n" +
	"\fcantusetwice\x18\x05 \x01(\x05R\fcantusetwice\x12\x16\n" +
	"\x06charge\x18\x06 \x01(\x05R\x06charge\x12\x18\n" +
	"\acontact\x18\a \x01(\x05R\acontact\x12\x14\n" +
	"\x05dance\x18\b \x01(\x05R\x05dance\x12\x18\n" +
	"\adefrost\x18\t \x01(\x05R\adefrost\x12\x1a\n" +
	"\bdistance\x18\n" +
	" \x01(\x05R\bdistance\x12 \n" +
	"\vfailcopycat\x18\v \x01(\x05R\vfailcopycat\x12\x1e\n" +
	"\n" +
	"failencore\x18\f \x01(\x05R\n" +
	"failencore\x12\"\n" +
	"\ffailinstruct\x18\r \x01(\x05R\ffailinstruct\x12 \n" +
	"\vfailmefirst\x18\x0e \x01(\x05R\vfailmefirst\x12\x1c\n" +
	"\tfailmimic\x18\x0f \x01(\x05R\tfailmimic\x12\x1e\n" +
	"\n" +
	"futuremove\x18\x10 \x01(\x05R\n" +
	"futuremove\x12\x18\n" +
	"\agravity\x18\x11 \x01(\x05R\agravity\x12\x12\n" +
	"\x04heal\x18\x12 \x01(\x05R\x04heal\x12\x1c\n" +
	"\tmetronome\x18\x13 \x01(\x05R\tmetronome\x12\x16\n" +
	"\x06mirror\x18\x14 \x01(\x05R\x06mirror\x12\"\n" +
	"\fmustpressure\x18\x15 \x01(\x05R\fmustpressure\x12\x1a\n" +
	"\bnoassist\x18\x16 \x01(\x05R\bnoassist\x12\x16\n" +
	"\x06nonsky\x18\x17 \x01(\x05R\x06nonsky\x12&\n" +
	"\x0enoparentalbond\x18\x18 \x01(\x05R\x0enoparentalbond\x12\x1a\n" +
	"\bnosketch\x18\x19 \x01(\x05R\bnosketch\x12 \n" +
	"\vnosleeptalk\x18\x1a \x01(\x05R\vnosleeptalk\x12 \n" +
	"\vpledgecombo\x18\x1b \x01(\x05R\vpledgecombo\x12\x16\n" +
	"\x06powder\x18\x1c \x01(\x05R\x06powder\x12\x18\n" +
	"\aprotect\x18\x1d \x01(\x05R\aprotect\x12\x14\n" +
	"\x05pulse\x18\x1e \x01(\x05R\x05pulse\x12\x14\n" +
	"\x05punch\x18\x1f \x01(\x05R\x05punch\x12\x1a\n" +
	"\brecharge\x18  \x01(\x05R\brecharge\x12 \n" +
	"\vreflectable\x18! \x01(\x05R\vreflectable\x12\x18\n" +
	"\aslicing\x18\" \x01(\x05R\aslicing\x12\x16\n" +
	"\x06snatch\x18# \x01(\x05R\x06snatch\x12\x14\n" +
	"\x05sound\x18$ \x01(\x05R\x05sound\x12\x12\n" +
	"\x04wind\x18% \x01(\x05R\x04wind\"+\n" +
	"\x11PSMIgnoreimmunity\x12\x16\n" +
	"\x06Ground\x18\x01 \x01(\bR\x06Ground\"*\n" +
	"\n" +
	"PSMMaxmove\x12\x1c\n" +
	"\tbasePower\x18\x01 \x01(\x05R\tbasePower\"h\n" +
	"\x0ePSMSecondaries\x12\x16\n" +
	"\x06chance\x18\x01 \x01(\x05R\x06chance\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12&\n" +
	"\x0evolatileStatus\x18\x03 \x01(\tR\x0evolatileStatus\"f\n" +
	"\fPSMSecondary\x12\x16\n" +
	"\x06chance\x18\x01 \x01(\x05R\x06chance\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12&\n" +
	"\x0evolatileStatus\x18\x03 \x01(\tR\x0evolatileStatus\"\x95\x01\n" +
	"\aPSMSelf\x12\x16\n" +
	"\x06chance\x18\x01 \x01(\x05R\x06chance\x12$\n" +
	"\rpseudoWeather\x18\x02 \x01(\tR\rpseudoWeather\x12$\n" +
	"\rsideCondition\x18\x03 \x01(\tR\rsideCondition\x12&\n" +
	"\x0evolatileStatus\x18\x04 \x01(\tR\x0evolatileStatus\"@\n" +
	"\bPSMZmove\x12\x1c\n" +
	"\tbasePower\x18\x01 \x01(\x05R\tbasePower\x12\x16\n" +
	"\x06effect\x18\x02 \x01(\tR\x06effect\"\x9a\x01\n" +
	"\bPSMBoost\x12\x1a\n" +
	"\baccuracy\x18\x01 \x01(\x05R\baccuracy\x12\x10\n" +
	"\x03atk\x18\x02 \x01(\x05R\x03atk\x12\x10\n" +
	"\x03def\x18\x03 \x01(\x05R\x03def\x12\x18\n" +
	"\aevasion\x18\x04 \x01(\x05R\aevasion\x12\x10\n" +
	"\x03spa\x18\x05 \x01(\x05R\x03spa\x12\x10\n" +
	"\x03spd\x18\x06 \x01(\x05R\x03spd\x12\x10\n" +
	"\x03spe\x18\a \x01(\x05R\x03speB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_PSMove_proto_rawDescOnce sync.Once
	file_MainServer_PSMove_proto_rawDescData []byte
)

func file_MainServer_PSMove_proto_rawDescGZIP() []byte {
	file_MainServer_PSMove_proto_rawDescOnce.Do(func() {
		file_MainServer_PSMove_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_PSMove_proto_rawDesc), len(file_MainServer_PSMove_proto_rawDesc)))
	})
	return file_MainServer_PSMove_proto_rawDescData
}

var file_MainServer_PSMove_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_MainServer_PSMove_proto_goTypes = []any{
	(*PSMMoveDataMap)(nil),    // 0: MainServer.PSMMoveDataMap
	(*PSMMoveData)(nil),       // 1: MainServer.PSMMoveData
	(*PSMBoosts)(nil),         // 2: MainServer.PSMBoosts
	(*PSMCondition)(nil),      // 3: MainServer.PSMCondition
	(*PSMFlags)(nil),          // 4: MainServer.PSMFlags
	(*PSMIgnoreimmunity)(nil), // 5: MainServer.PSMIgnoreimmunity
	(*PSMMaxmove)(nil),        // 6: MainServer.PSMMaxmove
	(*PSMSecondaries)(nil),    // 7: MainServer.PSMSecondaries
	(*PSMSecondary)(nil),      // 8: MainServer.PSMSecondary
	(*PSMSelf)(nil),           // 9: MainServer.PSMSelf
	(*PSMZmove)(nil),          // 10: MainServer.PSMZmove
	(*PSMBoost)(nil),          // 11: MainServer.PSMBoost
	nil,                       // 12: MainServer.PSMMoveDataMap.DataEntry
}
var file_MainServer_PSMove_proto_depIdxs = []int32{
	12, // 0: MainServer.PSMMoveDataMap.data:type_name -> MainServer.PSMMoveDataMap.DataEntry
	1,  // 1: MainServer.PSMMoveDataMap.DataEntry.value:type_name -> MainServer.PSMMoveData
	2,  // [2:2] is the sub-list for method output_type
	2,  // [2:2] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_MainServer_PSMove_proto_init() }
func file_MainServer_PSMove_proto_init() {
	if File_MainServer_PSMove_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_PSMove_proto_rawDesc), len(file_MainServer_PSMove_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_PSMove_proto_goTypes,
		DependencyIndexes: file_MainServer_PSMove_proto_depIdxs,
		MessageInfos:      file_MainServer_PSMove_proto_msgTypes,
	}.Build()
	File_MainServer_PSMove_proto = out.File
	file_MainServer_PSMove_proto_goTypes = nil
	file_MainServer_PSMove_proto_depIdxs = nil
}

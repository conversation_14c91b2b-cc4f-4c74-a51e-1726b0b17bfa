// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: GameManager/Region.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RPokeItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	P             float32                `protobuf:"fixed32,2,opt,name=p,proto3" json:"p,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RPokeItem) Reset() {
	*x = RPokeItem{}
	mi := &file_GameManager_Region_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RPokeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPokeItem) ProtoMessage() {}

func (x *RPokeItem) ProtoReflect() protoreflect.Message {
	mi := &file_GameManager_Region_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPokeItem.ProtoReflect.Descriptor instead.
func (*RPokeItem) Descriptor() ([]byte, []int) {
	return file_GameManager_Region_proto_rawDescGZIP(), []int{0}
}

func (x *RPokeItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RPokeItem) GetP() float32 {
	if x != nil {
		return x.P
	}
	return 0
}

type RPoke struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	P             float32                `protobuf:"fixed32,2,opt,name=p,proto3" json:"p,omitempty"`       //概率
	Minl          int32                  `protobuf:"varint,3,opt,name=minl,proto3" json:"minl,omitempty"`  // 最低等级
	Maxl          int32                  `protobuf:"varint,4,opt,name=maxl,proto3" json:"maxl,omitempty"`  // 最高等级
	Items         []*RPokeItem           `protobuf:"bytes,5,rep,name=items,proto3" json:"items,omitempty"` // 宝可梦的物品列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RPoke) Reset() {
	*x = RPoke{}
	mi := &file_GameManager_Region_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RPoke) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RPoke) ProtoMessage() {}

func (x *RPoke) ProtoReflect() protoreflect.Message {
	mi := &file_GameManager_Region_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RPoke.ProtoReflect.Descriptor instead.
func (*RPoke) Descriptor() ([]byte, []int) {
	return file_GameManager_Region_proto_rawDescGZIP(), []int{1}
}

func (x *RPoke) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RPoke) GetP() float32 {
	if x != nil {
		return x.P
	}
	return 0
}

func (x *RPoke) GetMinl() int32 {
	if x != nil {
		return x.Minl
	}
	return 0
}

func (x *RPoke) GetMaxl() int32 {
	if x != nil {
		return x.Maxl
	}
	return 0
}

func (x *RPoke) GetItems() []*RPokeItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type Region struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Minl          int32                  `protobuf:"varint,2,opt,name=minl,proto3" json:"minl,omitempty"`  //最低等级
	Maxl          int32                  `protobuf:"varint,3,opt,name=maxl,proto3" json:"maxl,omitempty"`  //最高等级
	Pokes         []*RPoke               `protobuf:"bytes,4,rep,name=pokes,proto3" json:"pokes,omitempty"` // 宝可梦列表
	Items         []*RPokeItem           `protobuf:"bytes,5,rep,name=items,proto3" json:"items,omitempty"` // 区域的物品列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Region) Reset() {
	*x = Region{}
	mi := &file_GameManager_Region_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Region) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Region) ProtoMessage() {}

func (x *Region) ProtoReflect() protoreflect.Message {
	mi := &file_GameManager_Region_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Region.ProtoReflect.Descriptor instead.
func (*Region) Descriptor() ([]byte, []int) {
	return file_GameManager_Region_proto_rawDescGZIP(), []int{2}
}

func (x *Region) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Region) GetMinl() int32 {
	if x != nil {
		return x.Minl
	}
	return 0
}

func (x *Region) GetMaxl() int32 {
	if x != nil {
		return x.Maxl
	}
	return 0
}

func (x *Region) GetPokes() []*RPoke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *Region) GetItems() []*RPokeItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type RegionList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Regions       []*Region              `protobuf:"bytes,1,rep,name=regions,proto3" json:"regions,omitempty"` // 列表包含多个区域
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegionList) Reset() {
	*x = RegionList{}
	mi := &file_GameManager_Region_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegionList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionList) ProtoMessage() {}

func (x *RegionList) ProtoReflect() protoreflect.Message {
	mi := &file_GameManager_Region_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionList.ProtoReflect.Descriptor instead.
func (*RegionList) Descriptor() ([]byte, []int) {
	return file_GameManager_Region_proto_rawDescGZIP(), []int{3}
}

func (x *RegionList) GetRegions() []*Region {
	if x != nil {
		return x.Regions
	}
	return nil
}

func (x *RegionList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_GameManager_Region_proto protoreflect.FileDescriptor

const file_GameManager_Region_proto_rawDesc = "" +
	"\n" +
	"\x18GameManager/Region.proto\x12\tPetsProto\"-\n" +
	"\tRPokeItem\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\f\n" +
	"\x01p\x18\x02 \x01(\x02R\x01p\"}\n" +
	"\x05RPoke\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\f\n" +
	"\x01p\x18\x02 \x01(\x02R\x01p\x12\x12\n" +
	"\x04minl\x18\x03 \x01(\x05R\x04minl\x12\x12\n" +
	"\x04maxl\x18\x04 \x01(\x05R\x04maxl\x12*\n" +
	"\x05items\x18\x05 \x03(\v2\x14.PetsProto.RPokeItemR\x05items\"\x94\x01\n" +
	"\x06Region\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04minl\x18\x02 \x01(\x05R\x04minl\x12\x12\n" +
	"\x04maxl\x18\x03 \x01(\x05R\x04maxl\x12&\n" +
	"\x05pokes\x18\x04 \x03(\v2\x10.PetsProto.RPokeR\x05pokes\x12*\n" +
	"\x05items\x18\x05 \x03(\v2\x14.PetsProto.RPokeItemR\x05items\"M\n" +
	"\n" +
	"RegionList\x12+\n" +
	"\aregions\x18\x01 \x03(\v2\x11.PetsProto.RegionR\aregions\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04nameB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_GameManager_Region_proto_rawDescOnce sync.Once
	file_GameManager_Region_proto_rawDescData []byte
)

func file_GameManager_Region_proto_rawDescGZIP() []byte {
	file_GameManager_Region_proto_rawDescOnce.Do(func() {
		file_GameManager_Region_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_GameManager_Region_proto_rawDesc), len(file_GameManager_Region_proto_rawDesc)))
	})
	return file_GameManager_Region_proto_rawDescData
}

var file_GameManager_Region_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_GameManager_Region_proto_goTypes = []any{
	(*RPokeItem)(nil),  // 0: PetsProto.RPokeItem
	(*RPoke)(nil),      // 1: PetsProto.RPoke
	(*Region)(nil),     // 2: PetsProto.Region
	(*RegionList)(nil), // 3: PetsProto.RegionList
}
var file_GameManager_Region_proto_depIdxs = []int32{
	0, // 0: PetsProto.RPoke.items:type_name -> PetsProto.RPokeItem
	1, // 1: PetsProto.Region.pokes:type_name -> PetsProto.RPoke
	0, // 2: PetsProto.Region.items:type_name -> PetsProto.RPokeItem
	2, // 3: PetsProto.RegionList.regions:type_name -> PetsProto.Region
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_GameManager_Region_proto_init() }
func file_GameManager_Region_proto_init() {
	if File_GameManager_Region_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_GameManager_Region_proto_rawDesc), len(file_GameManager_Region_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_GameManager_Region_proto_goTypes,
		DependencyIndexes: file_GameManager_Region_proto_depIdxs,
		MessageInfos:      file_GameManager_Region_proto_msgTypes,
	}.Build()
	File_GameManager_Region_proto = out.File
	file_GameManager_Region_proto_goTypes = nil
	file_GameManager_Region_proto_depIdxs = nil
}

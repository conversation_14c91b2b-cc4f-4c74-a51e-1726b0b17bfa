package poketeam

import "go-nakama-poke/proto/MainServer"

// 配置NPC的poke阵容
func GetNpcPokes(npcTeam MainServer.TrainerTeam, npcId MainServer.TrainerTeamNpcId) []*MainServer.Poke {
	switch npcTeam {
	case MainServer.TrainerTeam_TRAINER_TEAM_Rocket:
		return GetRocketNpcBy(npcId)
	case MainServer.TrainerTeam_TRAINER_TEAM_Magma:
		return GetMagmaNpcBy(npcId)
	case MainServer.TrainerTeam_TRAINER_TEAM_Aqua:
		return GetAquaNpcBy(npcId)

	}
	return []*MainServer.Poke{}
}

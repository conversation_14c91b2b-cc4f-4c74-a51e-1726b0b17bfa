package poketeam

import (
	"context"
	"database/sql"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableTeamName = "poke_team"

func InitTeam(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	// createTeamTableIfNotExists(ctx, logger, db)
	LoadLocalNpcConfigsInfos()
	LoadLocalPokeTeamsInfos()
}

// func createTeamTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
// 	tableName := TableTeamName
// 	createTableSQL := fmt.Sprintf(`
//         CREATE TABLE IF NOT EXISTS %s (
//             id BIGSERIAL PRIMARY KEY,
//             tid BIGINT,
// 			poke_ids BIGINT[],
// 			hire_tids BIGINT[],
//             create_ts BIGINT,
//             update_ts BIGINT
//         );
//     `, TableTeamName)
// 	_, err := db.ExecContext(ctx, createTableSQL)
// 	if err != nil {
// 		logger.Error("failed to create table %s: %v", TableTeamName, err)
// 		return fmt.Errorf("failed to create table %s: %v", TableTeamName, err)
// 	}
// 	createIndexes := []string{
// 		"CREATE INDEX IF NOT EXISTS idx_%s_id ON %s (id);",
// 		"CREATE INDEX IF NOT EXISTS idx_%s_tid ON %s (tid);",
// 		"CREATE INDEX IF NOT EXISTS idx_%s_update_ts ON %s (update_ts);",
// 	}
// 	var createIndexSQL string

// 	// 遍历数组，每一条索引语句都会被格式化为带有 tableName 的 SQL 语句
// 	for _, indexStmt := range createIndexes {
// 		createIndexSQL += fmt.Sprintf(indexStmt, tableName, tableName) + "\n"
// 	}

// 	_, err = db.ExecContext(ctx, createIndexSQL)
// 	if err != nil {
// 		logger.Error("failed to create index on table %s: %w", tableName, err)
// 		return fmt.Errorf("failed to create index on table %s: %w", tableName, err)
// 	}

// 	logger.Info("poke_team table created successfully.")
// 	return nil
// }

// func upsertTeam(ctx context.Context, logger runtime.Logger, tx *sql.Tx, team *MainServer.PokeTeam) (int64, error) {
// 	team.UpdateTs = time.Now().UnixMilli()
// 	tableName := TableTeamName

// 	if team.Id < 0 {
// 		err := tx.QueryRowContext(ctx, fmt.Sprintf("SELECT nextval(pg_get_serial_sequence('%s', 'id'))", tableName)).Scan(&team.Id)
// 		if err != nil {
// 			return 0, fmt.Errorf("failed to generate new id: %v", err)
// 		}
// 	}
// 	poke_ids := make([]int64, len(team.Pokes))
// 	for _, poke := range team.Pokes {
// 		poke_ids = append(poke_ids, poke.Id)
// 	}
// 	// hire_ids := make([]string, len(team.HireTids))
// 	// for _, tid := range team.HireTids {
// 	// 	poke_ids = append(poke_ids, strconv.FormatInt(tid, 10))
// 	// }
// 	updateSQL := fmt.Sprintf(`
// 		INSERT INTO %s (id, tid, poke_ids, hire_tids, create_ts, update_ts)
// 		VALUES ($1, $2, $3::BIGINT[], $4::BIGINT[], $5, $6)
// 		ON CONFLICT (tid) DO UPDATE
// 		SET poke_ids = $3::BIGINT[], hire_tids = $4::BIGINT[], update_ts = $6
// 		RETURNING id;
// 	`, tableName)
// 	var id int64
// 	err := tx.QueryRowContext(ctx, updateSQL, team.Id, team.Tid, pq.Array(&poke_ids), pq.Array(&team.HireTids), team.CreateTs, team.UpdateTs).Scan(&id)
// 	if err != nil {
// 		return 0, fmt.Errorf("failed to upsert trainer: %v", err)
// 	}
// 	return id, nil
// }
// func SelectTrainerTeamProtos(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, updateTs int64) ([]*MainServer.PokeTeam, error) {
// 	query := fmt.Sprintf(`
//     SELECT id, tid, poke_ids, hire_tids, create_ts, update_ts
//     FROM %s
//     WHERE $1 = ANY(hire_tids) AND update_ts <= $2`, TableTeamName)
// 	rows, err := tx.QueryContext(ctx, query, tid, updateTs)
// 	if err != nil {
// 		// logger.Error("Error fetching poke box: %v", err)
// 		return nil, err
// 	}
// 	defer rows.Close()
// 	var teams []*MainServer.PokeTeam
// 	for rows.Next() {
// 		var team MainServer.PokeTeam
// 		var pokeIds []int64
// 		err := rows.Scan(&team.Id, team.Tid, pq.Array(pokeIds), pq.Array(team.HireTids), &team.CreateTs, &team.UpdateTs)
// 		if err != nil {
// 			return nil, err
// 		}
// 		pokes, err := poke.QueryPokesByIdsAndUpdateTs(ctx, tx, team.Tid, pokeIds, 0)
// 		if err != nil {
// 			return nil, err
// 		}
// 		team.Pokes = pokes
// 		teams = append(teams, &team)
// 	}
// 	if err = rows.Err(); err != nil {
// 		logger.Error("Error during rows iteration: %v", err)
// 		return nil, err
// 	}
// 	return teams, nil
// }
// func SelectTeamProto(ctx context.Context, tx *sql.Tx, teamId int64, hireTid int64, updateTs int64) (*MainServer.PokeTeam, error) {
// 	query := fmt.Sprintf(`
//     SELECT id, tid, poke_ids, hire_tids, create_ts, update_ts
//     FROM %s
//     WHERE id = $1 AND $2 = ANY(hire_tids) AND update_ts <= $3`, TableTeamName)
// 	row := tx.QueryRowContext(ctx, query, teamId, hireTid, updateTs)
// 	var (
// 		team    MainServer.PokeTeam
// 		pokeIds []int64
// 	)
// 	row.Scan(&team.Id, team.Tid, pq.Array(&pokeIds), pq.Array(&team.HireTids), &team.CreateTs, &team.UpdateTs)
// 	// for _, tidStr := range strings.Split(hireTidsString, ",") {
// 	// 	tid, err := strconv.ParseInt(tidStr, 10, 64)
// 	// 	if err != nil {
// 	// 		team.HireTids = append(team.HireTids, tid)
// 	// 	}
// 	// }
// 	// pokeIds := make([]int64, 0)
// 	// for _, pidStr := range strings.Split(pokeIdsString, ",") {
// 	// 	pid, err := strconv.ParseInt(pidStr, 10, 64)
// 	// 	if err != nil {
// 	// 		pokeIds = append(pokeIds, pid)
// 	// 	}
// 	// }
// 	pokes, err := poke.QueryPokesByIdsAndUpdateTs(ctx, tx, team.Tid, pokeIds, 0)
// 	if err != nil {
// 		return nil, err
// 	}
// 	team.Pokes = pokes
// 	return &team, nil
// }

package tool

import (
	"encoding/base64"
	"encoding/json"
	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// ProtoToBase64 - 将单个 Protobuf 对象序列化为 Base64 字符串
func ProtoToBase64(data proto.Message) (string, error) {
	// 序列化 Protobuf 数据为字节数组
	serializedData, err := proto.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to serialize proto message: %w", err)
	}

	// 将字节数组转换为 Base64 编码的字符串
	encodedString := base64.StdEncoding.EncodeToString(serializedData)
	return encodedString, nil
}

// ProtosToBase64 - 将多个 Protobuf 对象序列化为 Base64 字符串
func ProtosToBase64(datas []proto.Message) (string, error) {
	// 创建空的字节切片，用于存放所有序列化后的 Protobuf 数据
	serializedData := make([]byte, 0)

	// 遍历 Protobuf 对象数组，逐个序列化并追加到字节切片
	for _, data := range datas {
		// 序列化每个 Protobuf 对象
		dataBytes, err := proto.Marshal(data)
		if err != nil {
			return "", fmt.Errorf("failed to serialize proto message: %w", err)
		}
		serializedData = append(serializedData, dataBytes...)
	}

	// 将所有序列化的二进制数据转换为 Base64 编码的字符串
	encodedString := base64.StdEncoding.EncodeToString(serializedData)
	return encodedString, nil
}

// Base64ToProto - 将 Base64 字符串转换为 Protobuf 对象，泛型解码
func Base64ToProto(encodedString string, message proto.Message) error {
	// 将 Base64 字符串解码为字节数组
	decodedData, err := base64.StdEncoding.DecodeString(encodedString)
	if err != nil {
		return fmt.Errorf("failed to decode base64 string: %w", err)
	}

	// 将字节数组反序列化为 Protobuf 对象
	if err := proto.Unmarshal(decodedData, message); err != nil {
		return fmt.Errorf("failed to unmarshal proto message: %w", err)
	}

	return nil
}

// 例子
//
//	pokeFactory := func() proto.Message {
//	    return &MainServer.Poke{}
//	}
//
// Base64ToProtos - 将 Base64 字符串解码为 Protobuf 对象数组
func Base64ToProtos(encodedString string, messageFactory func() proto.Message) ([]proto.Message, error) {
	// 将 Base64 字符串解码为字节数组
	decodedData, err := base64.StdEncoding.DecodeString(encodedString)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 string: %w", err)
	}

	// 创建一个数组用于存储解码后的 Protobuf 对象
	var protoMessages []proto.Message
	offset := 0

	// 不断从 decodedData 中读取并反序列化 Protobuf 对象，直到数据用完
	for offset < len(decodedData) {
		// 每次从工厂函数中获取一个新的空 Protobuf 对象
		message := messageFactory()

		// 尝试解码 Protobuf 数据
		err := proto.Unmarshal(decodedData[offset:], message)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal proto message: %w", err)
		}

		// 将成功反序列化的 Protobuf 对象加入数组
		protoMessages = append(protoMessages, message)

		// 计算当前 Protobuf 对象的大小，并更新偏移量
		messageSize := proto.Size(message)
		offset += messageSize
	}

	return protoMessages, nil
}

func ProtoToJson(pb proto.Message) (string, error) {
	// 使用 protojson.Marshal 进行 Protobuf 消息到 JSON 的转换
	jsonBytes, err := protojson.Marshal(pb)
	if err != nil {
		return "", fmt.Errorf("failed to marshal proto to json: %w", err)
	}

	return string(jsonBytes), nil
}

func ProtosToJson(pbArray []proto.Message) (string, error) {
	content, err := json.Marshal(pbArray)
	return string(content), err
	// 初始化一个空的 JSON 数组
	// jsonArray := "["
	// for i, pb := range pbArray {
	// 	// 使用 protojson.Marshal 将每个 proto.Message 转为 JSON
	// 	jsonBytes, err := json.Marshal(pb)
	// 	if err != nil {
	// 		return "", fmt.Errorf("failed to marshal proto to json: %w", err)
	// 	}

	// 	// 将结果附加到 jsonArray 字符串
	// 	if i > 0 {
	// 		jsonArray += ","
	// 	}
	// 	jsonArray += string(jsonBytes)
	// }

	// // 结束 JSON 数组
	// jsonArray += "]"
	// return jsonArray, nil
}

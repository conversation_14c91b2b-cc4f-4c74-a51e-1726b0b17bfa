package tool

import (
	"encoding/json"
	"fmt"
	"strconv"
)

func GetJsonFromPayload(payload string) (map[string]interface{}, error) {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(payload), &data)
	if err != nil {
		return nil, err
	}
	return data, err
}

// 从 JSON payload 中提取字符串值
func GetStringFromPayload(key, payload string) (string, bool) {
	// 假设 payload 是一个 JSON 格式字符串
	var data map[string]interface{}
	err := json.Unmarshal([]byte(payload), &data)
	if err != nil {
		return "", false
	}

	// 假设键的值是字符串类型
	if value, ok := data[key].(string); ok {
		return value, true
	}
	return "", false
}

// 从 JSON payload 中提取整数值
func GetIntFromPayload(key, payload string) (int64, bool) {
	// 假设 payload 是一个 JSON 格式字符串
	var data map[string]interface{}
	err := json.Unmarshal([]byte(payload), &data)
	if err != nil {
		return 0, false
	}

	// 假设键的值是浮点数类型，并且是整数（Go 的 JSON 解析器会将所有数字解析为 float64）
	if value, ok := data[key].(float64); ok {
		return int64(value), true // 将 float64 转换为 int 返回
	}
	return 0, false
}
func GetBoolFromPayload(key, payload string) (bool, bool) {
	// 假设 payload 是一个 JSON 格式字符串
	var data map[string]interface{}
	err := json.Unmarshal([]byte(payload), &data)
	if err != nil {
		return false, false
	}

	// 假设键的值是浮点数类型，并且是整数（Go 的 JSON 解析器会将所有数字解析为 float64）
	if value, ok := data[key].(bool); ok {
		return value, true // 将 float64 转换为 int 返回
	}
	return false, false
}

func ConvertStringsToInts(input []string) ([]int64, error) {
	result := make([]int64, len(input)) // 预分配切片大小
	for i, str := range input {
		num, err := strconv.ParseInt(str, 10, 64) // 将字符串转换为 int64
		if err != nil {
			return nil, fmt.Errorf("failed to convert %q to int64: %v", str, err)
		}
		result[i] = num
	}
	return result, nil
}

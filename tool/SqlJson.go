package tool

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"
)

func RetrieveMap(ctx context.Context , db *sql.DB, sSql string) ([]map[string]interface{}, error) {
    // 查询
    rows, err := db.QueryContext(context.Background(), sSql)
    if err != nil {
        log.Printf("Failed to execute query: %s, error: %v\n", sSql, err)
        return nil, err
    }
    defer rows.Close()

    // 获取列信息
    columns, err := rows.Columns()
    if err != nil {
        log.Println("Failed to get columns:", err)
        return nil, err
    }
    count := len(columns)

    // 初始化数据
    mData := make([]map[string]interface{}, 0, 10)
    values := make([]interface{}, count)
    valPointers := make([]interface{}, count)

    // 遍历结果
    for rows.Next() {
        for i := 0; i < count; i++ {
            valPointers[i] = &values[i]
        }

        if err := rows.Scan(valPointers...); err != nil {
            log.Println("Failed to scan row:", err)
            return nil, err
        }

        entry := make(map[string]interface{}, count)
        for i, col := range columns {
            entry[col] = parseValue(values[i])
        }
        mData = append(mData, entry)
    }

    // 检查迭代中的错误
    if err := rows.Err(); err != nil {
        log.Println("Error occurred during rows iteration:", err)
        return nil, err
    }

    return mData, nil
}

// 解析值为适当的类型
func parseValue(val interface{}) interface{} {
    if b, ok := val.([]byte); ok {
        var res map[string]interface{}
        if err := json.Unmarshal(b, &res); err != nil {
            return string(b)
        }
        return res
    }
    return val
}

func RetrieveJSON(db *sql.DB, sqlString string) (string, error) {
	rows, err := db.Query(sqlString)
	if err != nil {
		return "", err
	}
	defer rows.Close()
	columns, err := rows.Columns()
	if err != nil {
		return "", err
	}
	count := len(columns)
	tableData := make([]map[string]interface{}, 0)
	values := make([]interface{}, count)
	valuePtrs := make([]interface{}, count)
	for rows.Next() {
		for i := 0; i < count; i++ {
			valuePtrs[i] = &values[i]
		}
		rows.Scan(valuePtrs...)
		entry := make(map[string]interface{})
		for i, col := range columns {
			var v interface{}
			val := values[i]
			b, ok := val.([]byte)
			if ok {
				v = string(b)
			} else {
				v = val
			}
			entry[col] = v
		}
		tableData = append(tableData, entry)
	}
	jsonData, err := json.Marshal(tableData)
	if err != nil {
		return "", err
	}
	// fmt.Println(string(jsonData))
	return string(jsonData), nil
}

func MapToJson(data map[string]interface{}, tableName string) (string, string, []interface{}, error) {
	// Prepare for insert and update SQL generation
	var columns, placeholders, updates []string
	var values []interface{}
	idx := 1

	// Iterate through the map to generate SQL parts
	for k, v := range data {
		columns = append(columns, k)
		placeholders = append(placeholders, fmt.Sprintf("$%d", idx))
		updates = append(updates, fmt.Sprintf("%s = $%d", k, idx))
		values = append(values, v)
		idx++
	}

	// Generate SQL statements
	insertSQL := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)", tableName, strings.Join(columns, ", "), strings.Join(placeholders, ", "))
	updateSQL := fmt.Sprintf("UPDATE %s SET %s WHERE id = $%d", tableName, strings.Join(updates, ", "), idx)

	return insertSQL, updateSQL, values, nil
}

func JsonToSQL(jsonStr string, tableName string) (string, string, []interface{}, error) {
	// Parse JSON into a map
	data := make(map[string]interface{})
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", "", nil, err
	}
	return MapToJson(data, tableName)
}

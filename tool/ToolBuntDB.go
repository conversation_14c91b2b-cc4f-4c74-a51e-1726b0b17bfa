package tool

import (
	"fmt"
	"sync"
	"time"

	"github.com/tidwall/buntdb"
	// "google.golang.org/protobuf/encoding/protojson"
)

// var db *buntdb.DB
var (
	db             *buntdb.DB
	createdIndexes sync.Map
)

// 初始化 BuntDB 并创建空间索引
func InitBuntdb() {
	var err error
	db, err = buntdb.Open(":memory:") // 使用内存数据库
	if err != nil {
		panic(fmt.Sprintf("无法打开 BuntDB 数据库: %v", err))
	}
	db.Indexes()
}

//	func CreateSpatialIndexIfNeed(indexName string) error {
//		err := db.Update(func(tx *buntdb.Tx) error {
//			if tx.IndexExists(indexName) {
//				log.Printf("Index '%s' already exists.", indexName)
//				return nil
//			}
//			err := tx.CreateSpatialIndex(indexName, indexName+":*", buntdb.IndexRect)
//			if err != nil {
//				return fmt.Errorf("failed to create spatial index '%s': %w", indexName, err)
//			}
//			log.Printf("Index '%s' created successfully.", indexName)
//			return nil
//		})
//		return err
//	}
func CreateRectIndexIfNeed(indexName string) error {
	// 使用 sync.Map 的 LoadOrStore 方法来检查和标记索引
	_, loaded := createdIndexes.LoadOrStore(indexName, struct{}{})
	if loaded {
		// 索引已经创建过，直接返回
		return nil
	}

	// 如果没有创建过，尝试创建索引
	err := db.Update(func(tx *buntdb.Tx) error {
		err := tx.CreateSpatialIndex(indexName, indexName+":*", buntdb.IndexRect)
		if err != nil {
			if err != buntdb.ErrIndexExists {
				// 如果创建失败，从 map 中移除标记
				createdIndexes.Delete(indexName)
				return fmt.Errorf("failed to create spatial index '%s': %w", indexName, err)
			}
			fmt.Printf("failed to create spatial index: %v\n", err)
		}
		return nil
	})

	return err
}

//	func CreateRectIndexIfNeed(indexName string) error {
//		err := db.View(func(tx *buntdb.Tx) error {
//			_, err := tx.GetLess(indexName)
//			if err != nil {
//				return err
//			}
//			err = db.Update(func(tx *buntdb.Tx) error {
//				err := tx.CreateSpatialIndex(indexName, indexName+":*", buntdb.IndexRect)
//				if err != nil {
//					return fmt.Errorf("failed to create spatial index '%s': %w", indexName, err)
//				}
//				// log.Printf("Index '%s' created successfully.", indexName)
//				return nil
//				// tx.CreateSpatialIndex(indexName, indexName+":*", buntdb.IndexRect)
//			})
//			return err
//		})
//		return err
//		// db.GetLess(indexName)
//		// _, err := db.Get(indexName)
//		// if !db.IndexExists(indexName) {
//		// 	err := db.Update(func(tx *buntdb.Tx) error {
//		// 		err := tx.CreateSpatialIndex(indexName, indexName+":*", buntdb.IndexRect)
//		// 		if err != nil {
//		// 			return fmt.Errorf("failed to create spatial index '%s': %w", indexName, err)
//		// 		}
//		// 		// log.Printf("Index '%s' created successfully.", indexName)
//		// 		return nil
//		// 		// tx.CreateSpatialIndex(indexName, indexName+":*", buntdb.IndexRect)
//		// 	})
//		// 	return err
//		// }
//		// return nil
//	}
func Set(dbKey, value string) error {
	err := db.Update(func(tx *buntdb.Tx) error {
		_, _, err := tx.Set(dbKey, value, &buntdb.SetOptions{
			Expires: true,
			TTL:     60 * time.Second * 5, //过期时间5分钟
		})
		if err != nil {
			return fmt.Errorf("failed to set key '%s': %w", dbKey, err)
		}
		return nil
	})
	return err
}

// Remove deletes a key-value pair from the database.
func Remove(dbKey string) error {
	err := db.Update(func(tx *buntdb.Tx) error {
		_, err := tx.Delete(dbKey)
		if err != nil {
			// If the key doesn't exist, buntdb.Delete returns an error
			if err == buntdb.ErrNotFound {
				return fmt.Errorf("key '%s' not found: %w", dbKey, err)
			}
			return fmt.Errorf("failed to remove key '%s': %w", dbKey, err)
		}
		return nil
	})
	return err
}

// func Set(dbkey string, value string) error {
// 	err := db.Update(func(tx *buntdb.Tx) error {
// 		// 使用 BuntDB 的地理空间索引存储位置信息
// 		_, _, err := tx.Set(dbkey, value, &buntdb.SetOptions{
// 			Expires: false,
// 			// Rect:    buntdb.Rect(float64(userLoc.X), float64(userLoc.Y), float64(userLoc.X), float64(userLoc.Y)), // 空间索引
// 		})
// 		return err
// 	})
// 	return err
// }

func Get(dbKey string) (string, error) {
	var value string
	err := db.View(func(tx *buntdb.Tx) error {
		val, err := tx.Get(dbKey)
		if err != nil {
			// Return the error to indicate the key was not found or other issues
			return err
		}
		value = val // Assign the value to the outer variable
		return nil  // No error
	})

	if err != nil {
		// Handle and return the error appropriately
		return "", fmt.Errorf("failed to get value for key '%s': %w", dbKey, err)
	}
	// Return the retrieved value
	return value, nil
}

func DB() *buntdb.DB {
	return db
}

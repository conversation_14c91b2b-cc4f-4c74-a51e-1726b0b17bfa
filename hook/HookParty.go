package hook

import (
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/rtapi"
	"github.com/heroiclabs/nakama-common/runtime"
)

func hookPartyMethod(initializer runtime.Initializer) {
	// initializer.RegisterBeforeRt("PartyCreate", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	// 	envelope, ok := in.Message.(*rtapi.Envelope_PartyCreate)
	// 	if !ok {
	// 		return nil, runtime.NewError("error getting envelope as PartyCreate envelope", 13)
	// 	}
	// 	// userId := envelope.Party.Self.UserId
	// 	// partyId := envelope.Party.PartyId
	// 	// updatePatyInfo(userId, partyId, envelope.Party.Leader, envelope.Party.Presences)
	// 	return in, nil
	// })
	hookNames := []string{"PartyCreate", "PartyJoin", "PartyLeave", "PartyPromote", "PartyAccept", "PartyRemove", "PartyClose"}
	for i := range hookNames {
		initializer.RegisterAfterRt(hookNames[i], func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out *rtapi.Envelope, in *rtapi.Envelope) error {
			// if hookNames[i] == "PartyCreate" {

			// }
			// ok := false
			// if hookNames[i] == "PartyCreate" {
			// 	envelope, ok = in.Message.(*rtapi.Envelope_PartyCreate)
			// } else if hookNames[i] == "PartyJoin" {
			// 	envelope, ok = in.Message.(*rtapi.Envelope_PartyJoin)
			// } else if hookNames[i] == "PartyLeave" {
			// 	envelope, ok = in.Message.(*rtapi.Envelope_PartyLeave)
			// } else if hookNames[i] == "PartyPromote" {
			// 	envelope, ok = in.Message.(*rtapi.Envelope_PartyPromote)
			// } else if hookNames[i] == "PartyAccept" {
			// 	envelope, ok = in.Message.(*rtapi.Envelope_PartyAccept)
			// } else if hookNames[i] == "PartyClose" {
			// 	envelope, ok = in.Message.(*rtapi.Envelope_PartyClose)
			// }
			// if !ok {
			// 	return runtime.NewError("error getting envelope as "+hookNames[i]+" envelope", 13)
			// }
			trainer := tool.GetActiveTrainerByCtx(ctx)
			if trainer == nil {
				return runtime.NewError("Not found tid", 404)
			}
			envelope, ok := out.Message.(*rtapi.Envelope_Party)
			if !ok {
				return runtime.NewError("error getting envelope as "+hookNames[i]+" envelope", 13)
			}
			partyId := envelope.Party.PartyId
			updatePatyInfo(trainer.Id, partyId, envelope.Party.Leader, envelope.Party.Presences)
			//
			if hookNames[i] == "PartyClose" || hookNames[i] == "PartyLeave" {
				tool.GetGlobalPartyMap().Remove(trainer.Id)
			}
			return nil
		})
	}
	// initializer.RegisterAfterRt("PartyCreate", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {

	// initializer.RegisterAfterRt("PartyCreate", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, out, in *rtapi.Envelope) error {
	// 	envelope, ok := in.Message.(*rtapi.Envelope_Party)
	// 	if !ok {
	// 		return runtime.NewError("error getting envelope as PartyCreate envelope", 13)
	// 	}
	// 	userId := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	// 	partyId := envelope.Party.PartyId
	// 	updatePatyInfo(userId, partyId, envelope.Party.Leader, envelope.Party.Presences)
	// 	return nil
	// })

	// initializer.RegisterBeforeAddFriends(func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *api.CreateGroupRequest) (*api.CreateGroupRequest, error) {
	// 	//if containsProfanity(in.Name) {
	// 	//   return nil, runtime.NewError("profanity detected", 3)
	// 	// }

	// 	r eturn in, nil
	// })
}
func updatePatyInfo(tid int64, partyId string, leader *rtapi.UserPresence, presences []*rtapi.UserPresence) {
	if len(presences) <= 0 { //party没人就直接删除
		tool.GetGlobalPartyMap().Remove(tid)
		return
	}
	partyInfo, exists := tool.GetGlobalPartyMap().Get(tid)
	if !exists {
		partyInfo = &tool.PartyInfo{
			Trainers: make(map[int64]*MainServer.Trainer),
		}
	}
	//  else if partyInfo.Leader.Uid == userId &&  {

	// }
	// Uids := []string{}
	// trainers := []*MainServer.Trainer{}
	for i := range presences {
		// Uids = append(Uids, presences[i].UserId)
		trainer := tool.GetActiveTrainerByUid(presences[i].UserId)
		if trainer != nil {
			partyInfo.Trainers[trainer.Id] = trainer
			// trainers = append(trainers, trainer)
		}
	}
	// partyInfo.Trainers = trainers
	partyInfo.PartyId = partyId
	partyInfo.Leader = tool.GetActiveTrainerByUid(leader.UserId)
	tool.GetGlobalPartyMap().Set(tid, partyInfo)
}

package hook

import (
	"context"
	"database/sql"
	"go-nakama-poke/battle"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/rtapi"
	"github.com/heroiclabs/nakama-common/runtime"
)

var (
	errInternal = runtime.NewError("internal server error", 13)
)

func hookMatchMethod(initializer runtime.Initializer) {
	initializer.RegisterBeforeRt("MatchmakerAdd", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
		message, ok := in.Message.(*rtapi.Envelope_MatchmakerAdd)
		if !ok {
			return nil, errInternal
		}
		matchMaker := &MainServer.BattleMatchMaker{}
		if maker, ok := message.MatchmakerAdd.StringProperties["maker"]; ok {
			matchMaker = &MainServer.BattleMatchMaker{}
			err := tool.Base64ToProto(maker, matchMaker)
			if err != nil {
				return nil, errInternal
			}
		}
		// message.MatchmakerAdd.StringProperties = matchMakerToProperties(matchMaker)
		message.MatchmakerAdd.Query = battle.MatchMakerToQuery(matchMaker)
		message.MatchmakerAdd.MinCount = 1
		message.MatchmakerAdd.MaxCount = 1
		return in, nil
	})
	initializer.RegisterBeforeRt("PartyMatchmakerAdd", func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
		message, ok := in.Message.(*rtapi.Envelope_PartyMatchmakerAdd)
		if !ok {
			return nil, errInternal
		}
		matchMaker := &MainServer.BattleMatchMaker{}
		if maker, ok := message.PartyMatchmakerAdd.StringProperties["maker"]; ok {
			matchMaker = &MainServer.BattleMatchMaker{}
			err := tool.Base64ToProto(maker, matchMaker)
			if err != nil {
				return nil, errInternal
			}
		}
		trainer := tool.GetActiveTrainerByCtx(ctx)
		if trainer == nil {
			return nil, errInternal
		}
		// message.PartyMatchmakerAdd.StringProperties = matchMakerToProperties(matchMaker)
		message.PartyMatchmakerAdd.Query = battle.MatchMakerToQuery(matchMaker)
		partyInfo, exists := tool.GetGlobalPartyMap().Get(trainer.Id)
		if exists {
			if len(partyInfo.Trainers) > 1 { //先不进行不对等的匹配
				message.PartyMatchmakerAdd.MinCount = 2
				message.PartyMatchmakerAdd.MaxCount = 2
			} else {
				message.PartyMatchmakerAdd.MinCount = 1
				message.PartyMatchmakerAdd.MaxCount = 1
			}
		}
		return in, nil
	})
	// hookNames := []string{"MatchmakerAdd"}
	// for i := range hookNames {
	// 	initializer.RegisterBeforeRt(hookNames[i], func(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, in *rtapi.Envelope) (*rtapi.Envelope, error) {
	// 		message, ok := in.Message.(*rtapi.Envelope_MatchmakerAdd)
	// 		if !ok {
	// 			return nil, errInternal
	// 		}

	// 		return in, nil
	// 	})
	// }
	initializer.RegisterMatchmakerMatched(MakeMatch)
}

func MakeMatch(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, entries []runtime.MatchmakerEntry) (string, error) {
	for _, e := range entries {
		logger.Info("Matched user '%s' named '%s'", e.GetPresence().GetUserId(), e.GetPresence().GetUsername())

		for k, v := range e.GetProperties() {
			logger.Info("Matched on '%s' value '%v'", k, v)
		}
	}

	// matchId, err := nk.MatchCreate(ctx, "pingpong", map[string]interface{}{"invited": entries})

	// if err != nil {
	// 	return "", err
	// }

	return "matchId", nil
}

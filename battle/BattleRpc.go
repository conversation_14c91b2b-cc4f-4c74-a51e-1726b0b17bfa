package battle

// import (
// 	"context"
// 	"database/sql"
// 	"encoding/json"
// 	"sync"
// 	"time"

// 	"github.com/heroiclabs/nakama-common/runtime"
// )
// func SendMessageToServer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	// 获取调用者的客户端 ID
// 	sessionID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	subject := "You've unlocked level 100!"
// 	content := map[string]interface{}{
// 		"reward_coins": 1000,
// 	}
// 	receiverID := sessionID
// 	senderID := "dcb891ea-a311-4681-9213-6741351c9994"
// 	code := 101
// 	persistent := true

// 	nk.NotificationSend(ctx, receiverID, subject, content, code, senderID, persistent)

// 	return "{}", nil
// }

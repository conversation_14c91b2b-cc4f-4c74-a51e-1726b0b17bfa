package battle

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
	"google.golang.org/protobuf/encoding/protojson"
)

const TableBattleRecords = "battle_records"

// 使用生成的proto结构体，不再需要自定义结构体

// getOtherInfoOrDefault 获取其他信息或默认值
func getOtherInfoOrDefault(otherInfo *MainServer.BattleRecordOtherInfo) *MainServer.BattleRecordOtherInfo {
	if otherInfo != nil {
		return otherInfo
	}
	return &MainServer.BattleRecordOtherInfo{}
}

// InitBattleRecords 初始化战斗记录模块
func InitBattleRecords(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createBattleRecordsTableIfNotExists(ctx, logger, db)
}

// createBattleRecordsTableIfNotExists 创建战斗记录表
func createBattleRecordsTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTableSQL := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,                    -- 自增长 ID
            battle_id VARCHAR(255) NOT NULL,             -- 战斗ID
            tid BIGINT NOT NULL,                         -- 训练师ID
            battle_type INT NOT NULL,                    -- 战斗类型
            poke_names TEXT NOT NULL,                    -- 训练师使用的宝可梦名称，逗号分割
            opponent_poke_names TEXT NOT NULL,           -- 对手宝可梦名称，逗号分割
            is_winner BOOLEAN NOT NULL DEFAULT FALSE,    -- 是否胜利
            opponent_tid BIGINT NOT NULL DEFAULT 0,      -- 对手训练师ID (NPC/野生为0)
            battle_result VARCHAR(10) NOT NULL DEFAULT 'lose', -- 战斗结果
            battle_start_ts BIGINT NOT NULL,             -- 战斗开始时间戳
            battle_end_ts BIGINT NOT NULL,               -- 战斗结束时间戳
            npc_name_id VARCHAR(55) NOT NULL DEFAULT '', -- NPC名称(野生为空字符串)
            poke_team_name_id VARCHAR(55) NOT NULL DEFAULT '', -- 宝可梦队名称
            other_info JSONB NOT NULL DEFAULT '{}',      -- 其他信息
            create_ts BIGINT NOT NULL,                   -- 创建时间戳
            update_ts BIGINT NOT NULL                    -- 更新时间戳
        );`, TableBattleRecords)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("Failed to create table %s: %v", TableBattleRecords, err)
	} else {
		logger.Info("Successfully created table %s", TableBattleRecords)

		// 创建索引以优化查询性能
		createIndexSQL := fmt.Sprintf(`
            CREATE INDEX IF NOT EXISTS idx_%[1]s_tid ON %[1]s (tid);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_battle_type ON %[1]s (battle_type);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_battle_start_ts ON %[1]s (battle_start_ts);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_battle_end_ts ON %[1]s (battle_end_ts);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_is_winner ON %[1]s (is_winner);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_npc_name_id ON %[1]s (npc_name_id);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_poke_team_name_id ON %[1]s (poke_team_name_id);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_tid_battle_start_ts ON %[1]s (tid, battle_start_ts);
            CREATE INDEX IF NOT EXISTS idx_%[1]s_composite ON %[1]s (tid, battle_start_ts);
        `, TableBattleRecords)

		_, err = db.ExecContext(ctx, createIndexSQL)
		if err != nil {
			logger.Error("Failed to create indexes on table %s: %v", TableBattleRecords, err)
		} else {
			logger.Info("Successfully created indexes on table %s", TableBattleRecords)
		}
	}
}

// RecordBattleData 记录战斗数据
func RecordBattleData(ctx context.Context, logger runtime.Logger, tx *sql.Tx, battleID string, battleType MainServer.BattleType, winPokes []*MainServer.Poke, losePokes []*MainServer.Poke, battleStartTs int64, npcNameId string, pokeTeamNameId string) error {
	return RecordBattleDataWithOtherInfo(ctx, logger, tx, battleID, battleType, winPokes, losePokes, battleStartTs, npcNameId, pokeTeamNameId, nil)
}

// RecordBattleDataWithOtherInfo 记录战斗数据（包含其他信息）
func RecordBattleDataWithOtherInfo(ctx context.Context, logger runtime.Logger, tx *sql.Tx, battleID string, battleType MainServer.BattleType, winPokes []*MainServer.Poke, losePokes []*MainServer.Poke, battleStartTs int64, npcNameId string, pokeTeamNameId string, otherInfo *MainServer.BattleRecordOtherInfo) error {
	nowTs := time.Now().UnixMilli()

	// 按训练师分组宝可梦
	trainerPokes := make(map[int64][]*MainServer.Poke)
	trainerResults := make(map[int64]bool) // true表示胜利，false表示失败

	// 收集胜利方训练师的宝可梦
	for _, poke := range winPokes {
		trainerPokes[poke.Tid] = append(trainerPokes[poke.Tid], poke)
		trainerResults[poke.Tid] = true
	}

	// 收集失败方训练师的宝可梦
	for _, poke := range losePokes {
		trainerPokes[poke.Tid] = append(trainerPokes[poke.Tid], poke)
		trainerResults[poke.Tid] = false
	}

	// 为每个训练师创建一条记录
	for tid, pokes := range trainerPokes {
		// 获取该训练师的宝可梦名称列表
		var pokeNames []string
		for _, poke := range pokes {
			pokeNames = append(pokeNames, poke.Name)
		}

		// 获取对手宝可梦名称列表
		var opponentPokeNames []string
		isWinner := trainerResults[tid]
		var opponentTid int64 = 0

		if isWinner {
			// 如果是胜利方，对手是失败方的宝可梦
			for _, poke := range losePokes {
				opponentPokeNames = append(opponentPokeNames, poke.Name)
				if opponentTid == 0 {
					opponentTid = poke.Tid
				}
			}
		} else {
			// 如果是失败方，对手是胜利方的宝可梦
			for _, poke := range winPokes {
				opponentPokeNames = append(opponentPokeNames, poke.Name)
				if opponentTid == 0 {
					opponentTid = poke.Tid
				}
			}
		}

		// 创建战斗记录
		record := &MainServer.BattleRecordInfo{
			BattleId:          battleID,
			Tid:               tid,
			BattleType:        battleType,
			PokeNames:         strings.Join(pokeNames, ","),
			OpponentPokeNames: strings.Join(opponentPokeNames, ","),
			IsWinner:          isWinner,
			OpponentTid:       opponentTid,
			BattleResult:      getBattleResult(isWinner),
			BattleStartTs:     battleStartTs,
			BattleEndTs:       nowTs,
			NpcNameId:         npcNameId,
			PokeTeamNameId:    pokeTeamNameId,
			OtherInfo:         getOtherInfoOrDefault(otherInfo),
			CreateTs:          nowTs,
			UpdateTs:          nowTs,
		}

		err := insertBattleRecord(ctx, tx, record)
		if err != nil {
			logger.Error("Failed to record battle data for trainer %d: %v", tid, err)
			return err
		}
	}

	logger.Info("Successfully recorded battle data for battle %s", battleID)
	return nil
}

// getBattleResult 根据胜负状态获取战斗结果字符串
func getBattleResult(isWinner bool) string {
	if isWinner {
		return "win"
	}
	return "lose"
}

// insertBattleRecord 插入单条战斗记录
func insertBattleRecord(ctx context.Context, tx *sql.Tx, record *MainServer.BattleRecordInfo) error {
	// 将 other_info proto 转换为 JSON 字符串
	otherInfoJSON := "{}"
	if record.OtherInfo != nil {
		if jsonStr, err := tool.ProtoToJson(record.OtherInfo); err == nil {
			otherInfoJSON = jsonStr
		}
	}

	insertSQL := fmt.Sprintf(`
		INSERT INTO %s (battle_id, tid, battle_type, poke_names, opponent_poke_names, is_winner, opponent_tid, battle_result, battle_start_ts, battle_end_ts, npc_name_id, poke_team_name_id, other_info, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
	`, TableBattleRecords)

	_, err := tx.ExecContext(ctx, insertSQL,
		record.BattleId,
		record.Tid,
		int(record.BattleType),
		record.PokeNames,
		record.OpponentPokeNames,
		record.IsWinner,
		record.OpponentTid,
		record.BattleResult,
		record.BattleStartTs,
		record.BattleEndTs,
		record.NpcNameId,
		record.PokeTeamNameId,
		otherInfoJSON,
		record.CreateTs,
		record.UpdateTs,
	)

	return err
}

// GetBattleStatistics 获取战斗统计信息
// timeRangeMs: 时间范围（毫秒），例如 3600000 表示1小时，60000 表示1分钟
// opponentPokeName: 对手宝可梦名称
func GetBattleStatistics(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, opponentPokeName string, timeRangeMs int64) (*MainServer.BattleStatistics, error) {
	nowTs := time.Now().UnixMilli()
	startTs := nowTs - timeRangeMs

	// 构建查询条件 - 查询训练师与特定对手宝可梦的战斗统计
	// 使用LIKE查询来匹配逗号分割的宝可梦名称
	query := fmt.Sprintf(`
		SELECT
			COUNT(*) as battle_count,
			SUM(CASE WHEN is_winner = true THEN 1 ELSE 0 END) as win_count,
			SUM(CASE WHEN is_winner = false THEN 1 ELSE 0 END) as lose_count
		FROM %s
		WHERE tid = $1 AND (
			opponent_poke_names = $2 OR
			opponent_poke_names LIKE $3 OR
			opponent_poke_names LIKE $4 OR
			opponent_poke_names LIKE $5
		) AND battle_start_ts >= $6
	`, TableBattleRecords)

	// 构建LIKE查询参数
	exactMatch := opponentPokeName
	startsWith := opponentPokeName + ",%"
	endsWith := "%," + opponentPokeName
	contains := "%," + opponentPokeName + ",%"

	var battleCount, winCount, loseCount int32
	err := tx.QueryRowContext(ctx, query, tid, exactMatch, startsWith, endsWith, contains, startTs).Scan(&battleCount, &winCount, &loseCount)
	if err != nil {
		logger.Error("Failed to query battle statistics: %v", err)
		return nil, err
	}

	// 计算胜率
	var winRate float64 = 0
	if battleCount > 0 {
		winRate = float64(winCount) / float64(battleCount) * 100
	}

	statistics := &MainServer.BattleStatistics{
		Tid:              tid,
		OpponentPokeName: opponentPokeName,
		BattleCount:      battleCount,
		WinCount:         winCount,
		LoseCount:        loseCount,
		WinRate:          winRate,
		TimeRangeMs:      timeRangeMs,
	}

	logger.Info("Battle statistics for tid %d vs opponent poke %s in %d ms: %d battles, %d wins, %.2f%% win rate",
		tid, opponentPokeName, timeRangeMs, battleCount, winCount, winRate)

	return statistics, nil
}

// GetBattleCount 获取指定条件下的战斗次数（简化版本）
// opponentPokeName: 对手宝可梦名称
func GetBattleCount(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, opponentPokeName string, timeRangeMs int64) (int32, error) {
	nowTs := time.Now().UnixMilli()
	startTs := nowTs - timeRangeMs

	query := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM %s
		WHERE tid = $1 AND (
			opponent_poke_names = $2 OR
			opponent_poke_names LIKE $3 OR
			opponent_poke_names LIKE $4 OR
			opponent_poke_names LIKE $5
		) AND battle_start_ts >= $6
	`, TableBattleRecords)

	// 构建LIKE查询参数
	exactMatch := opponentPokeName
	startsWith := opponentPokeName + ",%"
	endsWith := "%," + opponentPokeName
	contains := "%," + opponentPokeName + ",%"

	var count int32
	err := tx.QueryRowContext(ctx, query, tid, exactMatch, startsWith, endsWith, contains, startTs).Scan(&count)
	if err != nil {
		logger.Error("Failed to query battle count: %v", err)
		return 0, err
	}

	logger.Info("Battle count for tid %d vs opponent poke %s in %d ms: %d battles", tid, opponentPokeName, timeRangeMs, count)
	return count, nil
}

// GetBattleRecords 获取战斗记录列表
func GetBattleRecords(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, opponentPokeName string, timeRangeMs int64, limit int32) ([]*MainServer.BattleRecordInfo, error) {
	nowTs := time.Now().UnixMilli()
	startTs := nowTs - timeRangeMs

	query := fmt.Sprintf(`
		SELECT id, battle_id, tid, battle_type, poke_names, opponent_poke_names, is_winner, opponent_tid, battle_result, battle_start_ts, battle_end_ts, npc_name_id, poke_team_name_id, other_info, create_ts, update_ts
		FROM %s
		WHERE tid = $1 AND (
			opponent_poke_names = $2 OR
			opponent_poke_names LIKE $3 OR
			opponent_poke_names LIKE $4 OR
			opponent_poke_names LIKE $5
		) AND battle_start_ts >= $6
		ORDER BY battle_start_ts DESC
		LIMIT $7
	`, TableBattleRecords)

	// 构建LIKE查询参数
	exactMatch := opponentPokeName
	startsWith := opponentPokeName + ",%"
	endsWith := "%," + opponentPokeName
	contains := "%," + opponentPokeName + ",%"

	rows, err := tx.QueryContext(ctx, query, tid, exactMatch, startsWith, endsWith, contains, startTs, limit)
	if err != nil {
		logger.Error("Failed to query battle records: %v", err)
		return nil, err
	}
	defer rows.Close()

	var records []*MainServer.BattleRecordInfo
	for rows.Next() {
		record := &MainServer.BattleRecordInfo{}
		var battleType int
		var otherInfoJSON string
		err := rows.Scan(
			&record.Id,
			&record.BattleId,
			&record.Tid,
			&battleType,
			&record.PokeNames,
			&record.OpponentPokeNames,
			&record.IsWinner,
			&record.OpponentTid,
			&record.BattleResult,
			&record.BattleStartTs,
			&record.BattleEndTs,
			&record.NpcNameId,
			&record.PokeTeamNameId,
			&otherInfoJSON,
			&record.CreateTs,
			&record.UpdateTs,
		)
		if err != nil {
			logger.Error("Failed to scan battle record: %v", err)
			return nil, err
		}
		record.BattleType = MainServer.BattleType(battleType)

		// 解析 other_info JSON 到 proto 结构体
		record.OtherInfo = &MainServer.BattleRecordOtherInfo{}
		if otherInfoJSON != "" && otherInfoJSON != "{}" {
			if err := protojson.Unmarshal([]byte(otherInfoJSON), record.OtherInfo); err != nil {
				logger.Error("Failed to unmarshal other_info JSON: %v", err)
				// 使用默认值
				record.OtherInfo = &MainServer.BattleRecordOtherInfo{}
			}
		}

		records = append(records, record)
	}

	logger.Info("Found %d battle records for tid %d vs opponent poke %s", len(records), tid, opponentPokeName)
	return records, nil
}

// GetBattleStatisticsByBattleType 按战斗类型获取统计信息
func GetBattleStatisticsByBattleType(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, opponentPokeName string, battleType MainServer.BattleType, timeRangeMs int64) (*MainServer.BattleStatistics, error) {
	nowTs := time.Now().UnixMilli()
	startTs := nowTs - timeRangeMs

	query := fmt.Sprintf(`
		SELECT
			COUNT(*) as battle_count,
			SUM(CASE WHEN is_winner = true THEN 1 ELSE 0 END) as win_count,
			SUM(CASE WHEN is_winner = false THEN 1 ELSE 0 END) as lose_count
		FROM %s
		WHERE tid = $1 AND (
			opponent_poke_names = $2 OR
			opponent_poke_names LIKE $3 OR
			opponent_poke_names LIKE $4 OR
			opponent_poke_names LIKE $5
		) AND battle_type = $6 AND battle_start_ts >= $7
	`, TableBattleRecords)

	// 构建LIKE查询参数
	exactMatch := opponentPokeName
	startsWith := opponentPokeName + ",%"
	endsWith := "%," + opponentPokeName
	contains := "%," + opponentPokeName + ",%"

	var battleCount, winCount, loseCount int32
	err := tx.QueryRowContext(ctx, query, tid, exactMatch, startsWith, endsWith, contains, int(battleType), startTs).Scan(&battleCount, &winCount, &loseCount)
	if err != nil {
		logger.Error("Failed to query battle statistics by type: %v", err)
		return nil, err
	}

	// 计算胜率
	var winRate float64 = 0
	if battleCount > 0 {
		winRate = float64(winCount) / float64(battleCount) * 100
	}

	statistics := &MainServer.BattleStatistics{
		Tid:              tid,
		OpponentPokeName: opponentPokeName,
		BattleCount:      battleCount,
		WinCount:         winCount,
		LoseCount:        loseCount,
		WinRate:          winRate,
		TimeRangeMs:      timeRangeMs,
	}

	logger.Info("Battle statistics for tid %d vs opponent poke %s, battle type %d in %d ms: %d battles, %d wins, %.2f%% win rate",
		tid, opponentPokeName, int(battleType), timeRangeMs, battleCount, winCount, winRate)

	return statistics, nil
}

// RpcGetBattleStatistics RPC函数：获取战斗统计信息
func RpcGetBattleStatistics(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcGetBattleStatisticsRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 设置默认值
	timeRangeMs := request.TimeRangeMs
	if timeRangeMs == 0 {
		timeRangeMs = 3600000 // 默认1小时
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 查询战斗统计
	statistics, err := GetBattleStatistics(ctx, logger, tx, trainer.Id, request.OpponentPokeName, timeRangeMs)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果 - 使用tool.ProtoToBase64
	return tool.ProtoToBase64(statistics)
}

// RpcGetBattleCount RPC函数：获取战斗次数
func RpcGetBattleCount(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证用户身份
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	if userID == "" {
		return "", runtime.NewError("用户未登录", 401)
	}

	// 解析请求参数
	var request MainServer.RpcGetBattleCountRequest
	err := tool.Base64ToProto(payload, &request)
	if err != nil {
		return "", runtime.NewError("无效的请求数据: "+err.Error(), 400)
	}

	// 设置默认值
	timeRangeMs := request.TimeRangeMs
	if timeRangeMs == 0 {
		timeRangeMs = 3600000 // 默认1小时
	}

	// 获取训练师信息
	trainer := tool.GetActiveTrainerByUid(userID)
	if trainer == nil {
		return "", runtime.NewError("训练师信息不存在", 404)
	}

	// 创建事务
	tx, err := db.Begin()
	if err != nil {
		return "", runtime.NewError("Failed to begin transaction", 500)
	}
	defer tx.Rollback()

	// 查询战斗次数
	count, err := GetBattleCount(ctx, logger, tx, trainer.Id, request.OpponentPokeName, timeRangeMs)
	if err != nil {
		return "", err
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return "", runtime.NewError("Failed to commit transaction", 500)
	}

	// 返回结果 - 使用proto结构体
	result := &MainServer.RpcGetBattleCountResponse{
		Tid:              trainer.Id,
		OpponentPokeName: request.OpponentPokeName,
		BattleCount:      count,
		TimeRangeMs:      timeRangeMs,
	}

	// 使用tool.ProtoToBase64
	return tool.ProtoToBase64(result)
}

// GetBattleRecordsByNpcName 根据NPC名称获取战斗记录
func GetBattleRecordsByNpcName(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, npcNameId string, timeRangeMs int64, limit int32) ([]*MainServer.BattleRecordInfo, error) {
	nowTs := time.Now().UnixMilli()
	startTs := nowTs - timeRangeMs

	query := fmt.Sprintf(`
		SELECT id, battle_id, tid, battle_type, poke_names, opponent_poke_names, is_winner, opponent_tid, battle_result, battle_start_ts, battle_end_ts, npc_name_id, poke_team_name_id, other_info, create_ts, update_ts
		FROM %s
		WHERE tid = $1 AND npc_name_id = $2 AND battle_start_ts >= $3
		ORDER BY battle_start_ts DESC
		LIMIT $4
	`, TableBattleRecords)

	rows, err := tx.QueryContext(ctx, query, tid, npcNameId, startTs, limit)
	if err != nil {
		logger.Error("Failed to query battle records by npc name: %v", err)
		return nil, err
	}
	defer rows.Close()

	var records []*MainServer.BattleRecordInfo
	for rows.Next() {
		record := &MainServer.BattleRecordInfo{}
		var battleType int
		var otherInfoJSON string
		err := rows.Scan(
			&record.Id,
			&record.BattleId,
			&record.Tid,
			&battleType,
			&record.PokeNames,
			&record.OpponentPokeNames,
			&record.IsWinner,
			&record.OpponentTid,
			&record.BattleResult,
			&record.BattleStartTs,
			&record.BattleEndTs,
			&record.NpcNameId,
			&record.PokeTeamNameId,
			&otherInfoJSON,
			&record.CreateTs,
			&record.UpdateTs,
		)
		if err != nil {
			logger.Error("Failed to scan battle record: %v", err)
			return nil, err
		}
		record.BattleType = MainServer.BattleType(battleType)

		// 解析 other_info JSON 到 proto 结构体
		record.OtherInfo = &MainServer.BattleRecordOtherInfo{}
		if otherInfoJSON != "" && otherInfoJSON != "{}" {
			if err := protojson.Unmarshal([]byte(otherInfoJSON), record.OtherInfo); err != nil {
				logger.Error("Failed to unmarshal other_info JSON: %v", err)
				// 使用默认值
				record.OtherInfo = &MainServer.BattleRecordOtherInfo{}
			}
		}

		records = append(records, record)
	}

	logger.Info("Found %d battle records for tid %d with npc name %s", len(records), tid, npcNameId)
	return records, nil
}

// GetBattleRecordsByPokeTeamName 根据宝可梦队名称获取战斗记录
func GetBattleRecordsByPokeTeamName(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, pokeTeamNameId string, timeRangeMs int64, limit int32) ([]*MainServer.BattleRecordInfo, error) {
	nowTs := time.Now().UnixMilli()
	startTs := nowTs - timeRangeMs

	query := fmt.Sprintf(`
		SELECT id, battle_id, tid, battle_type, poke_names, opponent_poke_names, is_winner, opponent_tid, battle_result, battle_start_ts, battle_end_ts, npc_name_id, poke_team_name_id, other_info, create_ts, update_ts
		FROM %s
		WHERE tid = $1 AND poke_team_name_id = $2 AND battle_start_ts >= $3
		ORDER BY battle_start_ts DESC
		LIMIT $4
	`, TableBattleRecords)

	rows, err := tx.QueryContext(ctx, query, tid, pokeTeamNameId, startTs, limit)
	if err != nil {
		logger.Error("Failed to query battle records by poke team name: %v", err)
		return nil, err
	}
	defer rows.Close()

	var records []*MainServer.BattleRecordInfo
	for rows.Next() {
		record := &MainServer.BattleRecordInfo{}
		var battleType int
		var otherInfoJSON string
		err := rows.Scan(
			&record.Id,
			&record.BattleId,
			&record.Tid,
			&battleType,
			&record.PokeNames,
			&record.OpponentPokeNames,
			&record.IsWinner,
			&record.OpponentTid,
			&record.BattleResult,
			&record.BattleStartTs,
			&record.BattleEndTs,
			&record.NpcNameId,
			&record.PokeTeamNameId,
			&otherInfoJSON,
			&record.CreateTs,
			&record.UpdateTs,
		)
		if err != nil {
			logger.Error("Failed to scan battle record: %v", err)
			return nil, err
		}
		record.BattleType = MainServer.BattleType(battleType)

		// 解析 other_info JSON 到 proto 结构体
		record.OtherInfo = &MainServer.BattleRecordOtherInfo{}
		if otherInfoJSON != "" && otherInfoJSON != "{}" {
			if err := protojson.Unmarshal([]byte(otherInfoJSON), record.OtherInfo); err != nil {
				logger.Error("Failed to unmarshal other_info JSON: %v", err)
				// 使用默认值
				record.OtherInfo = &MainServer.BattleRecordOtherInfo{}
			}
		}

		records = append(records, record)
	}

	logger.Info("Found %d battle records for tid %d with poke team name %s", len(records), tid, pokeTeamNameId)
	return records, nil
}

// GetBattleStatisticsByNpcName 根据NPC名称获取战斗统计
func GetBattleStatisticsByNpcName(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, npcNameId string, timeRangeMs int64) (*MainServer.BattleStatistics, error) {
	nowTs := time.Now().UnixMilli()
	startTs := nowTs - timeRangeMs

	query := fmt.Sprintf(`
		SELECT
			COUNT(*) as battle_count,
			SUM(CASE WHEN is_winner = true THEN 1 ELSE 0 END) as win_count,
			SUM(CASE WHEN is_winner = false THEN 1 ELSE 0 END) as lose_count
		FROM %s
		WHERE tid = $1 AND npc_name_id = $2 AND battle_start_ts >= $3
	`, TableBattleRecords)

	var battleCount, winCount, loseCount int32
	err := tx.QueryRowContext(ctx, query, tid, npcNameId, startTs).Scan(&battleCount, &winCount, &loseCount)
	if err != nil {
		logger.Error("Failed to query battle statistics by npc name: %v", err)
		return nil, err
	}

	// 计算胜率
	var winRate float64 = 0
	if battleCount > 0 {
		winRate = float64(winCount) / float64(battleCount) * 100
	}

	statistics := &MainServer.BattleStatistics{
		Tid:              tid,
		OpponentPokeName: npcNameId, // 这里复用字段存储NPC名称
		BattleCount:      battleCount,
		WinCount:         winCount,
		LoseCount:        loseCount,
		WinRate:          winRate,
		TimeRangeMs:      timeRangeMs,
	}

	logger.Info("Battle statistics for tid %d vs npc %s in %d ms: %d battles, %d wins, %.2f%% win rate",
		tid, npcNameId, timeRangeMs, battleCount, winCount, winRate)

	return statistics, nil
}

// GetBattleStatisticsByPokeTeamName 根据宝可梦队名称获取战斗统计
func GetBattleStatisticsByPokeTeamName(ctx context.Context, logger runtime.Logger, tx *sql.Tx, tid int64, pokeTeamNameId string, timeRangeMs int64) (*MainServer.BattleStatistics, error) {
	nowTs := time.Now().UnixMilli()
	startTs := nowTs - timeRangeMs

	query := fmt.Sprintf(`
		SELECT
			COUNT(*) as battle_count,
			SUM(CASE WHEN is_winner = true THEN 1 ELSE 0 END) as win_count,
			SUM(CASE WHEN is_winner = false THEN 1 ELSE 0 END) as lose_count
		FROM %s
		WHERE tid = $1 AND poke_team_name_id = $2 AND battle_start_ts >= $3
	`, TableBattleRecords)

	var battleCount, winCount, loseCount int32
	err := tx.QueryRowContext(ctx, query, tid, pokeTeamNameId, startTs).Scan(&battleCount, &winCount, &loseCount)
	if err != nil {
		logger.Error("Failed to query battle statistics by poke team name: %v", err)
		return nil, err
	}

	// 计算胜率
	var winRate float64 = 0
	if battleCount > 0 {
		winRate = float64(winCount) / float64(battleCount) * 100
	}

	statistics := &MainServer.BattleStatistics{
		Tid:              tid,
		OpponentPokeName: pokeTeamNameId, // 这里复用字段存储队伍名称
		BattleCount:      battleCount,
		WinCount:         winCount,
		LoseCount:        loseCount,
		WinRate:          winRate,
		TimeRangeMs:      timeRangeMs,
	}

	logger.Info("Battle statistics for tid %d vs poke team %s in %d ms: %d battles, %d wins, %.2f%% win rate",
		tid, pokeTeamNameId, timeRangeMs, battleCount, winCount, winRate)

	return statistics, nil
}

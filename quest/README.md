# 任务系统 (Quest System)

## 概述

这是一个完整的任务系统实现，支持多种任务类型、条件检查、奖励发放和广播功能。

## 文件结构

```
quest/
├── Quest.go           # 核心任务管理
├── TrainerQuest.go    # 训练师任务管理
├── QuestCondition.go  # 任务条件检查
├── QuestReward.go     # 任务奖励系统
├── QuestBroadcast.go  # 任务广播系统
├── QuestRpc.go        # RPC接口
├── QuestInit.go       # 系统初始化
└── README.md          # 说明文档
```

## 主要功能

### 1. 任务管理

- 创建、更新、删除任务
- 支持多种任务类型（一次性、每日、每周等）
- 任务状态管理（等待、开放、关闭）
- 时间控制（开始时间、结束时间）

### 2. 条件系统

- 解锁条件检查（等级、前置任务、道具等）
- 完成条件验证（击败宝可梦、收集道具等）
- 自定义条件支持

### 3. 奖励系统

- 多种奖励类型（宝可梦、道具、服装）
- 概率奖励机制
- 数量分配算法（固定、平均、阶梯、中间值优先）

### 4. 广播系统

- 任务开始/结束通知
- 系统级、世界级、区域级广播
- 自动调度器

## 数据库表结构

### quest 表

```sql
CREATE TABLE quest (
    quest_id INTEGER PRIMARY KEY,
    quest_type INTEGER NOT NULL,
    quest_level INTEGER DEFAULT 0,
    quest_status INTEGER DEFAULT 0,
    quest_unlock_id INTEGER DEFAULT 0,
    linear_quest_ids JSONB,
    single_quest BOOLEAN DEFAULT false,
    current_quest_ids JSONB,
   quest_reward_id INTEGER DEFAULT 0,
    quest_start_time BIGINT DEFAULT 0,
    quest_end_time BIGINT DEFAULT 0,
    quest_repeat_limit INTEGER DEFAULT 0,
    quest_repeat_interval INTEGER DEFAULT 0,
    quest_broadcast JSONB,
    quest_complete_id INTEGER DEFAULT 0,
    quest_repeat_limit_time INTEGER DEFAULT 0,
    create_ts BIGINT NOT NULL,
    update_ts BIGINT NOT NULL
);
```

### trainer_quest 表

```sql
CREATE TABLE trainer_quest (
    id BIGSERIAL PRIMARY KEY,
    tid BIGINT NOT NULL,
    quest_id INTEGER NOT NULL,
    quest_status INTEGER DEFAULT 1,
    quest_progress JSONB DEFAULT '{}',
    accept_time BIGINT NOT NULL,
    complete_time BIGINT DEFAULT 0,
    reward_claimed BOOLEAN DEFAULT false,
    repeat_count INTEGER DEFAULT 0,
    create_ts BIGINT NOT NULL,
    update_ts BIGINT NOT NULL
);
```

## RPC接口

### 1. 接受任务

```
RPC: accept_quest
请求: RpcAcceptQuestRequest
响应: RpcAcceptQuestResponse
```

### 2. 完成任务

```
RPC: complete_quest
请求: RpcCompleteQuestRequest
响应: RpcCompleteQuestResponse
```

### 3. 领取奖励

```
RPC: claim_quest_reward
请求: RpcClaimQuestRewardRequest
响应: RpcClaimQuestRewardResponse
```

### 4. 获取可用任务

```
RPC: get_available_quests
请求: RpcGetAvailableQuestsRequest
响应: RpcGetAvailableQuestsResponse
```

### 5. 获取训练师任务

```
RPC: get_trainer_quests
请求: 无
响应: RpcGetTrainerQuestsResponse
```

### 6. 更新任务进度

```
RPC: update_quest_progress
请求: RpcUpdateQuestProgressRequest
响应: RpcUpdateQuestProgressResponse
```

## 使用示例

### 初始化任务系统

```go
// 在main.go中初始化
err := quest.InitQuestSystem(ctx, logger, db, nk)
if err != nil {
    logger.Error("Failed to init quest system: %v", err)
}

// 注册RPC函数
err = quest.RegisterQuestRPCs(ctx, logger, db, nk, initializer)
if err != nil {
    logger.Error("Failed to register quest RPCs: %v", err)
}
```

### 添加任务

```go
questInfo := &MainServer.QuestInfo{
    QuestId: 1001,
    QuestType: MainServer.QuestType_daily,
    QuestLevel: 1,
    QuestStatus: MainServer.QuestStatus_open,
    QuestStartTime: time.Now().Unix(),
    QuestEndTime: time.Now().Add(24 * time.Hour).Unix(),
}

err := quest.AddQuest(ctx, logger, tx, questInfo)
```

### 更新任务进度

```go
progressInfo := &MainServer.QuestInfo{
    QuestId: questId,
    // 根据需要设置其他字段
}
err := quest.UpdateQuestProgress(ctx, logger, tx, trainerId, questId, progressInfo)
```

## 配置说明

任务系统支持以下配置项：

- `EnableBroadcast`: 是否启用广播功能
- `BroadcastInterval`: 广播检查间隔（秒）
- `MaxQuestsPerTrainer`: 每个训练师最大任务数
- `QuestTimeoutSeconds`: 任务超时时间
- `EnableQuestLog`: 是否启用任务日志
- `LogRetentionDays`: 日志保留天数

## 注意事项

1. 所有数据库操作都使用事务（`*sql.Tx`）
2. 任务条件和奖励配置需要从JSON文件加载到内存
3. 广播功能需要配合Nakama的通知系统
4. 奖励发放需要集成具体的道具、宝可梦、服装系统
5. 函数设计简单、可复用，包含中文注释

## 扩展说明

系统设计为模块化，可以轻松扩展：

- 添加新的任务类型
- 实现新的条件检查逻辑
- 增加新的奖励类型
- 自定义广播规则

## 依赖项

- Nakama游戏服务器框架
- PostgreSQL数据库
- Protocol Buffers (MainServer包)
- go-nakama-poke/tool工具包

package user

import (
	"context"
	"database/sql"
	"encoding/json"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	UserOpIllegal = "[非法]"
)
const (
	UserFieldUsername    = "username"
	UserFieldDisplayName = "displayName"
	UserFieldTimezone    = "timezone"
	UserFieldLocation    = "location"
	UserFieldLangTag     = "langTag"
	UserFieldAvatarUrl   = "avatarUrl"
	UserFieldMetadata    = "metadata"
)

// 允许更新的字段常量
const (
	MetadataFeildLoc   = "loc"
	MetadataFeildItems = "items"
	MetadataFeildPokes = "pokes"
)

// 允许更新的字段列表，用于字段合法性检查
var allowedFields = map[string]bool{
	MetadataFeildLoc:   true,
	MetadataFeildItems: true,
	MetadataFeildPokes: false,
}

// RPC 方法：更新用户状态的 Metadata
func RpcUpdateUserStatusMetadata(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析输入的 payload
	var statusUpdates map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &statusUpdates); err != nil {
		return "", runtime.NewError("无效的输入格式", 400)
	}
	// 从 ctx 中获取 userID
	userID := ctx.Value("user_id").(string)
	if userID == "" {
		return "", runtime.NewError("无法获取用户ID", 400)
	}

	if len(statusUpdates) > len(allowedFields) {
		logger.Warn(UserOpIllegal+userID+"更新的字段数量超过限制: %s", payload)
		return "", runtime.NewError("更新的字段数量超过限制", 400)
	}

	// 创建一个 map 来存储合法的更新字段
	validUpdates := make(map[string]interface{})

	// 检查输入中的字段是否在允许更新的字段列表中
	for key, value := range statusUpdates {
		if allowedFields[key] {
			validUpdates[key] = value
		} else {
			logger.Warn(UserOpIllegal+userID+"字段尝试更新: %s", key)
		}
	}

	// 如果没有合法的字段可更新，返回错误
	if len(validUpdates) == 0 {
		return "", nil
		// return "", runtime.NewError("没有可更新的合法字段", 400)
	}

	// 调用更新方法来更新用户的状态 Metadata
	err := updateUserStatusMetadata(ctx, logger, nk, userID, validUpdates)
	if err != nil {
		return "", err
	}

	// // 构造成功响应
	// response := map[string]interface{}{
	// 	"success": true,
	// 	"updated": validUpdates,
	// }

	// // 序列化响应为 JSON 字符串
	// responseData, err := json.Marshal(response)
	// if err != nil {
	// 	return "", runtime.NewError("响应序列化失败", 500)
	// }

	return "{}", nil
}

// 更新用户状态到 metadata
func updateUserStatusMetadata(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, userID string, updatedStatus map[string]interface{}) error {
	// 获取用户当前账户信息
	account, err := nk.AccountGetId(ctx, userID)
	if err != nil {
		return runtime.NewError("无法获取用户账户信息", 500)
	}

	// 解析现有的 metadata
	metadata := make(map[string]interface{})
	if err := json.Unmarshal([]byte(account.User.Metadata), &metadata); err != nil {
		return runtime.NewError("解析 metadata 失败", 500)
	}

	// 更新合法的字段到 metadata 中
	for key, value := range updatedStatus {
		metadata[key] = value
	}

	// 准备更新的字段
	updatedFields := map[string]interface{}{
		UserFieldMetadata: metadata,
	}

	// 调用更新账户的函数
	err = updateAccount(ctx, nk, userID, updatedFields)
	if err != nil {
		logger.Error("Failed to update account: %v", err)
		return err
	}

	logger.Info("用户状态已更新: %v", updatedStatus)
	return nil
}

// 从 metadata 中删除指定字段
func deleteUserStatusFieldMetadata(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, userID, field string) (bool, error) {
	// 获取用户账户信息
	account, err := nk.AccountGetId(ctx, userID)
	if err != nil {
		return false, runtime.NewError("获取用户账户信息失败", 500)
	}

	// 解析现有的 metadata
	metadata := make(map[string]interface{})
	if err := json.Unmarshal([]byte(account.User.Metadata), &metadata); err != nil {
		return false, runtime.NewError("解析 metadata 失败", 500)
	}

	// 删除指定字段
	if _, exists := metadata[field]; exists {
		delete(metadata, field)
	} else {
		return false, runtime.NewError("字段不存在", 400)
	}

	updatedFields := map[string]interface{}{
		UserFieldMetadata: metadata,
	}

	// 更新账户
	err = updateAccount(ctx, nk, userID, updatedFields)
	if err != nil {
		logger.Error("Failed to update account: %v", err)
		return false, err
	}

	logger.Info("已删除用户的状态字段: %s", field)
	return true, nil
}

// 更新用户信息，只更新传入的字段
func updateAccount(ctx context.Context, nk runtime.NakamaModule, userID string, updatedFields map[string]interface{}) error {
	// 获取当前用户的账号信息
	account, err := nk.AccountGetId(ctx, userID)
	if err != nil {
		return runtime.NewError("获取用户信息失败", 500)
	}

	// 初始化字段
	username := account.User.Username
	displayName := account.User.DisplayName
	timezone := account.User.Timezone
	location := account.User.Location
	langTag := account.User.LangTag
	avatarUrl := account.User.AvatarUrl
	metadata := make(map[string]interface{})
	if err := json.Unmarshal([]byte(account.User.Metadata), &metadata); err != nil {
		return runtime.NewError("解析 metadata 失败", 500)
	}

	// 更新指定字段
	if value, ok := updatedFields[UserFieldUsername].(string); ok && value != "" {
		username = value
	}
	if value, ok := updatedFields[UserFieldDisplayName].(string); ok && value != "" {
		displayName = value
	}
	if value, ok := updatedFields[UserFieldTimezone].(string); ok && value != "" {
		timezone = value
	}
	if value, ok := updatedFields[UserFieldLocation].(string); ok && value != "" {
		location = value
	}
	if value, ok := updatedFields[UserFieldLangTag].(string); ok && value != "" {
		langTag = value
	}
	if value, ok := updatedFields[UserFieldAvatarUrl].(string); ok && value != "" {
		avatarUrl = value
	}
	if value, ok := updatedFields[UserFieldMetadata].(map[string]interface{}); ok && value != nil {
		metadata = value
	}

	// 更新用户信息
	err = nk.AccountUpdateId(ctx, userID, username, metadata, displayName, timezone, location, langTag, avatarUrl)
	if err != nil {
		return runtime.NewError("更新用户信息失败", 500)
	}

	return nil
}

// 获取用户公开状态
// func getUserStatusMetadata(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (*MainServer.PublicUserStatus, error) {
// 	targetUserID := payload
// 	// 获取目标用户账户信息
// 	users, err := nk.UsersGetId(ctx, []string{targetUserID}, nil)
// 	if err != nil || len(users) == 0 {
// 		return nil, runtime.NewError("无法获取目标用户账户", 404)
// 	}

// 	// 解析 metadata
// 	metadata := make(map[string]interface{})
// 	if err := json.Unmarshal([]byte(users[0].Metadata), &metadata); err != nil {
// 		return nil, runtime.NewError("解析 metadata 失败", 500)
// 	}

// 	// 提取并返回公共状态
// 	publicStatus := &MainServer.PublicUserStatus{}

// 	if loc, ok := metadata[MetadataFeildLoc].(string); ok {
// 		publicStatus.Loc = loc
// 	}

// 	return publicStatus, nil
// }

-- 为 name 创建索引，便于通过 names 进行匹配
CREATE INDEX idx_storage_value_poke_name ON storage ((value->'poke'->>'name'));

-- 为 price 创建索引，便于根据价格进行筛选
CREATE INDEX idx_storage_value_price ON storage (((value->>'price')::numeric));

-- 为 evs 的各个属性创建索引，便于筛选和排序
CREATE INDEX idx_storage_value_evs_hp ON storage (((value->'poke'->'evs'->>'hp')::int));
CREATE INDEX idx_storage_value_evs_atk ON storage (((value->'poke'->'evs'->>'atk')::int));
CREATE INDEX idx_storage_value_evs_def ON storage (((value->'poke'->'evs'->>'def')::int));
CREATE INDEX idx_storage_value_evs_spa ON storage (((value->'poke'->'evs'->>'spa')::int));
CREATE INDEX idx_storage_value_evs_spd ON storage (((value->'poke'->'evs'->>'spd')::int));
CREATE INDEX idx_storage_value_evs_spe ON storage (((value->'poke'->'evs'->>'spe')::int));

-- 为 ivs 的各个属性创建索引，便于筛选和排序
CREATE INDEX idx_storage_value_ivs_hp ON storage (((value->'poke'->'ivs'->>'hp')::int));
CREATE INDEX idx_storage_value_ivs_atk ON storage (((value->'poke'->'ivs'->>'atk')::int));
CREATE INDEX idx_storage_value_ivs_def ON storage (((value->'poke'->'ivs'->>'def')::int));
CREATE INDEX idx_storage_value_ivs_spa ON storage (((value->'poke'->'ivs'->>'spa')::int));
CREATE INDEX idx_storage_value_ivs_spd ON storage (((value->'poke'->'ivs'->>'spd')::int));
CREATE INDEX idx_storage_value_ivs_spe ON storage (((value->'poke'->'ivs'->>'spe')::int));
-- 为 ability 创建索引，便于后续通过计算进行筛选
CREATE INDEX idx_storage_value_ability ON storage (((value->'poke'->>'ability')::int));
package market

import (
	"go-nakama-poke/proto/MainServer"
)

// 获取提交方状态
func getSubmitterState(submitterId, initiatorId int64, swop *MainServer.SwopInfo) MainServer.SwopState {
	if submitterId == initiatorId {
		return swop.InitiatorState
	}
	return swop.TargetState
}

// 发送交换请求通知
// func SendSwopInfoNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, swop *MainServer.SwopInfo) error {
// 	trainer := tool.GetActiveTrainerByCtx(ctx)
// 	if trainer == nil {
// 		return runtime.NewError("未找到训练师信息", 400)
// 	}
// 	// 获取发起者和目标训练师
// 	// initiator := tool.GetActiveTrainerByTid(swop.InitiatorId)
// 	// target := tool.GetActiveTrainerByTid(swop.TargetId)
// 	// if initiator == nil || target == nil {
// 	// 	logger.Error("发送交换请求通知失败: 无法获取训练师信息")
// 	// 	return runtime.NewError("无法获取训练师信息", 404)
// 	// }

// 	// // 创建通知内容，使用脱敏的训练师信息
// 	// notification := &MainServer.SwopRequestNotification{
// 	// 	SwopId:    swop.SwopId,
// 	// 	Initiator: tool.CopySafeTrainer(initiator),
// 	// 	Target:    tool.CopySafeTrainer(target),
// 	// 	State:     swop.InitiatorState, // 使用发起方状态
// 	// 	CreateTs:  swop.CreateTs,
// 	// 	UpdateTs:  swop.UpdateTs,
// 	// }

// 	initiatorTrainer := tool.GetActiveTrainerByTid(swop.InitiatorId)
// 	if initiatorTrainer == nil {
// 		return runtime.NewError("未找到发起方训练师信息", 400)
// 	}
// 	targetTrainer := tool.GetActiveTrainerByTid(swop.TargetId)
// 	if targetTrainer == nil {
// 		return runtime.NewError("未找到目标训练师信息", 400)
// 	}
// 	// 序列化通知
// 	notificationBytes, err := proto.Marshal(swop)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知内容
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}
// 	sender := initiatorTrainer
// 	receiver := targetTrainer
// 	if trainer.Id == swop.TargetId {
// 		sender = targetTrainer
// 		receiver = initiatorTrainer
// 	}

// 	if err := nk.NotificationSend(ctx, receiver.Uid, "swop_info", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopInfo), sender.Uid, true); err != nil {
// 		logger.Error("发送SendSwopInfoNotification通知失败: %v", err)
// 		return err
// 	}
// 	// 发送给发起者
// 	// if err := nk.NotificationSend(ctx, initiator.Uid, "swop_request", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopRequest), "", true); err != nil {
// 	// 	logger.Error("发送通知给发起者失败: %v", err)
// 	// 	return err
// 	// }

// 	// // 发送给接收者
// 	// if err := nk.NotificationSend(ctx, target.Uid, "swop_request", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopRequest), "", true); err != nil {
// 	// 	logger.Error("发送通知给接收者失败: %v", err)
// 	// 	return err
// 	// }

// 	return nil
// }

// 发送交换请求通知
// func SendSwopRequestNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, swop *MainServer.SwopInfo) error {
// 	// 获取发起者和目标训练师
// 	initiator := tool.GetActiveTrainerByTid(swop.InitiatorId)
// 	target := tool.GetActiveTrainerByTid(swop.TargetId)
// 	if initiator == nil || target == nil {
// 		logger.Error("发送交换请求通知失败: 无法获取训练师信息")
// 		return runtime.NewError("无法获取训练师信息", 404)
// 	}

// 	// 创建通知内容，使用脱敏的训练师信息
// 	notification := &MainServer.SwopRequestNotification{
// 		SwopId:    swop.SwopId,
// 		Initiator: tool.CopySafeTrainer(initiator),
// 		Target:    tool.CopySafeTrainer(target),
// 		State:     swop.InitiatorState, // 使用发起方状态
// 		CreateTs:  swop.CreateTs,
// 		UpdateTs:  swop.UpdateTs,
// 	}

// 	// 序列化通知
// 	notificationBytes, err := proto.Marshal(notification)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知内容
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}

// 	// 发送给发起者
// 	if err := nk.NotificationSend(ctx, initiator.Uid, "swop_request", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopRequest), "", true); err != nil {
// 		logger.Error("发送通知给发起者失败: %v", err)
// 		return err
// 	}

// 	// 发送给接收者
// 	if err := nk.NotificationSend(ctx, target.Uid, "swop_request", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopRequest), "", true); err != nil {
// 		logger.Error("发送通知给接收者失败: %v", err)
// 		return err
// 	}

// 	return nil
// }

// // 发送交换接受通知
// func SendSwopAcceptNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, swop *MainServer.SwopInfo) error {
// 	// 获取发起者和目标训练师
// 	initiator := tool.GetActiveTrainerByTid(swop.InitiatorId)
// 	target := tool.GetActiveTrainerByTid(swop.TargetId)
// 	if initiator == nil || target == nil {
// 		logger.Error("发送交换接受通知失败: 无法获取训练师信息")
// 		return runtime.NewError("无法获取训练师信息", 404)
// 	}

// 	// 创建通知内容，使用脱敏的训练师信息
// 	notification := &MainServer.SwopAcceptNotification{
// 		SwopId:    swop.SwopId,
// 		Initiator: tool.CopySafeTrainer(initiator),
// 		Target:    tool.CopySafeTrainer(target),
// 		State:     swop.TargetState, // 使用目标方状态
// 		UpdateTs:  swop.UpdateTs,
// 	}

// 	// 序列化通知
// 	notificationBytes, err := proto.Marshal(notification)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知内容
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}

// 	// 发送给发起者
// 	if err := nk.NotificationSend(ctx, initiator.Uid, "swop_accept", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopAccept), "", true); err != nil {
// 		logger.Error("发送通知给发起者失败: %v", err)
// 		return err
// 	}

// 	// 发送给接收者
// 	if err := nk.NotificationSend(ctx, target.Uid, "swop_accept", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopAccept), "", true); err != nil {
// 		logger.Error("发送通知给接收者失败: %v", err)
// 		return err
// 	}

// 	return nil
// }

// // 发送交换内容提交通知
// func SendSwopContentNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, swop *MainServer.SwopInfo, submitterId int64) error {
// 	// 获取发起者和目标训练师
// 	initiator := tool.GetActiveTrainerByTid(swop.InitiatorId)
// 	target := tool.GetActiveTrainerByTid(swop.TargetId)
// 	if initiator == nil || target == nil {
// 		logger.Error("发送交换内容提交通知失败: 无法获取训练师信息")
// 		return runtime.NewError("无法获取训练师信息", 404)
// 	}

// 	// 创建通知内容，使用脱敏的训练师信息
// 	notification := &MainServer.SwopContentNotification{
// 		SwopId:      swop.SwopId,
// 		Initiator:   tool.CopySafeTrainer(initiator),
// 		Target:      tool.CopySafeTrainer(target),
// 		SubmitterId: submitterId,
// 		State:       getSubmitterState(submitterId, initiator.Id, swop), // 使用提交方状态
// 		UpdateTs:    swop.UpdateTs,
// 	}

// 	// 序列化通知
// 	notificationBytes, err := proto.Marshal(notification)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知内容
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}

// 	// 发送给发起者
// 	if err := nk.NotificationSend(ctx, initiator.Uid, "swop_content", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopContent), "", true); err != nil {
// 		logger.Error("发送通知给发起者失败: %v", err)
// 		return err
// 	}

// 	// 发送给接收者
// 	if err := nk.NotificationSend(ctx, target.Uid, "swop_content", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopContent), "", true); err != nil {
// 		logger.Error("发送通知给接收者失败: %v", err)
// 		return err
// 	}

// 	return nil
// }

// // 发送交换完成通知
// func SendSwopCompleteNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, swop *MainServer.SwopInfo) error {
// 	// 获取发起者和目标训练师
// 	initiator := tool.GetActiveTrainerByTid(swop.InitiatorId)
// 	target := tool.GetActiveTrainerByTid(swop.TargetId)
// 	if initiator == nil || target == nil {
// 		logger.Error("发送交换完成通知失败: 无法获取训练师信息")
// 		return runtime.NewError("无法获取训练师信息", 404)
// 	}

// 	// 创建通知内容，使用脱敏的训练师信息
// 	notification := map[string]interface{}{
// 		"swop_id":           swop.SwopId,
// 		"initiator":         tool.CopySafeTrainer(initiator),
// 		"target":            tool.CopySafeTrainer(target),
// 		"initiator_content": swop.InitiatorContent,
// 		"target_content":    swop.TargetContent,
// 		"initiator_state":   swop.InitiatorState,
// 		"target_state":      swop.TargetState,
// 		"update_ts":         swop.UpdateTs,
// 	}

// 	// 序列化通知
// 	notificationBytes, err := json.Marshal(notification)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知内容
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}

// 	// 发送给发起者
// 	if err := nk.NotificationSend(ctx, initiator.Uid, "swop_complete", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopComplete), "", true); err != nil {
// 		logger.Error("发送通知给发起者失败: %v", err)
// 		return err
// 	}

// 	// 发送给接收者
// 	if err := nk.NotificationSend(ctx, target.Uid, "swop_complete", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopComplete), "", true); err != nil {
// 		logger.Error("发送通知给接收者失败: %v", err)
// 		return err
// 	}

// 	return nil
// }

// func SendSwopChangeStateNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, stateInfo *MainServer.SwopChangeContentInfo) error {
// 	// 获取发起者和目标训练师
// 	initiator := tool.GetActiveTrainerByTid(stateInfo.InitiatorId)
// 	target := tool.GetActiveTrainerByTid(stateInfo.TargetId)
// 	if initiator == nil || target == nil {
// 		logger.Error("发送交换状态变更通知失败: 无法获取训练师信息")
// 		return runtime.NewError("无法获取训练师信息", 404)
// 	}

// 	// 确定接收方的 UID
// 	var receiverUid string
// 	if stateInfo.ActorId == initiator.Id {
// 		receiverUid = target.Uid
// 	} else {
// 		receiverUid = initiator.Uid
// 	}

// 	// 创建通知内容并序列化
// 	// updateTs := time.Now().UnixMilli()
// 	// notification := &MainServer.SwopCompleteNotification{
// 	// 	SwopId:      stateInfo.SwopId,
// 	// 	InitiatorId: stateInfo.InitiatorId,
// 	// 	TargetId:    stateInfo.TargetId,
// 	// 	State:       stateInfo.State,
// 	// 	ActorId:     stateInfo.ActorId,
// 	// 	Initiator:   tool.CopySafeTrainer(initiator),
// 	// 	Target:      tool.CopySafeTrainer(target),
// 	// 	UpdateTs:    updateTs,
// 	// }

// 	// // 序列化通知
// 	// notification := map[string]interface{}{
// 	// 	"swop_id":      stateInfo.SwopId,
// 	// 	"initiator_id": stateInfo.InitiatorId,
// 	// 	"target_id":    stateInfo.TargetId,
// 	// 	"state":        stateInfo.State,
// 	// 	"actor_id":     stateInfo.ActorId,
// 	// 	"initiator":    tool.CopySafeTrainer(initiator),
// 	// 	"target":       tool.CopySafeTrainer(target),
// 	// 	"update_ts":    updateTs,
// 	// }

// 	// 序列化通知
// 	notificationBytes, err := json.Marshal(stateInfo)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知内容
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}

// 	// 只发送给接收方
// 	if err := nk.NotificationSend(ctx, receiverUid, "swop_change_state", content, 10003, "", true); err != nil {
// 		logger.Error("发送交换状态变更通知失败: %v", err)
// 		return err
// 	}

// 	return nil
// }

// 发送交换取消确认通知
// func SendSwopCancelConfirmNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, swop *MainServer.SwopInfo, cancelerId int64) error {
// 	// 获取发起者和目标训练师
// 	initiator := tool.GetActiveTrainerByTid(swop.InitiatorId)
// 	target := tool.GetActiveTrainerByTid(swop.TargetId)
// 	if initiator == nil || target == nil {
// 		logger.Error("发送交换取消确认通知失败: 无法获取训练师信息")
// 		return runtime.NewError("无法获取训练师信息", 404)
// 	}

// 	// 确定接收方的 UID
// 	var receiverUid string
// 	if cancelerId == initiator.Id {
// 		receiverUid = target.Uid
// 	} else {
// 		receiverUid = initiator.Uid
// 	}

// 	// 创建通知内容
// 	notification := map[string]interface{}{
// 		"swop_id":         swop.SwopId,
// 		"initiator":       tool.CopySafeTrainer(initiator),
// 		"target":          tool.CopySafeTrainer(target),
// 		"canceler_id":     cancelerId,
// 		"initiator_state": swop.InitiatorState,
// 		"target_state":    swop.TargetState,
// 		"update_ts":       swop.UpdateTs,
// 		"reason":          "用户取消确认",
// 	}

// 	// 序列化通知
// 	notificationBytes, err := json.Marshal(notification)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知内容
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}

// 	// 只发送给接收方
// 	if err := nk.NotificationSend(ctx, receiverUid, "swop_cancel_confirm", content, 10001, "", true); err != nil {
// 		logger.Error("发送交换取消确认通知失败: %v", err)
// 		return err
// 	}

// 	return nil
// }

// // 发送交换关闭通知
// func SendSwopCloseNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, swop *MainServer.SwopInfo, closerId int64, reason string) error {
// 	// 获取发起者和目标训练师
// 	initiator := tool.GetActiveTrainerByTid(swop.InitiatorId)
// 	target := tool.GetActiveTrainerByTid(swop.TargetId)
// 	if initiator == nil || target == nil {
// 		logger.Error("发送交换关闭通知失败: 无法获取训练师信息")
// 		return runtime.NewError("无法获取训练师信息", 404)
// 	}

// 	// 确定接收方的 UID
// 	var receiverUid string
// 	if closerId == initiator.Id {
// 		receiverUid = target.Uid
// 	} else {
// 		receiverUid = initiator.Uid
// 	}

// 	// 创建通知内容
// 	notification := map[string]interface{}{
// 		"swop_id":         swop.SwopId,
// 		"initiator":       tool.CopySafeTrainer(initiator),
// 		"target":          tool.CopySafeTrainer(target),
// 		"closer_id":       closerId,
// 		"initiator_state": swop.InitiatorState,
// 		"target_state":    swop.TargetState,
// 		"update_ts":       swop.UpdateTs,
// 		"reason":          reason,
// 	}

// 	// 序列化通知
// 	notificationBytes, err := json.Marshal(notification)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知内容
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}

// 	// 只发送给接收方
// 	if err := nk.NotificationSend(ctx, receiverUid, "swop_close", content, 10002, "", true); err != nil {
// 		logger.Error("发送交换关闭通知失败: %v", err)
// 		return err
// 	}

// 	return nil
// }

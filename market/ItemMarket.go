package market

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/inventory"
	"go-nakama-poke/item"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"go-nakama-poke/trainer"

	"github.com/heroiclabs/nakama-common/runtime"
)

func RpcBuyItemsByKeyAndName(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	var buyInventoryItemInfo = &MainServer.BuyInventoryItemInfo{}
	if err := tool.Base64ToProto(payload, buyInventoryItemInfo); err != nil {
		logger.Error("解析 buyInventoryItemInfo 失败: %v", err) // 记录解析失败
		return "", runtime.NewError("无效的请求数据", 400)
	}
	activeTrainer := tool.GetActiveTrainerByCtx(ctx)
	if activeTrainer == nil {
		return "", runtime.NewError("Not found tid", 404)
	}
	localItem, exists := item.GetItemByKeyAndName(buyInventoryItemInfo.Category, buyInventoryItemInfo.Name)
	if !exists {
		logger.Error("Failed to query item info")
		return "", runtime.NewError("Failed to query item info", 404)
	}
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", err)
	}
	if activeTrainer.Coin < int64(localItem.Cost) {
		return "", runtime.NewError("coin err", 400)
	}
	err = trainer.Payment(ctx, logger, tx, activeTrainer, int64(localItem.Cost))
	if err != nil {
		return "", err
	}

	err = inventory.AddItem(ctx, tx, activeTrainer.Id, localItem.Name, buyInventoryItemInfo.Count)

	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to AddItemToInventory: %v", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}
	return "", nil
}

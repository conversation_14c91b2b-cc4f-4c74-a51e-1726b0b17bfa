package market

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"go-nakama-poke/inventory"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"go-nakama-poke/trainer"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	TableSwopName = "swop_records"
)

func InitSwop(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createSwopTableIfNotExists(ctx, logger, db)
}

func createSwopTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	createTableSQL := fmt.Sprintf(`
	CREATE TABLE IF NOT EXISTS %s (
		id BIGSERIAL PRIMARY KEY,              -- 交易唯一ID(自增长)
		initiator_id BIGINT NOT NULL,          -- 发起方ID
		target_id BIGINT NOT NULL,             -- 目标方ID
		initiator_state INT NOT NULL,          -- 发起方交易状态
		target_state INT NOT NULL,             -- 目标方交易状态
		initiator_content JSONB NOT NULL DEFAULT '{}'::jsonb,  -- 发起方内容（包含物品和宝可梦）
		target_content JSONB NOT NULL DEFAULT '{}'::jsonb,     -- 目标方内容（包含物品和宝可梦）
		extra JSONB NOT NULL DEFAULT '{}'::jsonb,              -- 额外信息
		create_ts BIGINT NOT NULL,             -- 创建时间
		update_ts BIGINT NOT NULL              -- 更新时间
	);
	CREATE INDEX IF NOT EXISTS idx_%s_initiator_id ON %s (initiator_id);
	CREATE INDEX IF NOT EXISTS idx_%s_target_id ON %s (target_id);
	CREATE INDEX IF NOT EXISTS idx_%s_initiator_state ON %s (initiator_state);
	CREATE INDEX IF NOT EXISTS idx_%s_target_state ON %s (target_state);
	CREATE INDEX IF NOT EXISTS idx_%s_create_ts ON %s (create_ts);
	CREATE INDEX IF NOT EXISTS idx_%s_update_ts ON %s (update_ts);
	`, TableSwopName, TableSwopName, TableSwopName, TableSwopName, TableSwopName,
		TableSwopName, TableSwopName, TableSwopName, TableSwopName, TableSwopName, TableSwopName, TableSwopName, TableSwopName)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("Error creating table %s: %v", TableSwopName, err)
		return err
	}

	logger.Info("Table %s created or already exists", TableSwopName)
	return nil
}

// RPC函数：创建交换请求
func RpcCreateSwopRequest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数

	// 获取当前用户的训练师ID（发起方）
	initiatorTrainer := tool.GetActiveTrainerByCtx(ctx)
	if initiatorTrainer == nil {
		return "", runtime.NewError("未找到发起方训练师信息", 400)
	}

	// 获取目标训练师ID
	targetTid, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", runtime.NewError("缺少目标训练师ID", 400)
	}

	// 获取目标训练师信息
	targetTrainer := tool.GetActiveTrainerByTid(targetTid)
	if targetTrainer == nil {
		return "", runtime.NewError("未找到目标训练师信息", 400)
	}

	// 创建交换请求
	swop, err := CreateSwopRequest(ctx, logger, db, initiatorTrainer, targetTrainer)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("创建交换请求失败: %v", err), 500)
	}
	if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop); err != nil {
		logger.Error("发送交换请求通知失败: %v", err)
		// 继续执行，不中断流程
		return "", err
	}
	// 发送通知
	// if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop, initiatorTrainer, targetTrainer); err != nil {
	// 	logger.Error("发送交换请求通知失败: %v", err)
	// 	// 继续执行，不中断流程
	// }

	return tool.ProtoToBase64(swop)
}

// 创建交换请求  接收方点击同意之后才进行创建 TODO 创建完成应该发一条通知给双方
// 流程  A发起 ，B接受后，CreateSwopRequest， 然后系统发送通知， 双方客户端比较是否有发起过，发起过就弹出交易（交易方状态应该要是idle
func CreateSwopRequest(ctx context.Context, logger runtime.Logger, db *sql.DB,
	initiatorTrainer *MainServer.Trainer, targetTrainer *MainServer.Trainer) (*MainServer.SwopInfo, error) {
	if initiatorTrainer.Id == targetTrainer.Id {
		return nil, runtime.NewError("same trainer", 400)
	}
	if initiatorTrainer.ActionInfo.Action != MainServer.TrainerActionType_idle || targetTrainer.ActionInfo.Action != MainServer.TrainerActionType_idle {
		return nil, runtime.NewError("trainer not idle", 400)
	}

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	swop := &MainServer.SwopInfo{
		InitiatorId:    initiatorTrainer.Id,
		TargetId:       targetTrainer.Id,
		InitiatorState: MainServer.SwopState_REQUESTED,
		TargetState:    MainServer.SwopState_INIT,
		CreateTs:       time.Now().Unix(),
		UpdateTs:       time.Now().Unix(),
		InitiatorContent: &MainServer.SwopContent{
			PokeIds: []int64{},
			Items:   []*MainServer.SwopItem{},
		},
		TargetContent: &MainServer.SwopContent{
			PokeIds: []int64{},
			Items:   []*MainServer.SwopItem{},
		},
	}

	// 序列化交换内容
	initiatorContentBytes, err := json.Marshal(swop.InitiatorContent)
	if err != nil {
		return nil, fmt.Errorf("序列化发起方内容失败: %w", err)
	}

	targetContentBytes, err := json.Marshal(swop.TargetContent)
	if err != nil {
		return nil, fmt.Errorf("序列化目标方内容失败: %w", err)
	}

	// 获取自增ID
	err = tx.QueryRowContext(ctx,
		fmt.Sprintf("INSERT INTO %s (initiator_id, target_id, initiator_state, target_state, initiator_content, target_content, create_ts, update_ts) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id",
			TableSwopName),
		initiatorTrainer.Id, targetTrainer.Id,
		int32(swop.InitiatorState), int32(swop.TargetState),
		initiatorContentBytes, targetContentBytes,
		swop.CreateTs, swop.UpdateTs).Scan(&swop.SwopId)
	if err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}
	return swop, nil
}

// RPC函数：接受交换请求
func RpcAcceptSwopRequest(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户的训练师ID（目标方）
	targetTrainer := tool.GetActiveTrainerByCtx(ctx)
	if targetTrainer == nil {
		return "", runtime.NewError("未找到目标训练师信息", 400)
	}
	// 获取目标训练师ID
	swopId, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", runtime.NewError("缺少交换请求ID", 400)
	}

	// 接受交换请求
	swop, err := AcceptSwopRequest(ctx, logger, db, swopId, targetTrainer.Id)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("接受交换请求失败: %v", err), 500)
	}
	// 发送通知
	if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop); err != nil {
		logger.Error("发送交换接受通知失败: %v", err)
		// 继续执行，不中断流程
		return "", err
	}
	// initiatorTrainer := tool.GetActiveTrainerByTid(swop.InitiatorId)
	// if initiatorTrainer == nil {
	// 	return "", runtime.NewError("未找到发起方训练师信息", 400)
	// }
	// // 发送通知
	// if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop, targetTrainer, initiatorTrainer); err != nil {
	// 	logger.Error("发送交换接受通知失败: %v", err)
	// 	// 继续执行，不中断流程
	// }

	return tool.ProtoToBase64(swop)
}

// 接受交换请求
func AcceptSwopRequest(ctx context.Context, logger runtime.Logger, db *sql.DB,
	swopId int64, targetId int64) (*MainServer.SwopInfo, error) {

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	swop, err := getSwop(ctx, tx, swopId)
	if err != nil {
		return nil, err
	}

	if swop.TargetId != targetId {
		return nil, runtime.NewError("unauthorized", 403)
	}

	// 验证发起方状态
	if swop.InitiatorState > MainServer.SwopState_ACCEPTED || swop.InitiatorState == MainServer.SwopState_INIT {
		return nil, runtime.NewError("invalid initiator state", 400)
	}

	// 验证目标方状态
	if swop.TargetState >= MainServer.SwopState_ACCEPTED {
		return nil, runtime.NewError("invalid target state", 400)
	}

	// 更新目标方状态为选择，接受后直接开始选择
	swop.TargetState = MainServer.SwopState_ACCEPTED
	swop.InitiatorState = MainServer.SwopState_ACCEPTED
	swop.UpdateTs = time.Now().Unix()

	if err := updateSwop(ctx, tx, swop); err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return swop, nil
}

// RPC函数：提交交换内容
func RpcSubmitSwopContent(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	swopChangeStateInfo := &MainServer.SwopChangeContentInfo{}
	if err := tool.Base64ToProto(payload, swopChangeStateInfo); err != nil {
		logger.Error("解析请求参数失败: %v", err)
		return "", runtime.NewError("请求参数无效", 400)
	}

	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	// 提交交换内容
	swop, err := SubmitSwopContent(ctx, logger, db, swopChangeStateInfo.SwopId, trainer, swopChangeStateInfo.Content)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("提交交换内容失败: %v", err), 500)
	}
	// 发送通知
	if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop); err != nil {
		logger.Error("发送交换内容提交通知失败: %v", err)
		// 继续执行，不中断流程
		return "", err
	}
	// initiatorTrainer := tool.GetActiveTrainerByTid(swop.InitiatorId)
	// if initiatorTrainer == nil {
	// 	return "", runtime.NewError("未找到发起方训练师信息", 400)
	// }
	// targetTrainer := tool.GetActiveTrainerByTid(swop.TargetId)
	// if targetTrainer == nil {
	// 	return "", runtime.NewError("未找到目标训练师信息", 400)
	// }

	// if trainer.Id == swop.InitiatorId {
	// 	if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop, initiatorTrainer, targetTrainer); err != nil {
	// 		logger.Error("发送交换内容提交通知失败: %v", err)
	// 		return "", runtime.NewError("发送交换内容提交通知失败", 500)
	// 	}
	// } else {
	// 	if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop, targetTrainer, initiatorTrainer); err != nil {
	// 		logger.Error("发送交换内容提交通知失败: %v", err)
	// 		return "", runtime.NewError("发送交换内容提交通知失败", 500)
	// 	}
	// }

	return tool.ProtoToBase64(swop)
}

// 提交交换内容
func SubmitSwopContent(ctx context.Context, logger runtime.Logger, db *sql.DB,
	swopId int64, trainer *MainServer.Trainer, content *MainServer.SwopContent) (*MainServer.SwopInfo, error) {

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	swop, err := getSwop(ctx, tx, swopId)
	if err != nil {
		return nil, err
	}
	if trainer.Id != swop.InitiatorId && trainer.Id != swop.TargetId {
		return nil, runtime.NewError("unauthorized", 403)
	}
	if swop.InitiatorState < MainServer.SwopState_ACCEPTED ||
		swop.TargetState < MainServer.SwopState_ACCEPTED {
		return nil, runtime.NewError("invalid swop state", 400)
	}
	// 验证物品所有权
	if err := validateSwopContent(ctx, tx, trainer, content); err != nil {
		return nil, err
	}
	pokes, err := poke.QueryPokesByIdsAndUpdateTs(ctx, tx, trainer.Id, content.PokeIds, 0)
	if err != nil {
		return nil, err
	}
	content.Pokes = pokes

	// 更新对应方的内容和状态
	if trainer.Id == swop.InitiatorId {
		swop.InitiatorContent = content
		swop.InitiatorState = MainServer.SwopState_SUBMITTED
	} else if trainer.Id == swop.TargetId {
		swop.TargetContent = content
		swop.TargetState = MainServer.SwopState_SUBMITTED
	} else {
		return nil, runtime.NewError("unauthorized", 403)
	}

	swop.UpdateTs = time.Now().Unix()

	if err := updateSwop(ctx, tx, swop); err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return swop, nil
}

// RPC函数：确认交换
func RpcConfirmSwop(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	// swopChangeStateInfo := &MainServer.SwopChangeStateInfo{}
	// if err := tool.Base64ToProto(payload, swopChangeStateInfo); err != nil {
	// 	logger.Error("解析请求参数失败: %v", err)
	// 	return "", runtime.NewError("请求参数无效", 400)
	// }
	swopId, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", runtime.NewError("缺少交换请求ID", 400)
	}

	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	// 确认交换
	swop, err := ConfirmSwop(ctx, logger, db, swopId, trainer.Id)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("确认交换失败: %v", err), 500)
	}
	// 发送通知
	if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop); err != nil {
		logger.Error("发送交换确认通知失败: %v", err)
		// 继续执行，不中断流程
		return "", err
	}
	return tool.ProtoToBase64(swop)
}
func RpcCancelSubmitSwop(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	swopId, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", runtime.NewError("缺少交换请求ID", 400)
	}

	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	swop, err := CancelSubmitSwop(ctx, logger, db, swopId, trainer.Id)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("取消交换提交失败: %v", err), 500)
	}
	if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop); err != nil {
		logger.Error("发送交换取消提交通知失败: %v", err)
		// 继续执行，不中断流程
		return "", err
	}
	return tool.ProtoToBase64(swop)
}
func CancelSubmitSwop(ctx context.Context, logger runtime.Logger, db *sql.DB,
	swopId int64, trainerId int64) (*MainServer.SwopInfo, error) {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()
	swop, err := getSwop(ctx, tx, swopId)
	if err != nil {
		return nil, err
	}
	if trainerId != swop.InitiatorId && trainerId != swop.TargetId {
		return nil, runtime.NewError("unauthorized", 403)
	}
	if trainerId == swop.InitiatorId {
		if swop.InitiatorState != MainServer.SwopState_SUBMITTED {
			return nil, runtime.NewError("invalid initiator state", 400)
		}
		if swop.TargetState == MainServer.SwopState_CONFIRMED {
			swop.TargetState = MainServer.SwopState_SUBMITTED
		}
		swop.InitiatorState = MainServer.SwopState_SELECTING
		swop.InitiatorContent = &MainServer.SwopContent{
			PokeIds: []int64{},
			Items:   []*MainServer.SwopItem{},
		}
	} else if trainerId == swop.TargetId {
		if swop.TargetState != MainServer.SwopState_SUBMITTED {
			return nil, runtime.NewError("invalid target state", 400)
		}
		if swop.InitiatorState == MainServer.SwopState_CONFIRMED {
			swop.InitiatorState = MainServer.SwopState_SUBMITTED
		}
		swop.TargetState = MainServer.SwopState_SELECTING
		swop.TargetContent = &MainServer.SwopContent{
			PokeIds: []int64{},
			Items:   []*MainServer.SwopItem{},
		}
	} else {
		return nil, runtime.NewError("unauthorized", 403)
	}
	swop.UpdateTs = time.Now().Unix()
	if err := updateSwop(ctx, tx, swop); err != nil {
		return nil, err
	}
	if err := tx.Commit(); err != nil {
		return nil, err
	}
	return swop, nil
}

// RPC函数：取消确认交换
func RpcCancelConfirmSwop(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数
	// var params map[string]interface{}
	// if err := json.Unmarshal([]byte(payload), &params); err != nil {
	// 	logger.Error("解析请求参数失败: %v", err)
	// 	return "", runtime.NewError("请求参数无效", 400)
	// }

	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	// 获取交换请求ID
	swopId, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", runtime.NewError("缺少交换请求ID", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("开始事务失败: %v", err), 500)
	}
	defer tx.Rollback()

	// 查询交换信息
	swop, err := getSwop(ctx, tx, swopId)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("查询交换信息失败: %v", err), 500)
	}

	// 验证用户是否是交换的参与者
	if trainer.Id != swop.InitiatorId && trainer.Id != swop.TargetId {
		return "", runtime.NewError("无权取消确认此交换", 403)
	}

	// 验证交换状态
	if trainer.Id == swop.InitiatorId {
		if swop.InitiatorState != MainServer.SwopState_CONFIRMED {
			return "", runtime.NewError(fmt.Sprintf("发起方交换状态不正确，当前状态: %v", swop.InitiatorState), 400)
		}
		// 更新发起方状态为已提交（回退到上一步）
		swop.InitiatorState = MainServer.SwopState_SUBMITTED
	} else {
		if swop.TargetState != MainServer.SwopState_CONFIRMED {
			return "", runtime.NewError(fmt.Sprintf("目标方交换状态不正确，当前状态: %v", swop.TargetState), 400)
		}
		// 更新目标方状态为已提交（回退到上一步）
		swop.TargetState = MainServer.SwopState_SUBMITTED
	}

	swop.UpdateTs = time.Now().UnixMilli()

	// 保存交换信息
	if err := updateSwop(ctx, tx, swop); err != nil {
		return "", runtime.NewError(fmt.Sprintf("保存交换信息失败: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError(fmt.Sprintf("提交事务失败: %v", err), 500)
	}

	// 发送通知
	if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop); err != nil {
		logger.Error("发送交换取消确认通知失败: %v", err)
		// 继续执行，不中断流程
		return "", err
	}

	// 构建响应
	// responseJSON, err := json.Marshal(swop)
	// if err != nil {
	// 	return "", runtime.NewError(fmt.Sprintf("构建响应失败: %v", err), 500)
	// }

	return tool.ProtoToBase64(swop)
}

// RPC函数：关闭交换
func RpcCloseSwop(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 解析请求参数

	// 获取当前用户的训练师ID
	trainer := tool.GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return "", runtime.NewError("未找到训练师信息", 400)
	}

	// 获取交换请求ID
	swopId, err := strconv.ParseInt(payload, 10, 64)
	if err != nil {
		return "", runtime.NewError("缺少交换请求ID", 400)
	}

	// 获取关闭原因
	// reason, _ := params["reason"].(string)
	// if reason == "" {
	// reason := "用户主动关闭交换"
	// }

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("开始事务失败: %v", err), 500)
	}
	defer tx.Rollback()

	// 查询交换信息
	swop, err := getSwop(ctx, tx, swopId)
	if err != nil {
		return "", runtime.NewError(fmt.Sprintf("查询交换信息失败: %v", err), 500)
	}

	// 验证用户是否是交换的参与者
	if trainer.Id != swop.InitiatorId && trainer.Id != swop.TargetId {
		return "", runtime.NewError("无权关闭此交换", 403)
	}

	// 验证交换状态
	if swop.InitiatorState == MainServer.SwopState_COMPLETED && swop.TargetState == MainServer.SwopState_COMPLETED {
		return "", runtime.NewError("交换已完成，无法关闭", 400)
	}

	// 更新双方状态为已关闭
	swop.InitiatorState = MainServer.SwopState_CLOSED
	swop.TargetState = MainServer.SwopState_CLOSED
	swop.UpdateTs = time.Now().UnixMilli()

	// 保存交换信息
	if err := updateSwop(ctx, tx, swop); err != nil {
		return "", runtime.NewError(fmt.Sprintf("保存交换信息失败: %v", err), 500)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", runtime.NewError(fmt.Sprintf("提交事务失败: %v", err), 500)
	}
	//通知即使发送失败，也关闭
	tool.SendSwopInfoNotification(ctx, logger, nk, swop)
	// 发送通知
	// if err := tool.SendSwopInfoNotification(ctx, logger, nk, swop); err != nil {
	// 	logger.Error("发送交换关闭通知失败: %v", err)
	// 	// 继续执行，不中断流程
	// 	return "", err
	// }
	// 发送通知
	// if err := SendSwopCloseNotification(ctx, logger, nk, swop, trainer.Id, reason); err != nil {
	// 	logger.Error("发送交换关闭通知失败: %v", err)
	// 	// 继续执行，不中断流程
	// }

	// 构建响应
	// response := map[string]interface{}{
	// 	"success": true,
	// 	"message": "交换已关闭",
	// }
	// responseJSON, err := json.Marshal(response)
	// if err != nil {
	// 	return "", runtime.NewError(fmt.Sprintf("构建响应失败: %v", err), 500)
	// }

	return tool.ProtoToBase64(swop)
}

// 确认交换
func ConfirmSwop(ctx context.Context, logger runtime.Logger, db *sql.DB,
	swopId int64, tId int64) (*MainServer.SwopInfo, error) {

	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback()

	swop, err := getSwop(ctx, tx, swopId)
	if err != nil {
		return nil, err
	}

	// 验证交易方是否是参与者
	if tId != swop.InitiatorId && tId != swop.TargetId {
		return nil, runtime.NewError("unauthorized", 403)
	}

	// 更新对应方的状态为已确认
	if tId == swop.InitiatorId {
		// 验证发起方状态
		if swop.InitiatorState != MainServer.SwopState_SUBMITTED {
			return nil, runtime.NewError("invalid initiator state", 400)
		}
		swop.InitiatorState = MainServer.SwopState_CONFIRMED
	} else {
		// 验证目标方状态
		if swop.TargetState != MainServer.SwopState_SUBMITTED {
			return nil, runtime.NewError("invalid target state", 400)
		}
		swop.TargetState = MainServer.SwopState_CONFIRMED
	}

	swop.UpdateTs = time.Now().Unix()

	// 保存状态变更
	if err := updateSwop(ctx, tx, swop); err != nil {
		return nil, err
	}

	// 如果双方都已确认，则执行交换
	if swop.InitiatorState == MainServer.SwopState_CONFIRMED && swop.TargetState == MainServer.SwopState_CONFIRMED {
		// 执行实际的交换操作
		if err := executeSwop(ctx, logger, tx, swop); err != nil {
			return nil, err
		}

		// 更新双方状态为已完成
		swop.InitiatorState = MainServer.SwopState_COMPLETED
		swop.TargetState = MainServer.SwopState_COMPLETED
		swop.UpdateTs = time.Now().Unix()

		if err := updateSwop(ctx, tx, swop); err != nil {
			return nil, err
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return swop, nil
}

// 执行实际的交换操作
func executeSwop(ctx context.Context, logger runtime.Logger, tx *sql.Tx, swop *MainServer.SwopInfo) error {
	// 交换宝可梦
	if err := swopPokemons(ctx, logger, tx, swop); err != nil {
		return err
	}

	// 交换道具
	if err := swopItems(ctx, logger, tx, swop); err != nil {
		return err
	}

	return nil
}

// 交换宝可梦
func swopPokemons(ctx context.Context, logger runtime.Logger, tx *sql.Tx, swop *MainServer.SwopInfo) error {
	return trainer.BatchTransferPokemonsBetweenTrainers(ctx, logger, tx, swop.InitiatorId, swop.InitiatorContent.PokeIds, swop.TargetId, swop.TargetContent.PokeIds)
	// for _, pokeId := range swop.InitiatorContent.PokeIds {
	// 	if err := transferPokemon(ctx, tx, pokeId, swop.InitiatorId, swop.TargetId); err != nil {
	// 		return err
	// 	}
	// }

	// for _, pokeId := range swop.TargetContent.PokeIds {
	// 	if err := transferPokemon(ctx, tx, pokeId, swop.TargetId, swop.InitiatorId); err != nil {
	// 		return err
	// 	}
	// }

	// return nil
}

// func transferPokemon(ctx context.Context, tx *sql.Tx, pokeId int64, fromId int64, toId int64) error {
// 	// 使用 trainer 包中的 TransferPokemon 函数
// 	// 由于在这个上下文中没有 logger，我们传递 nil
// 	return trainer.TransferPokemon(ctx, nil, tx, pokeId, fromId, toId)
// }

// 交换道具
func swopItems(ctx context.Context, logger runtime.Logger, tx *sql.Tx, swop *MainServer.SwopInfo) error {
	for _, item := range swop.InitiatorContent.Items {
		if err := inventory.RemoveItem(ctx, tx, swop.InitiatorId, item.Name, int(item.Count)); err != nil {
			return err
		}
		if err := inventory.AddItem(ctx, tx, swop.TargetId, item.Name, item.Count); err != nil {
			return err
		}
	}

	for _, item := range swop.TargetContent.Items {
		if err := inventory.RemoveItem(ctx, tx, swop.TargetId, item.Name, int(item.Count)); err != nil {
			return err
		}
		if err := inventory.AddItem(ctx, tx, swop.InitiatorId, item.Name, item.Count); err != nil {
			return err
		}
	}

	return nil
}

// 从数据库获取交易信息
func getSwop(ctx context.Context, tx *sql.Tx, swopId int64) (*MainServer.SwopInfo, error) {
	swop := &MainServer.SwopInfo{
		InitiatorContent: &MainServer.SwopContent{},
		TargetContent:    &MainServer.SwopContent{},
	}

	var (
		initiatorState   int32
		targetState      int32
		initiatorContent []byte
		targetContent    []byte
	)

	err := tx.QueryRowContext(ctx,
		fmt.Sprintf("SELECT id, initiator_id, target_id, initiator_state, target_state, initiator_content, target_content, create_ts, update_ts FROM %s WHERE id = $1",
			TableSwopName),
		swopId).Scan(
		&swop.SwopId,
		&swop.InitiatorId,
		&swop.TargetId,
		&initiatorState,
		&targetState,
		&initiatorContent,
		&targetContent,
		&swop.CreateTs,
		&swop.UpdateTs)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, runtime.NewError("swop not found", 404)
		}
		return nil, err
	}

	// 设置状态
	swop.InitiatorState = MainServer.SwopState(initiatorState)
	swop.TargetState = MainServer.SwopState(targetState)

	// 解析交换内容
	if len(initiatorContent) > 0 {
		if err := json.Unmarshal(initiatorContent, swop.InitiatorContent); err != nil {
			return nil, fmt.Errorf("解析发起方交换内容失败: %w", err)
		}
	}

	if len(targetContent) > 0 {
		if err := json.Unmarshal(targetContent, swop.TargetContent); err != nil {
			return nil, fmt.Errorf("解析目标方交换内容失败: %w", err)
		}
	}

	return swop, nil
}

// 更新交易信息
func updateSwop(ctx context.Context, tx *sql.Tx, swop *MainServer.SwopInfo) error {
	// 序列化交换内容
	initiatorContentBytes, err := json.Marshal(swop.InitiatorContent)
	if err != nil {
		return fmt.Errorf("序列化发起方内容失败: %w", err)
	}

	targetContentBytes, err := json.Marshal(swop.TargetContent)
	if err != nil {
		return fmt.Errorf("序列化目标方内容失败: %w", err)
	}

	_, err = tx.ExecContext(ctx,
		fmt.Sprintf("UPDATE %s SET initiator_state = $1, target_state = $2, initiator_content = $3, target_content = $4, update_ts = $5 WHERE id = $6",
			TableSwopName),
		int32(swop.InitiatorState),
		int32(swop.TargetState),
		initiatorContentBytes,
		targetContentBytes,
		swop.UpdateTs,
		swop.SwopId)

	return err
}

// 验证交换内容的所有权
func validateSwopContent(ctx context.Context, tx *sql.Tx, trainer *MainServer.Trainer, content *MainServer.SwopContent) error {
	if trainer.Coin < content.Coin {
		return runtime.NewError("insufficient coin", 403)
	}
	// 验证宝可梦所有权
	owned, err := poke.CheckPokemonsOwnership(ctx, tx, content.PokeIds, trainer.Id)
	// for _, pokeId := range content.PokeIds {
	// 	owned, err := CheckPokemonsOwnership(ctx, tx, pokeId, userId)
	if err != nil {
		return err
	}
	if !owned {
		return runtime.NewError("pokemon not owned", 403)
	}

	// 验证道具所有权和数量
	for _, item := range content.Items {
		sufficient, err := inventory.CheckItemQuantity(ctx, tx, trainer.Id, item.Name, int(item.Count))
		if err != nil {
			return err
		}
		if !sufficient {
			return runtime.NewError("insufficient items", 403)
		}
	}

	return nil
}

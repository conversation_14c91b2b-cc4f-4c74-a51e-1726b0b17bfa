{"items": {"pokeball": {"name": "pokeball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 1}, "greatball": {"name": "greatball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 2}, "ultraball": {"name": "ultraball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 3}, "safariball": {"name": "safariball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 4}, "masterball": {"name": "masterball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 5}, "fastball": {"name": "fastball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 6}, "levelball": {"name": "levelball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 7}, "lureball": {"name": "lureball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 8}, "loveball": {"name": "loveball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 9}, "friendball": {"name": "friendball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 10}, "moonball": {"name": "moonball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 11}, "heavyball": {"name": "heavyball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 12}, "leadenball": {"name": "leadenball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 13}, "gigatonball": {"name": "gigatonball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 14}, "sportball": {"name": "sportball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 15}, "netball": {"name": "netball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 17}, "diveball": {"name": "diveball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 18}, "nestball": {"name": "nestball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 19}, "repeatball": {"name": "repeatball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 20}, "timerball": {"name": "timerball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 21}, "luxuryball": {"name": "luxuryball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 22}, "premierball": {"name": "premierball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 23}, "duskball": {"name": "duskball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 24}, "healball": {"name": "healball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 25}, "quickball": {"name": "quickball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 26}, "cherishball": {"name": "cherishball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 27}, "dreamball": {"name": "dreamball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 28}, "beastball": {"name": "beastball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 29}, "featherball": {"name": "featherball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 30}, "wingball": {"name": "wingball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 31}, "jetball": {"name": "jetball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 32}, "strangeball": {"name": "strangeball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 33}, "abilitycapsule": {"name": "abilitycapsule", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 1}, "abilitypatch": {"name": "abilitypatch", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 2}, "rarecandy": {"name": "rarecandy", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 3}, "healthwing": {"name": "healthwing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 4}, "musclewing": {"name": "musclewing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 5}, "resistwing": {"name": "resistwing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 6}, "geniuswing": {"name": "geniuswing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 7}, "cleverwing": {"name": "cleverwing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 8}, "swiftwing": {"name": "swiftwing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 9}, "bottlecap": {"name": "bottlecap", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 10}, "goldbottlecap": {"name": "goldbottlecap", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 11}, "lonelymint": {"name": "lonelymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 12}, "adamantmint": {"name": "adamantmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 13}, "naughtymint": {"name": "naughtymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 14}, "bravemint": {"name": "bravemint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 15}, "boldmint": {"name": "boldmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 16}, "impishmint": {"name": "impishmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 17}, "laxmint": {"name": "laxmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 18}, "relaxedmint": {"name": "relaxedmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 19}, "modestmint": {"name": "modestmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 20}, "mildmint": {"name": "mildmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 21}, "rashmint": {"name": "rashmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 22}, "quietmint": {"name": "quietmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 23}, "calmmint": {"name": "calmmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 24}, "gentlemint": {"name": "gentlemint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 25}, "carefulmint": {"name": "carefulmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 26}, "sassymint": {"name": "sassymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 27}, "timidmint": {"name": "timidmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 28}, "hastymint": {"name": "hastymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 29}, "jollymint": {"name": "jollymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 30}, "naivemint": {"name": "naivemint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 31}, "seriousmint": {"name": "seriousmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 32}, "heartscale": {"name": "heartscale", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 33}, "normalterashard": {"name": "normalterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 34}, "fireterashard": {"name": "fireterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 35}, "waterterashard": {"name": "waterterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 36}, "electricterashard": {"name": "electricterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 37}, "grassterashard": {"name": "grassterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 38}, "iceterashard": {"name": "iceterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 39}, "fightingterashard": {"name": "fightingterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 40}, "poisonterashard": {"name": "poisonterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 41}, "groundterashard": {"name": "groundterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 42}, "flyingterashard": {"name": "flyingterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 43}, "psychicterashard": {"name": "psychicterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 44}, "bugterashard": {"name": "bugterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 45}, "rockterashard": {"name": "rockterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 46}, "ghostterashard": {"name": "ghostterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 47}, "dragonterashard": {"name": "dragonterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 48}, "darkterashard": {"name": "darkterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 49}, "steelterashard": {"name": "steelterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 50}, "fairyterashard": {"name": "fairy<PERSON>shard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 51}, "stellarterashard": {"name": "stellarterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 52}, "dynamaxcandy": {"name": "dynamaxcandy", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 53}, "firestone": {"name": "firestone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 54}, "thunderstone": {"name": "thunderstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 55}, "waterstone": {"name": "waterstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 56}, "leafstone": {"name": "leafstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 57}, "moonstone": {"name": "moonstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 58}, "sunstone": {"name": "sunstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 59}, "shinystone": {"name": "shinystone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 60}, "duskstone": {"name": "duskstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 61}, "dawnstone": {"name": "dawnstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 62}, "icestone": {"name": "icestone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 63}, "sweetapple": {"name": "sweetapple", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 64}, "tartapple": {"name": "tartapple", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 65}, "crackedpot": {"name": "crackedpot", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 66}, "chippedpot": {"name": "chippedpot", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 67}, "galaricacuff": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 68}, "galaricawreath": {"name": "galaricawreath", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 69}, "linkingcord": {"name": "linkingcord", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 70}, "blackaugurite": {"name": "blackaugurite", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 71}, "peatblock": {"name": "peatblock", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 72}, "auspiciousarmor": {"name": "auspiciousarmor", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 73}, "maliciousarmor": {"name": "malicious<PERSON>or", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 74}, "syrupyapple": {"name": "syrupyapple", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 75}, "unremarkableteacup": {"name": "unremarkable<PERSON><PERSON>up", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 76}, "masterpieceteacup": {"name": "masterpieceteacup", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 77}, "metalalloy": {"name": "metalalloy", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 78}, "potion": {"name": "potion", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 1}, "superpotion": {"name": "superpotion", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 2}, "hyperpotion": {"name": "hyperpotion", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 3}, "maxpotion": {"name": "maxpotion", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 4}, "fullrestore": {"name": "<PERSON><PERSON><PERSON>", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 5}, "antidote": {"name": "antidote", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 6}, "burnheal": {"name": "burnheal", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 7}, "iceheal": {"name": "iceheal", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 8}, "awakening": {"name": "awakening", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 9}, "paralyzeheal": {"name": "paralyzeheal", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 10}, "fullheal": {"name": "fullheal", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 11}, "revive": {"name": "revive", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 12}, "maxrevive": {"name": "maxrevive", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 13}, "ether": {"name": "ether", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 14}, "maxether": {"name": "maxether", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 15}, "elixir": {"name": "elixir", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 16}, "maxelixir": {"name": "maxelixir", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 17}, "sacredash": {"name": "sacredash", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 18}, "abilityshield": {"name": "abilityshield", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 1}, "abomasite": {"name": "abomasite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 2}, "absolite": {"name": "absolite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 3}, "absorbbulb": {"name": "absorbbulb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 4}, "adamantcrystal": {"name": "adamantcrystal", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 5}, "adamantorb": {"name": "adamantorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 6}, "adrenalineorb": {"name": "adrenalineorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 7}, "aerodactylite": {"name": "aerodactylite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 8}, "aggronite": {"name": "aggronite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 9}, "aguavberry": {"name": "aguavberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 10}, "airballoon": {"name": "airballoon", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 11}, "alakazite": {"name": "alakazite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 12}, "aloraichiumz": {"name": "aloraichiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 13}, "altarianite": {"name": "altarianite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 14}, "ampharosite": {"name": "ampharosite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 15}, "apicotberry": {"name": "apicotberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 16}, "armorfossil": {"name": "armorfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 17}, "aspearberry": {"name": "as<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 18}, "assaultvest": {"name": "assaultvest", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 19}, "audinite": {"name": "audinite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 20}, "babiriberry": {"name": "babiriberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 22}, "banettite": {"name": "banettite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 23}, "beedrillite": {"name": "beedrillite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 25}, "belueberry": {"name": "belueberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 26}, "berryjuice": {"name": "berryjuice", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 27}, "berrysweet": {"name": "berrysweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 28}, "bignugget": {"name": "bignugget", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 29}, "bigroot": {"name": "bigroot", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 30}, "bindingband": {"name": "bindingband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 31}, "blackbelt": {"name": "blackbelt", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 32}, "blackglasses": {"name": "blackglasses", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 33}, "blacksludge": {"name": "blacksludge", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 34}, "blastoisinite": {"name": "blastoisinite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 35}, "blazikenite": {"name": "blazikenite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 36}, "blueorb": {"name": "blueorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 37}, "blukberry": {"name": "blukberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 38}, "blunderpolicy": {"name": "blunderpolicy", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 39}, "boosterenergy": {"name": "boosterenergy", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 40}, "brightpowder": {"name": "brightpowder", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 42}, "buggem": {"name": "buggem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 43}, "bugmemory": {"name": "bugmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 44}, "buginiumz": {"name": "buginiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 45}, "burndrive": {"name": "burndrive", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 46}, "cameruptite": {"name": "cameruptite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 47}, "cellbattery": {"name": "cellbattery", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 48}, "charcoal": {"name": "charcoal", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 49}, "charizarditex": {"name": "charizarditex", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 50}, "charizarditey": {"name": "charizarditey", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 51}, "chartiberry": {"name": "chartiberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 52}, "cheriberry": {"name": "cheriberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 53}, "chestoberry": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 55}, "chilanberry": {"name": "chilanberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 56}, "chilldrive": {"name": "chilldrive", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 57}, "choiceband": {"name": "choiceband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 59}, "choicescarf": {"name": "choicescarf", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 60}, "choicespecs": {"name": "choicespecs", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 61}, "chopleberry": {"name": "chopleberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 62}, "clawfossil": {"name": "clawfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 63}, "clearamulet": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 64}, "cloversweet": {"name": "cloversweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 65}, "cobaberry": {"name": "cobaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 66}, "colburberry": {"name": "colburberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 67}, "cornerstonemask": {"name": "cornerstonemask", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 68}, "cornnberry": {"name": "cornnberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 69}, "coverfossil": {"name": "coverfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 70}, "covertcloak": {"name": "covertcloak", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 71}, "custapberry": {"name": "custapberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 73}, "damprock": {"name": "damprock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 74}, "darkgem": {"name": "darkgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 75}, "darkmemory": {"name": "darkmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 76}, "darkiniumz": {"name": "darkiniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 77}, "decidiumz": {"name": "decidiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 79}, "deepseascale": {"name": "deepseascale", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 80}, "deepseatooth": {"name": "deepseatooth", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 81}, "destinyknot": {"name": "destinyknot", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 82}, "diancite": {"name": "diancite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 83}, "domefossil": {"name": "domefossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 85}, "dousedrive": {"name": "dousedrive", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 86}, "dracoplate": {"name": "dracoplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 87}, "dragonfang": {"name": "dragonfang", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 88}, "dragongem": {"name": "dragongem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 89}, "dragonmemory": {"name": "dragonmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 90}, "dragonscale": {"name": "dragonscale", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 91}, "dragoniumz": {"name": "dragoniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 92}, "dreadplate": {"name": "dreadplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 93}, "dubiousdisc": {"name": "dubiousdisc", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 95}, "durinberry": {"name": "durinberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 96}, "earthplate": {"name": "earthplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 99}, "eeviumz": {"name": "eeviumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 100}, "ejectbutton": {"name": "ejectbutton", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 101}, "ejectpack": {"name": "ejectpack", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 102}, "electirizer": {"name": "electirizer", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 103}, "electricgem": {"name": "electricgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 104}, "electricmemory": {"name": "electricmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 105}, "electricseed": {"name": "electricseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 106}, "electriumz": {"name": "electriumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 107}, "enigmaberry": {"name": "enigmaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 108}, "eviolite": {"name": "eviolite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 109}, "expertbelt": {"name": "expertbelt", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 110}, "fairiumz": {"name": "<PERSON><PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 111}, "fairyfeather": {"name": "fairyfeather", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 112}, "fairygem": {"name": "fairygem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 113}, "fairymemory": {"name": "fairymemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 114}, "fightinggem": {"name": "fightinggem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 116}, "fightingmemory": {"name": "fightingmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 117}, "fightiniumz": {"name": "fightiniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 118}, "figyberry": {"name": "figyberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 119}, "firegem": {"name": "firegem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 120}, "firememory": {"name": "firememory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 121}, "firiumz": {"name": "fi<PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 123}, "fistplate": {"name": "fistplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 124}, "flameorb": {"name": "flameorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 125}, "flameplate": {"name": "flameplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 126}, "floatstone": {"name": "floatstone", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 127}, "flowersweet": {"name": "flowersweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 128}, "flyinggem": {"name": "flyinggem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 129}, "flyingmemory": {"name": "flyingmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 130}, "flyiniumz": {"name": "flyiniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 131}, "focusband": {"name": "focusband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 132}, "focussash": {"name": "focussash", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 133}, "fossilizedbird": {"name": "fossilizedbird", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 134}, "fossilizeddino": {"name": "fossilizeddino", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 135}, "fossilizeddrake": {"name": "fossilizeddrake", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 136}, "fossilizedfish": {"name": "fossilizedfish", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 137}, "fullincense": {"name": "fullincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 139}, "galladite": {"name": "galladite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 142}, "ganlonberry": {"name": "gan<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 143}, "garchompite": {"name": "garchompite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 144}, "gardevoirite": {"name": "gardevoirite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 145}, "gengarite": {"name": "gengarite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 146}, "ghostgem": {"name": "ghostgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 147}, "ghostmemory": {"name": "ghostmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 148}, "ghostiumz": {"name": "ghostiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 149}, "glalitite": {"name": "glalitite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 150}, "grassgem": {"name": "grassgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 152}, "grassmemory": {"name": "grassmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 153}, "grassiumz": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 154}, "grassyseed": {"name": "grassyseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 155}, "grepaberry": {"name": "grepaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 157}, "gripclaw": {"name": "grip<PERSON>law", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 158}, "griseouscore": {"name": "griseouscore", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 159}, "griseousorb": {"name": "griseousorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 160}, "groundgem": {"name": "groundgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 161}, "groundmemory": {"name": "groundmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 162}, "groundiumz": {"name": "groundiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 163}, "gyaradosite": {"name": "gyaradosite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 164}, "habanberry": {"name": "habanberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 165}, "hardstone": {"name": "hardstone", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 166}, "hearthflamemask": {"name": "hearthflamemask", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 168}, "heatrock": {"name": "heatrock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 169}, "heavydutyboots": {"name": "heavydutyboots", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 171}, "helixfossil": {"name": "helixfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 172}, "heracronite": {"name": "heracronite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 173}, "hondewberry": {"name": "hondewberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 174}, "houndoominite": {"name": "houndoominite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 175}, "iapapaberry": {"name": "iapapaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 176}, "icegem": {"name": "icegem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 177}, "icememory": {"name": "icememory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 178}, "icicleplate": {"name": "icicleplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 180}, "iciumz": {"name": "ici<PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 181}, "icyrock": {"name": "icyrock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 182}, "inciniumz": {"name": "inciniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 183}, "insectplate": {"name": "insectplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 184}, "ironball": {"name": "ironball", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 185}, "ironplate": {"name": "ironplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 186}, "jabocaberry": {"name": "jabocaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 187}, "jawfossil": {"name": "jawfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 188}, "kasibberry": {"name": "kasibberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 189}, "kebiaberry": {"name": "kebiaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 190}, "keeberry": {"name": "keeberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 191}, "kelpsyberry": {"name": "kelpsyberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 192}, "kangaskhanite": {"name": "kangaskhanite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 193}, "kingsrock": {"name": "kingsrock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 194}, "kommoniumz": {"name": "kommoniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 195}, "laggingtail": {"name": "laggingtail", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 196}, "lansatberry": {"name": "lansatberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 197}, "latiasite": {"name": "latiasite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 198}, "latiosite": {"name": "latiosite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 199}, "laxincense": {"name": "laxincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 200}, "leek": {"name": "leek", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 202}, "leftovers": {"name": "leftovers", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 203}, "leppaberry": {"name": "le<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 204}, "liechiberry": {"name": "lie<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 206}, "lifeorb": {"name": "lifeorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 207}, "lightball": {"name": "lightball", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 208}, "lightclay": {"name": "lightclay", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 209}, "loadeddice": {"name": "loadeddice", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 210}, "lopunnite": {"name": "lopunnite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 211}, "lovesweet": {"name": "lovesweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 213}, "lucarionite": {"name": "lucarionite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 214}, "luckypunch": {"name": "luckypunch", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 215}, "lumberry": {"name": "lumberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 216}, "luminousmoss": {"name": "luminousmoss", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 217}, "lunaliumz": {"name": "lunaliumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 218}, "lustrousglobe": {"name": "lustrousglobe", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 220}, "lustrousorb": {"name": "lustrousorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 221}, "lycaniumz": {"name": "lycaniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 223}, "machobrace": {"name": "machobrace", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 224}, "magmarizer": {"name": "magmarizer", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 225}, "magnet": {"name": "magnet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 226}, "magoberry": {"name": "magoberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 227}, "magostberry": {"name": "magostberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 228}, "mail": {"name": "mail", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 229}, "manectite": {"name": "manectite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 231}, "marangaberry": {"name": "marangaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 232}, "marshadiumz": {"name": "marshadiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 233}, "mawilite": {"name": "mawi<PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 236}, "meadowplate": {"name": "meadowplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 237}, "medichamite": {"name": "medichamite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 238}, "mentalherb": {"name": "mentalherb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 239}, "metagrossite": {"name": "metagrossite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 240}, "metalcoat": {"name": "metalcoat", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 242}, "metalpowder": {"name": "metalpowder", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 243}, "metronome": {"name": "metronome", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 244}, "mewniumz": {"name": "mewn<PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 245}, "mewtwonitex": {"name": "mewtwonitex", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 246}, "mewtwonitey": {"name": "mewtwonitey", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 247}, "micleberry": {"name": "micleberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 248}, "mimikiumz": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 249}, "mindplate": {"name": "mindplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 250}, "miracleseed": {"name": "miracleseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 251}, "mirrorherb": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 252}, "mistyseed": {"name": "mistyseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 253}, "muscleband": {"name": "muscleband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 256}, "mysticwater": {"name": "mysticwater", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 257}, "nanabberry": {"name": "na<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 258}, "nevermeltice": {"name": "nevermeltice", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 261}, "nomelberry": {"name": "nomelberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 262}, "normalgem": {"name": "normalgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 263}, "normaliumz": {"name": "normaliumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 264}, "occaberry": {"name": "occaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 265}, "oddincense": {"name": "oddincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 266}, "oldamber": {"name": "oldamber", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 267}, "oranberry": {"name": "oranberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 268}, "ovalstone": {"name": "ovalstone", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 269}, "pamtreberry": {"name": "pam<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 270}, "parkball": {"name": "parkball", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 271}, "passhoberry": {"name": "pass<PERSON>berry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 272}, "payapaberry": {"name": "payapaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 273}, "pechaberry": {"name": "pechaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 274}, "persimberry": {"name": "persimberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 275}, "petayaberry": {"name": "petayaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 276}, "pidgeotite": {"name": "pidgeotite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 277}, "pikaniumz": {"name": "pikaniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 278}, "pikashuniumz": {"name": "pikashuniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 279}, "pinapberry": {"name": "pin<PERSON>berry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 280}, "pinsirite": {"name": "pinsirite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 281}, "pixieplate": {"name": "pixie<PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 282}, "plumefossil": {"name": "plumefossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 283}, "poisonbarb": {"name": "poisonbarb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 284}, "poisongem": {"name": "poisongem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 285}, "poisonmemory": {"name": "poisonmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 286}, "poisoniumz": {"name": "poisoniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 287}, "pomegberry": {"name": "pomegberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 289}, "poweranklet": {"name": "poweranklet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 290}, "powerband": {"name": "powerband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 291}, "powerbelt": {"name": "powerbelt", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 292}, "powerbracer": {"name": "powerbracer", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 293}, "powerherb": {"name": "powerherb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 294}, "powerlens": {"name": "powerlens", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 295}, "powerweight": {"name": "powerweight", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 296}, "primariumz": {"name": "primariumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 298}, "prismscale": {"name": "prismscale", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 299}, "protectivepads": {"name": "protectivepads", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 300}, "protector": {"name": "protector", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 301}, "psychicgem": {"name": "psychicgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 302}, "psychicmemory": {"name": "psychicmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 303}, "psychicseed": {"name": "psychicseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 304}, "psychiumz": {"name": "psychiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 305}, "punchingglove": {"name": "punchingglove", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 306}, "qualotberry": {"name": "qualotberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 307}, "quickclaw": {"name": "quickclaw", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 309}, "quickpowder": {"name": "quickpowder", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 310}, "rabutaberry": {"name": "rabutaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 311}, "rarebone": {"name": "rarebone", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 312}, "rawstberry": {"name": "rawstberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 313}, "razorclaw": {"name": "razorclaw", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 314}, "razorfang": {"name": "razorfang", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 315}, "razzberry": {"name": "razzberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 316}, "reapercloth": {"name": "reapercloth", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 317}, "redcard": {"name": "redcard", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 318}, "redorb": {"name": "redorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 319}, "ribbonsweet": {"name": "ribbonsweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 321}, "rindoberry": {"name": "rindoberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 322}, "ringtarget": {"name": "ringtarget", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 323}, "rockgem": {"name": "rockgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 324}, "rockincense": {"name": "rockincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 325}, "rockmemory": {"name": "rockmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 326}, "rockiumz": {"name": "rockiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 327}, "rockyhelmet": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 328}, "roomservice": {"name": "roomservice", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 329}, "rootfossil": {"name": "rootfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 330}, "roseincense": {"name": "roseincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 331}, "roseliberry": {"name": "roseliberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 332}, "rowapberry": {"name": "row<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 333}, "rustedshield": {"name": "rustedshield", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 334}, "rustedsword": {"name": "rustedsword", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 335}, "sablenite": {"name": "sablenite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 336}, "sachet": {"name": "sachet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 337}, "safetygoggles": {"name": "safetygoggles", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 339}, "sailfossil": {"name": "sailfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 340}, "salacberry": {"name": "salacberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 341}, "salamencite": {"name": "salamencite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 342}, "sceptilite": {"name": "sceptilite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 343}, "scizorite": {"name": "scizorite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 344}, "scopelens": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 345}, "seaincense": {"name": "seaincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 346}, "sharpbeak": {"name": "sharpbeak", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 347}, "sharpedonite": {"name": "sharpedonite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 348}, "shedshell": {"name": "shedshell", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 349}, "shellbell": {"name": "shellbell", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 350}, "shockdrive": {"name": "shockdrive", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 352}, "shucaberry": {"name": "shucaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 353}, "silkscarf": {"name": "silkscarf", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 354}, "silverpowder": {"name": "silverpowder", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 355}, "sitrusberry": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 356}, "skullfossil": {"name": "skullfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 357}, "skyplate": {"name": "skyplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 358}, "slowbronite": {"name": "slowbronite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 359}, "smoothrock": {"name": "smoothrock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 360}, "snorliumz": {"name": "snor<PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 361}, "snowball": {"name": "snowball", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 362}, "softsand": {"name": "softsand", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 363}, "solganiumz": {"name": "solganiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 364}, "souldew": {"name": "souldew", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 365}, "spelltag": {"name": "spelltag", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 366}, "spelonberry": {"name": "spelonberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 367}, "splashplate": {"name": "splashplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 368}, "spookyplate": {"name": "spookyplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 369}, "starfberry": {"name": "starfberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 371}, "starsweet": {"name": "starsweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 372}, "steelixite": {"name": "steelixite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 373}, "steelgem": {"name": "steelgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 374}, "steelmemory": {"name": "steelmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 375}, "steeliumz": {"name": "steeliumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 376}, "stick": {"name": "stick", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 377}, "stickybarb": {"name": "stickybarb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 378}, "stoneplate": {"name": "stoneplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 379}, "strawberrysweet": {"name": "strawberrysweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 381}, "swampertite": {"name": "swampertite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 383}, "tamatoberry": {"name": "tamatoberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 386}, "tangaberry": {"name": "tangaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 387}, "tapuniumz": {"name": "tap<PERSON>umz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 388}, "terrainextender": {"name": "terrainextender", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 390}, "thickclub": {"name": "thickclub", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 391}, "throatspray": {"name": "throatspray", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 392}, "toxicorb": {"name": "toxicorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 395}, "toxicplate": {"name": "toxicplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 396}, "tr00": {"name": "tr00", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 397}, "tr01": {"name": "tr01", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 398}, "tr02": {"name": "tr02", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 399}, "tr03": {"name": "tr03", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 400}, "tr04": {"name": "tr04", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 401}, "tr05": {"name": "tr05", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 402}, "tr06": {"name": "tr06", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 403}, "tr07": {"name": "tr07", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 404}, "tr08": {"name": "tr08", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 405}, "tr09": {"name": "tr09", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 406}, "tr10": {"name": "tr10", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 407}, "tr11": {"name": "tr11", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 408}, "tr12": {"name": "tr12", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 409}, "tr13": {"name": "tr13", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 410}, "tr14": {"name": "tr14", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 411}, "tr15": {"name": "tr15", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 412}, "tr16": {"name": "tr16", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 413}, "tr17": {"name": "tr17", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 414}, "tr18": {"name": "tr18", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 415}, "tr19": {"name": "tr19", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 416}, "tr20": {"name": "tr20", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 417}, "tr21": {"name": "tr21", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 418}, "tr22": {"name": "tr22", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 419}, "tr23": {"name": "tr23", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 420}, "tr24": {"name": "tr24", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 421}, "tr25": {"name": "tr25", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 422}, "tr26": {"name": "tr26", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 423}, "tr27": {"name": "tr27", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 424}, "tr28": {"name": "tr28", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 425}, "tr29": {"name": "tr29", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 426}, "tr30": {"name": "tr30", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 427}, "tr31": {"name": "tr31", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 428}, "tr32": {"name": "tr32", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 429}, "tr33": {"name": "tr33", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 430}, "tr34": {"name": "tr34", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 431}, "tr35": {"name": "tr35", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 432}, "tr36": {"name": "tr36", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 433}, "tr37": {"name": "tr37", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 434}, "tr38": {"name": "tr38", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 435}, "tr39": {"name": "tr39", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 436}, "tr40": {"name": "tr40", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 437}, "tr41": {"name": "tr41", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 438}, "tr42": {"name": "tr42", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 439}, "tr43": {"name": "tr43", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 440}, "tr44": {"name": "tr44", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 441}, "tr45": {"name": "tr45", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 442}, "tr46": {"name": "tr46", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 443}, "tr47": {"name": "tr47", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 444}, "tr48": {"name": "tr48", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 445}, "tr49": {"name": "tr49", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 446}, "tr50": {"name": "tr50", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 447}, "tr51": {"name": "tr51", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 448}, "tr52": {"name": "tr52", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 449}, "tr53": {"name": "tr53", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 450}, "tr54": {"name": "tr54", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 451}, "tr55": {"name": "tr55", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 452}, "tr56": {"name": "tr56", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 453}, "tr57": {"name": "tr57", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 454}, "tr58": {"name": "tr58", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 455}, "tr59": {"name": "tr59", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 456}, "tr60": {"name": "tr60", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 457}, "tr61": {"name": "tr61", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 458}, "tr62": {"name": "tr62", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 459}, "tr63": {"name": "tr63", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 460}, "tr64": {"name": "tr64", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 461}, "tr65": {"name": "tr65", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 462}, "tr66": {"name": "tr66", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 463}, "tr67": {"name": "tr67", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 464}, "tr68": {"name": "tr68", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 465}, "tr69": {"name": "tr69", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 466}, "tr70": {"name": "tr70", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 467}, "tr71": {"name": "tr71", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 468}, "tr72": {"name": "tr72", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 469}, "tr73": {"name": "tr73", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 470}, "tr74": {"name": "tr74", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 471}, "tr75": {"name": "tr75", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 472}, "tr76": {"name": "tr76", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 473}, "tr77": {"name": "tr77", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 474}, "tr78": {"name": "tr78", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 475}, "tr79": {"name": "tr79", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 476}, "tr80": {"name": "tr80", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 477}, "tr81": {"name": "tr81", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 478}, "tr82": {"name": "tr82", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 479}, "tr83": {"name": "tr83", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 480}, "tr84": {"name": "tr84", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 481}, "tr85": {"name": "tr85", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 482}, "tr86": {"name": "tr86", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 483}, "tr87": {"name": "tr87", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 484}, "tr88": {"name": "tr88", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 485}, "tr89": {"name": "tr89", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 486}, "tr90": {"name": "tr90", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 487}, "tr91": {"name": "tr91", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 488}, "tr92": {"name": "tr92", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 489}, "tr93": {"name": "tr93", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 490}, "tr94": {"name": "tr94", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 491}, "tr95": {"name": "tr95", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 492}, "tr96": {"name": "tr96", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 493}, "tr97": {"name": "tr97", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 494}, "tr98": {"name": "tr98", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 495}, "tr99": {"name": "tr99", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 496}, "twistedspoon": {"name": "twistedspoon", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 497}, "tyranitarite": {"name": "tyranitarite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 498}, "ultranecroziumz": {"name": "ultranecroziumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 500}, "upgrade": {"name": "upgrade", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 502}, "utilityumbrella": {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 503}, "venusaurite": {"name": "venusaurite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 504}, "wacanberry": {"name": "wacanberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 505}, "watergem": {"name": "watergem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 506}, "watermemory": {"name": "watermemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 507}, "wateriumz": {"name": "wateriumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 509}, "watmelberry": {"name": "watmelberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 510}, "waveincense": {"name": "waveincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 511}, "weaknesspolicy": {"name": "weaknesspolicy", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 512}, "wellspringmask": {"name": "wellspringmask", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 513}, "wepearberry": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 514}, "whippeddream": {"name": "whippeddream", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 515}, "whiteherb": {"name": "whiteherb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 516}, "widelens": {"name": "widelens", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 517}, "wikiberry": {"name": "wikiberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 518}, "wiseglasses": {"name": "wiseglasses", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 519}, "yacheberry": {"name": "yacheberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 520}, "zapplate": {"name": "zapplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 521}, "zoomlens": {"name": "zoomlens", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 522}, "berserkgene": {"name": "berserkgene", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 523}, "berry": {"name": "berry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 524}, "bitterberry": {"name": "bitterberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 525}, "burntberry": {"name": "burntberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 526}, "goldberry": {"name": "goldberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 527}, "iceberry": {"name": "iceberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 528}, "mintberry": {"name": "mintberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 529}, "miracleberry": {"name": "miracleberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 530}, "mysteryberry": {"name": "mysteryberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 531}, "pinkbow": {"name": "pinkbow", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 532}, "polkadotbow": {"name": "polkadotbow", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 533}, "przcureberry": {"name": "prz<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 534}, "psncureberry": {"name": "psncureberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 535}, "crucibellite": {"name": "crucibellite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 536}, "vilevial": {"name": "vilevial", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 537}, "mewtwoquestslate": {"name": "mewtwoquestslate", "cost": 0, "inventory_type": 10, "duration": 180, "teams": [0], "team_cost": 0, "sort": 1}, "mewtwomail_special": {"name": "mewtwomail_special", "cost": 0, "inventory_type": 10, "duration": 180, "teams": [0], "team_cost": 0, "sort": 2}, "summon_level_1": {"name": "summon_level_1", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 1}, "summon_level_2": {"name": "summon_level_2", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 2000, "sort": 2}, "summon_level_3": {"name": "summon_level_3", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 3000, "sort": 3}, "summon_level_4": {"name": "summon_level_4", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 4000, "sort": 4}, "summon_level_5": {"name": "summon_level_5", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 5000, "sort": 5}, "summon_level_first": {"name": "summon_level_first", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 6000, "sort": 6}, "summon_level_late": {"name": "summon_level_late", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 7000, "sort": 7}, "summon_level_paradox": {"name": "summon_level_paradox", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 7000, "sort": 8}, "summon_level_ultra": {"name": "summon_level_ultra", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 7000, "sort": 9}, "summon_legendary_normal": {"name": "summon_legendary_normal", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 0, "sort": 10}, "summon_legendary_special": {"name": "summon_legendary_special", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 0, "sort": 11}, "summon_mythical": {"name": "summon_mythical", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 0, "sort": 12}}, "type_item_list": {"2": {"items": {"pokeball": {"name": "pokeball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 1}, "greatball": {"name": "greatball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 2}, "ultraball": {"name": "ultraball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 3}, "safariball": {"name": "safariball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 4}, "masterball": {"name": "masterball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 5}, "fastball": {"name": "fastball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 6}, "levelball": {"name": "levelball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 7}, "lureball": {"name": "lureball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 8}, "loveball": {"name": "loveball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 9}, "friendball": {"name": "friendball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 10}, "moonball": {"name": "moonball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 11}, "heavyball": {"name": "heavyball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 12}, "leadenball": {"name": "leadenball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 13}, "gigatonball": {"name": "gigatonball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 14}, "sportball": {"name": "sportball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 15}, "netball": {"name": "netball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 17}, "diveball": {"name": "diveball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 18}, "nestball": {"name": "nestball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 19}, "repeatball": {"name": "repeatball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 20}, "timerball": {"name": "timerball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 21}, "luxuryball": {"name": "luxuryball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 22}, "premierball": {"name": "premierball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 23}, "duskball": {"name": "duskball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 24}, "healball": {"name": "healball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 25}, "quickball": {"name": "quickball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 26}, "cherishball": {"name": "cherishball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 27}, "dreamball": {"name": "dreamball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 28}, "beastball": {"name": "beastball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 29}, "featherball": {"name": "featherball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 30}, "wingball": {"name": "wingball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 31}, "jetball": {"name": "jetball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 32}, "strangeball": {"name": "strangeball", "cost": 100, "inventory_type": 2, "duration": 0, "teams": [0], "team_cost": 200, "sort": 33}}}, "8": {"items": {"abilitycapsule": {"name": "abilitycapsule", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 1}, "abilitypatch": {"name": "abilitypatch", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 2}, "rarecandy": {"name": "rarecandy", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 3}, "healthwing": {"name": "healthwing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 4}, "musclewing": {"name": "musclewing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 5}, "resistwing": {"name": "resistwing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 6}, "geniuswing": {"name": "geniuswing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 7}, "cleverwing": {"name": "cleverwing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 8}, "swiftwing": {"name": "swiftwing", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 9}, "bottlecap": {"name": "bottlecap", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 10}, "goldbottlecap": {"name": "goldbottlecap", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 11}, "lonelymint": {"name": "lonelymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 12}, "adamantmint": {"name": "adamantmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 13}, "naughtymint": {"name": "naughtymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 14}, "bravemint": {"name": "bravemint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 15}, "boldmint": {"name": "boldmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 16}, "impishmint": {"name": "impishmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 17}, "laxmint": {"name": "laxmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 18}, "relaxedmint": {"name": "relaxedmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 19}, "modestmint": {"name": "modestmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 20}, "mildmint": {"name": "mildmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 21}, "rashmint": {"name": "rashmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 22}, "quietmint": {"name": "quietmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 23}, "calmmint": {"name": "calmmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 24}, "gentlemint": {"name": "gentlemint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 25}, "carefulmint": {"name": "carefulmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 26}, "sassymint": {"name": "sassymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 27}, "timidmint": {"name": "timidmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 28}, "hastymint": {"name": "hastymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 29}, "jollymint": {"name": "jollymint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 30}, "naivemint": {"name": "naivemint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 31}, "seriousmint": {"name": "seriousmint", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 32}, "heartscale": {"name": "heartscale", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 33}, "normalterashard": {"name": "normalterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 34}, "fireterashard": {"name": "fireterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 35}, "waterterashard": {"name": "waterterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 36}, "electricterashard": {"name": "electricterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 37}, "grassterashard": {"name": "grassterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 38}, "iceterashard": {"name": "iceterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 39}, "fightingterashard": {"name": "fightingterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 40}, "poisonterashard": {"name": "poisonterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 41}, "groundterashard": {"name": "groundterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 42}, "flyingterashard": {"name": "flyingterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 43}, "psychicterashard": {"name": "psychicterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 44}, "bugterashard": {"name": "bugterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 45}, "rockterashard": {"name": "rockterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 46}, "ghostterashard": {"name": "ghostterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 47}, "dragonterashard": {"name": "dragonterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 48}, "darkterashard": {"name": "darkterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 49}, "steelterashard": {"name": "steelterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 50}, "fairyterashard": {"name": "fairy<PERSON>shard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 51}, "stellarterashard": {"name": "stellarterashard", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 52}, "dynamaxcandy": {"name": "dynamaxcandy", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 53}, "firestone": {"name": "firestone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 54}, "thunderstone": {"name": "thunderstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 55}, "waterstone": {"name": "waterstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 56}, "leafstone": {"name": "leafstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 57}, "moonstone": {"name": "moonstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 58}, "sunstone": {"name": "sunstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 59}, "shinystone": {"name": "shinystone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 60}, "duskstone": {"name": "duskstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 61}, "dawnstone": {"name": "dawnstone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 62}, "icestone": {"name": "icestone", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 63}, "sweetapple": {"name": "sweetapple", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 64}, "tartapple": {"name": "tartapple", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 65}, "crackedpot": {"name": "crackedpot", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 66}, "chippedpot": {"name": "chippedpot", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 67}, "galaricacuff": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 68}, "galaricawreath": {"name": "galaricawreath", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 69}, "linkingcord": {"name": "linkingcord", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 70}, "blackaugurite": {"name": "blackaugurite", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 71}, "peatblock": {"name": "peatblock", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 72}, "auspiciousarmor": {"name": "auspiciousarmor", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 73}, "maliciousarmor": {"name": "malicious<PERSON>or", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 74}, "syrupyapple": {"name": "syrupyapple", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 75}, "unremarkableteacup": {"name": "unremarkable<PERSON><PERSON>up", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 76}, "masterpieceteacup": {"name": "masterpieceteacup", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 77}, "metalalloy": {"name": "metalalloy", "cost": 0, "inventory_type": 8, "duration": 0, "teams": [0], "team_cost": 0, "sort": 78}}}, "4": {"items": {"potion": {"name": "potion", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 1}, "superpotion": {"name": "superpotion", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 2}, "hyperpotion": {"name": "hyperpotion", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 3}, "maxpotion": {"name": "maxpotion", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 4}, "fullrestore": {"name": "<PERSON><PERSON><PERSON>", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 5}, "antidote": {"name": "antidote", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 6}, "burnheal": {"name": "burnheal", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 7}, "iceheal": {"name": "iceheal", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 8}, "awakening": {"name": "awakening", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 9}, "paralyzeheal": {"name": "paralyzeheal", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 10}, "fullheal": {"name": "fullheal", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 11}, "revive": {"name": "revive", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 12}, "maxrevive": {"name": "maxrevive", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 13}, "ether": {"name": "ether", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 14}, "maxether": {"name": "maxether", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 15}, "elixir": {"name": "elixir", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 16}, "maxelixir": {"name": "maxelixir", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 17}, "sacredash": {"name": "sacredash", "cost": 200, "inventory_type": 4, "duration": 0, "teams": [0], "team_cost": 0, "sort": 18}}}, "7": {"items": {"abilityshield": {"name": "abilityshield", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 1}, "abomasite": {"name": "abomasite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 2}, "absolite": {"name": "absolite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 3}, "absorbbulb": {"name": "absorbbulb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 4}, "adamantcrystal": {"name": "adamantcrystal", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 5}, "adamantorb": {"name": "adamantorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 6}, "adrenalineorb": {"name": "adrenalineorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 7}, "aerodactylite": {"name": "aerodactylite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 8}, "aggronite": {"name": "aggronite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 9}, "aguavberry": {"name": "aguavberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 10}, "airballoon": {"name": "airballoon", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 11}, "alakazite": {"name": "alakazite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 12}, "aloraichiumz": {"name": "aloraichiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 13}, "altarianite": {"name": "altarianite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 14}, "ampharosite": {"name": "ampharosite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 15}, "apicotberry": {"name": "apicotberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 16}, "armorfossil": {"name": "armorfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 17}, "aspearberry": {"name": "as<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 18}, "assaultvest": {"name": "assaultvest", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 19}, "audinite": {"name": "audinite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 20}, "babiriberry": {"name": "babiriberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 22}, "banettite": {"name": "banettite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 23}, "beedrillite": {"name": "beedrillite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 25}, "belueberry": {"name": "belueberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 26}, "berryjuice": {"name": "berryjuice", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 27}, "berrysweet": {"name": "berrysweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 28}, "bignugget": {"name": "bignugget", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 29}, "bigroot": {"name": "bigroot", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 30}, "bindingband": {"name": "bindingband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 31}, "blackbelt": {"name": "blackbelt", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 32}, "blackglasses": {"name": "blackglasses", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 33}, "blacksludge": {"name": "blacksludge", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 34}, "blastoisinite": {"name": "blastoisinite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 35}, "blazikenite": {"name": "blazikenite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 36}, "blueorb": {"name": "blueorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 37}, "blukberry": {"name": "blukberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 38}, "blunderpolicy": {"name": "blunderpolicy", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 39}, "boosterenergy": {"name": "boosterenergy", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 40}, "brightpowder": {"name": "brightpowder", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 42}, "buggem": {"name": "buggem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 43}, "bugmemory": {"name": "bugmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 44}, "buginiumz": {"name": "buginiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 45}, "burndrive": {"name": "burndrive", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 46}, "cameruptite": {"name": "cameruptite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 47}, "cellbattery": {"name": "cellbattery", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 48}, "charcoal": {"name": "charcoal", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 49}, "charizarditex": {"name": "charizarditex", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 50}, "charizarditey": {"name": "charizarditey", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 51}, "chartiberry": {"name": "chartiberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 52}, "cheriberry": {"name": "cheriberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 53}, "chestoberry": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 55}, "chilanberry": {"name": "chilanberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 56}, "chilldrive": {"name": "chilldrive", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 57}, "choiceband": {"name": "choiceband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 59}, "choicescarf": {"name": "choicescarf", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 60}, "choicespecs": {"name": "choicespecs", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 61}, "chopleberry": {"name": "chopleberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 62}, "clawfossil": {"name": "clawfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 63}, "clearamulet": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 64}, "cloversweet": {"name": "cloversweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 65}, "cobaberry": {"name": "cobaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 66}, "colburberry": {"name": "colburberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 67}, "cornerstonemask": {"name": "cornerstonemask", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 68}, "cornnberry": {"name": "cornnberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 69}, "coverfossil": {"name": "coverfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 70}, "covertcloak": {"name": "covertcloak", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 71}, "custapberry": {"name": "custapberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 73}, "damprock": {"name": "damprock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 74}, "darkgem": {"name": "darkgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 75}, "darkmemory": {"name": "darkmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 76}, "darkiniumz": {"name": "darkiniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 77}, "decidiumz": {"name": "decidiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 79}, "deepseascale": {"name": "deepseascale", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 80}, "deepseatooth": {"name": "deepseatooth", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 81}, "destinyknot": {"name": "destinyknot", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 82}, "diancite": {"name": "diancite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 83}, "domefossil": {"name": "domefossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 85}, "dousedrive": {"name": "dousedrive", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 86}, "dracoplate": {"name": "dracoplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 87}, "dragonfang": {"name": "dragonfang", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 88}, "dragongem": {"name": "dragongem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 89}, "dragonmemory": {"name": "dragonmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 90}, "dragonscale": {"name": "dragonscale", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 91}, "dragoniumz": {"name": "dragoniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 92}, "dreadplate": {"name": "dreadplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 93}, "dubiousdisc": {"name": "dubiousdisc", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 95}, "durinberry": {"name": "durinberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 96}, "earthplate": {"name": "earthplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 99}, "eeviumz": {"name": "eeviumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 100}, "ejectbutton": {"name": "ejectbutton", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 101}, "ejectpack": {"name": "ejectpack", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 102}, "electirizer": {"name": "electirizer", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 103}, "electricgem": {"name": "electricgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 104}, "electricmemory": {"name": "electricmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 105}, "electricseed": {"name": "electricseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 106}, "electriumz": {"name": "electriumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 107}, "enigmaberry": {"name": "enigmaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 108}, "eviolite": {"name": "eviolite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 109}, "expertbelt": {"name": "expertbelt", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 110}, "fairiumz": {"name": "<PERSON><PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 111}, "fairyfeather": {"name": "fairyfeather", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 112}, "fairygem": {"name": "fairygem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 113}, "fairymemory": {"name": "fairymemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 114}, "fightinggem": {"name": "fightinggem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 116}, "fightingmemory": {"name": "fightingmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 117}, "fightiniumz": {"name": "fightiniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 118}, "figyberry": {"name": "figyberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 119}, "firegem": {"name": "firegem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 120}, "firememory": {"name": "firememory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 121}, "firiumz": {"name": "fi<PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 123}, "fistplate": {"name": "fistplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 124}, "flameorb": {"name": "flameorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 125}, "flameplate": {"name": "flameplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 126}, "floatstone": {"name": "floatstone", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 127}, "flowersweet": {"name": "flowersweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 128}, "flyinggem": {"name": "flyinggem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 129}, "flyingmemory": {"name": "flyingmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 130}, "flyiniumz": {"name": "flyiniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 131}, "focusband": {"name": "focusband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 132}, "focussash": {"name": "focussash", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 133}, "fossilizedbird": {"name": "fossilizedbird", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 134}, "fossilizeddino": {"name": "fossilizeddino", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 135}, "fossilizeddrake": {"name": "fossilizeddrake", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 136}, "fossilizedfish": {"name": "fossilizedfish", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 137}, "fullincense": {"name": "fullincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 139}, "galladite": {"name": "galladite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 142}, "ganlonberry": {"name": "gan<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 143}, "garchompite": {"name": "garchompite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 144}, "gardevoirite": {"name": "gardevoirite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 145}, "gengarite": {"name": "gengarite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 146}, "ghostgem": {"name": "ghostgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 147}, "ghostmemory": {"name": "ghostmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 148}, "ghostiumz": {"name": "ghostiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 149}, "glalitite": {"name": "glalitite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 150}, "grassgem": {"name": "grassgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 152}, "grassmemory": {"name": "grassmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 153}, "grassiumz": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 154}, "grassyseed": {"name": "grassyseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 155}, "grepaberry": {"name": "grepaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 157}, "gripclaw": {"name": "grip<PERSON>law", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 158}, "griseouscore": {"name": "griseouscore", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 159}, "griseousorb": {"name": "griseousorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 160}, "groundgem": {"name": "groundgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 161}, "groundmemory": {"name": "groundmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 162}, "groundiumz": {"name": "groundiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 163}, "gyaradosite": {"name": "gyaradosite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 164}, "habanberry": {"name": "habanberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 165}, "hardstone": {"name": "hardstone", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 166}, "hearthflamemask": {"name": "hearthflamemask", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 168}, "heatrock": {"name": "heatrock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 169}, "heavydutyboots": {"name": "heavydutyboots", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 171}, "helixfossil": {"name": "helixfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 172}, "heracronite": {"name": "heracronite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 173}, "hondewberry": {"name": "hondewberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 174}, "houndoominite": {"name": "houndoominite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 175}, "iapapaberry": {"name": "iapapaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 176}, "icegem": {"name": "icegem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 177}, "icememory": {"name": "icememory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 178}, "icicleplate": {"name": "icicleplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 180}, "iciumz": {"name": "ici<PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 181}, "icyrock": {"name": "icyrock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 182}, "inciniumz": {"name": "inciniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 183}, "insectplate": {"name": "insectplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 184}, "ironball": {"name": "ironball", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 185}, "ironplate": {"name": "ironplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 186}, "jabocaberry": {"name": "jabocaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 187}, "jawfossil": {"name": "jawfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 188}, "kasibberry": {"name": "kasibberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 189}, "kebiaberry": {"name": "kebiaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 190}, "keeberry": {"name": "keeberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 191}, "kelpsyberry": {"name": "kelpsyberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 192}, "kangaskhanite": {"name": "kangaskhanite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 193}, "kingsrock": {"name": "kingsrock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 194}, "kommoniumz": {"name": "kommoniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 195}, "laggingtail": {"name": "laggingtail", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 196}, "lansatberry": {"name": "lansatberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 197}, "latiasite": {"name": "latiasite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 198}, "latiosite": {"name": "latiosite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 199}, "laxincense": {"name": "laxincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 200}, "leek": {"name": "leek", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 202}, "leftovers": {"name": "leftovers", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 203}, "leppaberry": {"name": "le<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 204}, "liechiberry": {"name": "lie<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 206}, "lifeorb": {"name": "lifeorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 207}, "lightball": {"name": "lightball", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 208}, "lightclay": {"name": "lightclay", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 209}, "loadeddice": {"name": "loadeddice", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 210}, "lopunnite": {"name": "lopunnite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 211}, "lovesweet": {"name": "lovesweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 213}, "lucarionite": {"name": "lucarionite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 214}, "luckypunch": {"name": "luckypunch", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 215}, "lumberry": {"name": "lumberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 216}, "luminousmoss": {"name": "luminousmoss", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 217}, "lunaliumz": {"name": "lunaliumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 218}, "lustrousglobe": {"name": "lustrousglobe", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 220}, "lustrousorb": {"name": "lustrousorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 221}, "lycaniumz": {"name": "lycaniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 223}, "machobrace": {"name": "machobrace", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 224}, "magmarizer": {"name": "magmarizer", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 225}, "magnet": {"name": "magnet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 226}, "magoberry": {"name": "magoberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 227}, "magostberry": {"name": "magostberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 228}, "mail": {"name": "mail", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 229}, "manectite": {"name": "manectite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 231}, "marangaberry": {"name": "marangaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 232}, "marshadiumz": {"name": "marshadiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 233}, "mawilite": {"name": "mawi<PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 236}, "meadowplate": {"name": "meadowplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 237}, "medichamite": {"name": "medichamite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 238}, "mentalherb": {"name": "mentalherb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 239}, "metagrossite": {"name": "metagrossite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 240}, "metalcoat": {"name": "metalcoat", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 242}, "metalpowder": {"name": "metalpowder", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 243}, "metronome": {"name": "metronome", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 244}, "mewniumz": {"name": "mewn<PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 245}, "mewtwonitex": {"name": "mewtwonitex", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 246}, "mewtwonitey": {"name": "mewtwonitey", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 247}, "micleberry": {"name": "micleberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 248}, "mimikiumz": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 249}, "mindplate": {"name": "mindplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 250}, "miracleseed": {"name": "miracleseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 251}, "mirrorherb": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 252}, "mistyseed": {"name": "mistyseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 253}, "muscleband": {"name": "muscleband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 256}, "mysticwater": {"name": "mysticwater", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 257}, "nanabberry": {"name": "na<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 258}, "nevermeltice": {"name": "nevermeltice", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 261}, "nomelberry": {"name": "nomelberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 262}, "normalgem": {"name": "normalgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 263}, "normaliumz": {"name": "normaliumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 264}, "occaberry": {"name": "occaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 265}, "oddincense": {"name": "oddincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 266}, "oldamber": {"name": "oldamber", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 267}, "oranberry": {"name": "oranberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 268}, "ovalstone": {"name": "ovalstone", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 269}, "pamtreberry": {"name": "pam<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 270}, "parkball": {"name": "parkball", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 271}, "passhoberry": {"name": "pass<PERSON>berry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 272}, "payapaberry": {"name": "payapaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 273}, "pechaberry": {"name": "pechaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 274}, "persimberry": {"name": "persimberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 275}, "petayaberry": {"name": "petayaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 276}, "pidgeotite": {"name": "pidgeotite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 277}, "pikaniumz": {"name": "pikaniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 278}, "pikashuniumz": {"name": "pikashuniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 279}, "pinapberry": {"name": "pin<PERSON>berry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 280}, "pinsirite": {"name": "pinsirite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 281}, "pixieplate": {"name": "pixie<PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 282}, "plumefossil": {"name": "plumefossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 283}, "poisonbarb": {"name": "poisonbarb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 284}, "poisongem": {"name": "poisongem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 285}, "poisonmemory": {"name": "poisonmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 286}, "poisoniumz": {"name": "poisoniumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 287}, "pomegberry": {"name": "pomegberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 289}, "poweranklet": {"name": "poweranklet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 290}, "powerband": {"name": "powerband", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 291}, "powerbelt": {"name": "powerbelt", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 292}, "powerbracer": {"name": "powerbracer", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 293}, "powerherb": {"name": "powerherb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 294}, "powerlens": {"name": "powerlens", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 295}, "powerweight": {"name": "powerweight", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 296}, "primariumz": {"name": "primariumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 298}, "prismscale": {"name": "prismscale", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 299}, "protectivepads": {"name": "protectivepads", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 300}, "protector": {"name": "protector", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 301}, "psychicgem": {"name": "psychicgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 302}, "psychicmemory": {"name": "psychicmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 303}, "psychicseed": {"name": "psychicseed", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 304}, "psychiumz": {"name": "psychiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 305}, "punchingglove": {"name": "punchingglove", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 306}, "qualotberry": {"name": "qualotberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 307}, "quickclaw": {"name": "quickclaw", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 309}, "quickpowder": {"name": "quickpowder", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 310}, "rabutaberry": {"name": "rabutaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 311}, "rarebone": {"name": "rarebone", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 312}, "rawstberry": {"name": "rawstberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 313}, "razorclaw": {"name": "razorclaw", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 314}, "razorfang": {"name": "razorfang", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 315}, "razzberry": {"name": "razzberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 316}, "reapercloth": {"name": "reapercloth", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 317}, "redcard": {"name": "redcard", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 318}, "redorb": {"name": "redorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 319}, "ribbonsweet": {"name": "ribbonsweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 321}, "rindoberry": {"name": "rindoberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 322}, "ringtarget": {"name": "ringtarget", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 323}, "rockgem": {"name": "rockgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 324}, "rockincense": {"name": "rockincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 325}, "rockmemory": {"name": "rockmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 326}, "rockiumz": {"name": "rockiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 327}, "rockyhelmet": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 328}, "roomservice": {"name": "roomservice", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 329}, "rootfossil": {"name": "rootfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 330}, "roseincense": {"name": "roseincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 331}, "roseliberry": {"name": "roseliberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 332}, "rowapberry": {"name": "row<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 333}, "rustedshield": {"name": "rustedshield", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 334}, "rustedsword": {"name": "rustedsword", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 335}, "sablenite": {"name": "sablenite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 336}, "sachet": {"name": "sachet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 337}, "safetygoggles": {"name": "safetygoggles", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 339}, "sailfossil": {"name": "sailfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 340}, "salacberry": {"name": "salacberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 341}, "salamencite": {"name": "salamencite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 342}, "sceptilite": {"name": "sceptilite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 343}, "scizorite": {"name": "scizorite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 344}, "scopelens": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 345}, "seaincense": {"name": "seaincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 346}, "sharpbeak": {"name": "sharpbeak", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 347}, "sharpedonite": {"name": "sharpedonite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 348}, "shedshell": {"name": "shedshell", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 349}, "shellbell": {"name": "shellbell", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 350}, "shockdrive": {"name": "shockdrive", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 352}, "shucaberry": {"name": "shucaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 353}, "silkscarf": {"name": "silkscarf", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 354}, "silverpowder": {"name": "silverpowder", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 355}, "sitrusberry": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 356}, "skullfossil": {"name": "skullfossil", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 357}, "skyplate": {"name": "skyplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 358}, "slowbronite": {"name": "slowbronite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 359}, "smoothrock": {"name": "smoothrock", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 360}, "snorliumz": {"name": "snor<PERSON>z", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 361}, "snowball": {"name": "snowball", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 362}, "softsand": {"name": "softsand", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 363}, "solganiumz": {"name": "solganiumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 364}, "souldew": {"name": "souldew", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 365}, "spelltag": {"name": "spelltag", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 366}, "spelonberry": {"name": "spelonberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 367}, "splashplate": {"name": "splashplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 368}, "spookyplate": {"name": "spookyplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 369}, "starfberry": {"name": "starfberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 371}, "starsweet": {"name": "starsweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 372}, "steelixite": {"name": "steelixite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 373}, "steelgem": {"name": "steelgem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 374}, "steelmemory": {"name": "steelmemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 375}, "steeliumz": {"name": "steeliumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 376}, "stick": {"name": "stick", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 377}, "stickybarb": {"name": "stickybarb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 378}, "stoneplate": {"name": "stoneplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 379}, "strawberrysweet": {"name": "strawberrysweet", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 381}, "swampertite": {"name": "swampertite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 383}, "tamatoberry": {"name": "tamatoberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 386}, "tangaberry": {"name": "tangaberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 387}, "tapuniumz": {"name": "tap<PERSON>umz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 388}, "terrainextender": {"name": "terrainextender", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 390}, "thickclub": {"name": "thickclub", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 391}, "throatspray": {"name": "throatspray", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 392}, "toxicorb": {"name": "toxicorb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 395}, "toxicplate": {"name": "toxicplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 396}, "tr00": {"name": "tr00", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 397}, "tr01": {"name": "tr01", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 398}, "tr02": {"name": "tr02", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 399}, "tr03": {"name": "tr03", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 400}, "tr04": {"name": "tr04", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 401}, "tr05": {"name": "tr05", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 402}, "tr06": {"name": "tr06", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 403}, "tr07": {"name": "tr07", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 404}, "tr08": {"name": "tr08", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 405}, "tr09": {"name": "tr09", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 406}, "tr10": {"name": "tr10", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 407}, "tr11": {"name": "tr11", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 408}, "tr12": {"name": "tr12", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 409}, "tr13": {"name": "tr13", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 410}, "tr14": {"name": "tr14", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 411}, "tr15": {"name": "tr15", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 412}, "tr16": {"name": "tr16", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 413}, "tr17": {"name": "tr17", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 414}, "tr18": {"name": "tr18", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 415}, "tr19": {"name": "tr19", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 416}, "tr20": {"name": "tr20", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 417}, "tr21": {"name": "tr21", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 418}, "tr22": {"name": "tr22", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 419}, "tr23": {"name": "tr23", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 420}, "tr24": {"name": "tr24", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 421}, "tr25": {"name": "tr25", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 422}, "tr26": {"name": "tr26", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 423}, "tr27": {"name": "tr27", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 424}, "tr28": {"name": "tr28", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 425}, "tr29": {"name": "tr29", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 426}, "tr30": {"name": "tr30", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 427}, "tr31": {"name": "tr31", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 428}, "tr32": {"name": "tr32", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 429}, "tr33": {"name": "tr33", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 430}, "tr34": {"name": "tr34", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 431}, "tr35": {"name": "tr35", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 432}, "tr36": {"name": "tr36", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 433}, "tr37": {"name": "tr37", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 434}, "tr38": {"name": "tr38", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 435}, "tr39": {"name": "tr39", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 436}, "tr40": {"name": "tr40", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 437}, "tr41": {"name": "tr41", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 438}, "tr42": {"name": "tr42", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 439}, "tr43": {"name": "tr43", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 440}, "tr44": {"name": "tr44", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 441}, "tr45": {"name": "tr45", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 442}, "tr46": {"name": "tr46", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 443}, "tr47": {"name": "tr47", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 444}, "tr48": {"name": "tr48", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 445}, "tr49": {"name": "tr49", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 446}, "tr50": {"name": "tr50", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 447}, "tr51": {"name": "tr51", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 448}, "tr52": {"name": "tr52", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 449}, "tr53": {"name": "tr53", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 450}, "tr54": {"name": "tr54", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 451}, "tr55": {"name": "tr55", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 452}, "tr56": {"name": "tr56", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 453}, "tr57": {"name": "tr57", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 454}, "tr58": {"name": "tr58", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 455}, "tr59": {"name": "tr59", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 456}, "tr60": {"name": "tr60", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 457}, "tr61": {"name": "tr61", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 458}, "tr62": {"name": "tr62", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 459}, "tr63": {"name": "tr63", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 460}, "tr64": {"name": "tr64", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 461}, "tr65": {"name": "tr65", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 462}, "tr66": {"name": "tr66", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 463}, "tr67": {"name": "tr67", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 464}, "tr68": {"name": "tr68", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 465}, "tr69": {"name": "tr69", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 466}, "tr70": {"name": "tr70", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 467}, "tr71": {"name": "tr71", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 468}, "tr72": {"name": "tr72", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 469}, "tr73": {"name": "tr73", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 470}, "tr74": {"name": "tr74", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 471}, "tr75": {"name": "tr75", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 472}, "tr76": {"name": "tr76", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 473}, "tr77": {"name": "tr77", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 474}, "tr78": {"name": "tr78", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 475}, "tr79": {"name": "tr79", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 476}, "tr80": {"name": "tr80", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 477}, "tr81": {"name": "tr81", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 478}, "tr82": {"name": "tr82", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 479}, "tr83": {"name": "tr83", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 480}, "tr84": {"name": "tr84", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 481}, "tr85": {"name": "tr85", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 482}, "tr86": {"name": "tr86", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 483}, "tr87": {"name": "tr87", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 484}, "tr88": {"name": "tr88", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 485}, "tr89": {"name": "tr89", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 486}, "tr90": {"name": "tr90", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 487}, "tr91": {"name": "tr91", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 488}, "tr92": {"name": "tr92", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 489}, "tr93": {"name": "tr93", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 490}, "tr94": {"name": "tr94", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 491}, "tr95": {"name": "tr95", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 492}, "tr96": {"name": "tr96", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 493}, "tr97": {"name": "tr97", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 494}, "tr98": {"name": "tr98", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 495}, "tr99": {"name": "tr99", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 496}, "twistedspoon": {"name": "twistedspoon", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 497}, "tyranitarite": {"name": "tyranitarite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 498}, "ultranecroziumz": {"name": "ultranecroziumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 500}, "upgrade": {"name": "upgrade", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 502}, "utilityumbrella": {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 503}, "venusaurite": {"name": "venusaurite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 504}, "wacanberry": {"name": "wacanberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 505}, "watergem": {"name": "watergem", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 506}, "watermemory": {"name": "watermemory", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 507}, "wateriumz": {"name": "wateriumz", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 509}, "watmelberry": {"name": "watmelberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 510}, "waveincense": {"name": "waveincense", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 511}, "weaknesspolicy": {"name": "weaknesspolicy", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 512}, "wellspringmask": {"name": "wellspringmask", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 513}, "wepearberry": {"name": "<PERSON><PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 514}, "whippeddream": {"name": "whippeddream", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 515}, "whiteherb": {"name": "whiteherb", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 516}, "widelens": {"name": "widelens", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 517}, "wikiberry": {"name": "wikiberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 518}, "wiseglasses": {"name": "wiseglasses", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 519}, "yacheberry": {"name": "yacheberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 520}, "zapplate": {"name": "zapplate", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 521}, "zoomlens": {"name": "zoomlens", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 522}, "berserkgene": {"name": "berserkgene", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 523}, "berry": {"name": "berry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 524}, "bitterberry": {"name": "bitterberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 525}, "burntberry": {"name": "burntberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 526}, "goldberry": {"name": "goldberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 527}, "iceberry": {"name": "iceberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 528}, "mintberry": {"name": "mintberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 529}, "miracleberry": {"name": "miracleberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 530}, "mysteryberry": {"name": "mysteryberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 531}, "pinkbow": {"name": "pinkbow", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 532}, "polkadotbow": {"name": "polkadotbow", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 533}, "przcureberry": {"name": "prz<PERSON><PERSON>", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 534}, "psncureberry": {"name": "psncureberry", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 535}, "crucibellite": {"name": "crucibellite", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 536}, "vilevial": {"name": "vilevial", "cost": 0, "inventory_type": 7, "duration": 0, "teams": [0], "team_cost": 0, "sort": 537}}}, "10": {"items": {"mewtwoquestslate": {"name": "mewtwoquestslate", "cost": 0, "inventory_type": 10, "duration": 180, "teams": [0], "team_cost": 0, "sort": 1}, "mewtwomail_special": {"name": "mewtwomail_special", "cost": 0, "inventory_type": 10, "duration": 180, "teams": [0], "team_cost": 0, "sort": 2}}}, "11": {"items": {"summon_level_1": {"name": "summon_level_1", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 1000, "sort": 1}, "summon_level_2": {"name": "summon_level_2", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 2000, "sort": 2}, "summon_level_3": {"name": "summon_level_3", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 3000, "sort": 3}, "summon_level_4": {"name": "summon_level_4", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 4000, "sort": 4}, "summon_level_5": {"name": "summon_level_5", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 5000, "sort": 5}, "summon_level_first": {"name": "summon_level_first", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 6000, "sort": 6}, "summon_level_late": {"name": "summon_level_late", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 7000, "sort": 7}, "summon_level_paradox": {"name": "summon_level_paradox", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 7000, "sort": 8}, "summon_level_ultra": {"name": "summon_level_ultra", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 7000, "sort": 9}, "summon_legendary_normal": {"name": "summon_legendary_normal", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 0, "sort": 10}, "summon_legendary_special": {"name": "summon_legendary_special", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 0, "sort": 11}, "summon_mythical": {"name": "summon_mythical", "cost": 0, "inventory_type": 11, "duration": 0, "teams": [0], "team_cost": 0, "sort": 12}}}}}
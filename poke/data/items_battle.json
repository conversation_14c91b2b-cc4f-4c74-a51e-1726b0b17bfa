{"abilityshield": {"name": "Ability Shield", "spritenum": 746, "fling": {"basePower": 30}, "ignoreKlutz": true, "num": 1881, "gen": 9, "desc": "Holder's Ability cannot be changed by any effect.", "shortDesc": "Holder's Ability cannot be changed by any effect."}, "abomasite": {"name": "Abomasite", "spritenum": 575, "megaStone": "Abomasnow-Mega", "megaEvolves": "Abomasnow", "itemUser": ["Abomasnow"], "num": 674, "gen": 6, "isNonstandard": "Past", "desc": "If held by an Abomasnow, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by an Abomasnow, this item allows it to Mega Evolve in battle."}, "absolite": {"name": "Absolite", "spritenum": 576, "megaStone": "Absol-Mega", "megaEvolves": "Absol", "itemUser": ["Absol"], "num": 677, "gen": 6, "isNonstandard": "Past", "desc": "If held by an Absol, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by an Absol, this item allows it to Mega Evolve in battle."}, "absorbbulb": {"name": "Absorb Bulb", "spritenum": 2, "fling": {"basePower": 30}, "boosts": {"spa": 1}, "num": 545, "gen": 5, "desc": "Raises holder's Sp. Atk by 1 stage if hit by a Water-type attack. Single use.", "shortDesc": "Raises holder's Sp. Atk by 1 stage if hit by a Water-type attack. Single use."}, "adamantcrystal": {"name": "Adamant Crystal", "spritenum": 741, "onBasePowerPriority": 15, "forcedForme": "Dialga-Origin", "itemUser": ["Dialga-Origin"], "num": 1777, "gen": 8, "desc": "If held by a Dialga, its Steel- and Dragon-type attacks have 1.2x power.", "shortDesc": "If held by a Dialga, its Steel- and Dragon-type attacks have 1.2x power."}, "adamantorb": {"name": "<PERSON><PERSON>", "spritenum": 4, "fling": {"basePower": 60}, "onBasePowerPriority": 15, "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 135, "gen": 4, "desc": "If held by a Dialga, its Steel- and Dragon-type attacks have 1.2x power.", "shortDesc": "If held by a Dialga, its Steel- and Dragon-type attacks have 1.2x power."}, "adrenalineorb": {"name": "Adrenaline Orb", "spritenum": 660, "fling": {"basePower": 30}, "boosts": {"spe": 1}, "num": 846, "gen": 7, "desc": "Raises holder's Speed by 1 stage if it gets affected by Intimidate. Single use.", "shortDesc": "Raises holder's Speed by 1 stage if it gets affected by Intimidate. Single use."}, "aerodactylite": {"name": "Aerodactylite", "spritenum": 577, "megaStone": "Aerodactyl-Mega", "megaEvolves": "Aerodactyl", "itemUser": ["Aerodactyl"], "num": 672, "gen": 6, "isNonstandard": "Past", "desc": "If held by an Aerodactyl, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by an Aerodactyl, this item allows it to Mega Evolve in battle."}, "aggronite": {"name": "Aggronite", "spritenum": 578, "megaStone": "Aggron-Mega", "megaEvolves": "Aggron", "itemUser": ["Aggron"], "num": 667, "gen": 6, "isNonstandard": "Past", "desc": "If held by an Aggron, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by an Aggron, this item allows it to Mega Evolve in battle."}, "aguavberry": {"name": "A<PERSON>v <PERSON>", "spritenum": 5, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Dragon"}, "num": 162, "gen": 3, "desc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -SpD Nature. Single use.", "shortDesc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -SpD Nature. Single use."}, "airballoon": {"name": "Air Balloon", "spritenum": 6, "fling": {"basePower": 10}, "num": 541, "gen": 5, "desc": "Holder is immune to Ground-type attacks. Pops when holder is hit.", "shortDesc": "Holder is immune to Ground-type attacks. Pops when holder is hit."}, "alakazite": {"name": "Alakazite", "spritenum": 579, "megaStone": "Alakazam-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 679, "gen": 6, "isNonstandard": "Past", "desc": "If held by an Alakazam, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by an Alakazam, this item allows it to Mega Evolve in battle."}, "aloraichiumz": {"name": "Aloraichium Z", "spritenum": 655, "onTakeItem": false, "zMove": "<PERSON>d <PERSON>", "zMoveFrom": "Thunderbolt", "itemUser": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "num": 803, "gen": 7, "isNonstandard": "Past", "desc": "If held by an Alolan <PERSON>chu with Thunderbolt, it can use Stoked Sparksurfer.", "shortDesc": "If held by an Alolan <PERSON>chu with Thunderbolt, it can use Stoked Sparksurfer."}, "altarianite": {"name": "Altarianite", "spritenum": 615, "megaStone": "Altaria-Mega", "megaEvolves": "Altaria", "itemUser": ["Altaria"], "num": 755, "gen": 6, "isNonstandard": "Past", "desc": "If held by an Altaria, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by an Altaria, this item allows it to Mega Evolve in battle."}, "ampharosite": {"name": "Ampharosite", "spritenum": 580, "megaStone": "Ampharos-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 658, "gen": 6, "isNonstandard": "Past", "desc": "If held by an Ampha<PERSON>, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by an Ampha<PERSON>, this item allows it to Mega Evolve in battle."}, "apicotberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 10, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Ground"}, "num": 205, "gen": 3, "desc": "Raises holder's Sp. Def by 1 stage when at 1/4 max HP or less. Single use.", "shortDesc": "Raises holder's Sp. Def by 1 stage when at 1/4 max HP or less. Single use."}, "armorfossil": {"name": "<PERSON><PERSON>", "spritenum": 12, "fling": {"basePower": 100}, "num": 104, "gen": 4, "isNonstandard": "Past", "desc": "Can be revived into Shieldon.", "shortDesc": "Can be revived into Shieldon."}, "aspearberry": {"name": "<PERSON><PERSON>", "spritenum": 13, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Ice"}, "num": 153, "gen": 3, "desc": "Holder is cured if it is frozen. Single use.", "shortDesc": "Holder is cured if it is frozen. Single use."}, "assaultvest": {"name": "Assault Vest", "spritenum": 581, "fling": {"basePower": 80}, "onModifySpDPriority": 1, "num": 640, "gen": 6, "desc": "Holder's Sp. Def is 1.5x, but it can only select damaging moves.", "shortDesc": "Holder's Sp. Def is 1.5x, but it can only select damaging moves."}, "audinite": {"name": "Audinite", "spritenum": 617, "megaStone": "Audino-Mega", "megaEvolves": "Audino", "itemUser": ["Audino"], "num": 757, "gen": 6, "isNonstandard": "Past", "desc": "If held by an Audino, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by an Audino, this item allows it to Mega Evolve in battle."}, "auspiciousarmor": {"name": "Auspicious Armor", "spritenum": 753, "fling": {"basePower": 30}, "num": 2344, "gen": 9, "desc": "Evolves Charcadet into Armarouge when used.", "shortDesc": "Evolves Charcadet into Armarouge when used."}, "babiriberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 17, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Steel"}, "num": 199, "gen": 4, "desc": "Halves damage taken from a supereffective Steel-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Steel-type attack. Single use."}, "banettite": {"name": "Banettite", "spritenum": 582, "megaStone": "Banette-Mega", "megaEvolves": "<PERSON><PERSON>", "itemUser": ["<PERSON><PERSON>"], "num": 668, "gen": 6, "isNonstandard": "Past", "desc": "If held by a <PERSON><PERSON>, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a <PERSON><PERSON>, this item allows it to Mega Evolve in battle."}, "beastball": {"name": "Beast Ball", "spritenum": 661, "num": 851, "gen": 7, "isPokeball": true, "desc": "A special Poke Ball designed to catch Ultra Beasts.", "shortDesc": "A special Poke Ball designed to catch Ultra Beasts."}, "beedrillite": {"name": "Beedrillite", "spritenum": 628, "megaStone": "Beedrill-Mega", "megaEvolves": "Beedrill", "itemUser": ["Beedrill"], "num": 770, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Beedrill, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Beedrill, this item allows it to Mega Evolve in battle."}, "belueberry": {"name": "<PERSON><PERSON>", "spritenum": 21, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Electric"}, "onEat": false, "num": 183, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "berryjuice": {"name": "<PERSON> Ju<PERSON>", "spritenum": 22, "fling": {"basePower": 30}, "num": 43, "gen": 2, "isNonstandard": "Past", "desc": "Restores 20 HP when at 1/2 max HP or less. Single use.", "shortDesc": "Restores 20 HP when at 1/2 max HP or less. Single use."}, "berrysweet": {"name": "<PERSON>", "spritenum": 706, "fling": {"basePower": 10}, "num": 1111, "gen": 8, "desc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around.", "shortDesc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around."}, "bignugget": {"name": "Big Nugget", "spritenum": 27, "fling": {"basePower": 130}, "num": 581, "gen": 5, "desc": "A big nugget of pure gold that gives off a lustrous gleam.", "shortDesc": "A big nugget of pure gold that gives off a lustrous gleam."}, "bigroot": {"name": "Big Root", "spritenum": 29, "fling": {"basePower": 10}, "onTryHealPriority": 1, "num": 296, "gen": 4, "desc": "Holder gains 1.3x HP from draining/Aqua Ring/Ingrain/Leech Seed/Strength Sap.", "shortDesc": "Holder gains 1.3x HP from draining/Aqua Ring/Ingrain/Leech Seed/Strength Sap."}, "bindingband": {"name": "Binding Band", "spritenum": 31, "fling": {"basePower": 30}, "num": 544, "gen": 5, "desc": "Holder's partial-trapping moves deal 1/6 max HP per turn instead of 1/8.", "shortDesc": "Holder's partial-trapping moves deal 1/6 max HP per turn instead of 1/8."}, "blackbelt": {"name": "Black Belt", "spritenum": 32, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 241, "gen": 2, "desc": "Holder's Fighting-type attacks have 1.2x power.", "shortDesc": "Holder's Fighting-type attacks have 1.2x power."}, "blackglasses": {"name": "Black Glasses", "spritenum": 35, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 240, "gen": 2, "desc": "Holder's Dark-type attacks have 1.2x power.", "shortDesc": "Holder's Dark-type attacks have 1.2x power."}, "blacksludge": {"name": "Black Sludge", "spritenum": 34, "fling": {"basePower": 30}, "onResidualOrder": 5, "onResidualSubOrder": 4, "num": 281, "gen": 4, "desc": "Each turn, if holder is a Poison type, restores 1/16 max HP loses 1/8 if not.", "shortDesc": "Each turn, if holder is a Poison type, restores 1/16 max HP loses 1/8 if not."}, "blastoisinite": {"name": "Blastoisinite", "spritenum": 583, "megaStone": "Blastoise-Mega", "megaEvolves": "Blastoise", "itemUser": ["Blastoise"], "num": 661, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Blastoise, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Blastoise, this item allows it to Mega Evolve in battle."}, "blazikenite": {"name": "Blazikenite", "spritenum": 584, "megaStone": "Blaziken-Mega", "megaEvolves": "Blaziken", "itemUser": ["Blaziken"], "num": 664, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Blaziken, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Blaziken, this item allows it to Mega Evolve in battle."}, "blueorb": {"name": "Blue Orb", "spritenum": 41, "itemUser": ["Kyogre"], "num": 535, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Kyogre, this item triggers its Primal Reversion in battle.", "shortDesc": "If held by a Kyogre, this item triggers its Primal Reversion in battle."}, "blukberry": {"name": "<PERSON><PERSON>", "spritenum": 44, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Fire"}, "onEat": false, "num": 165, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "blunderpolicy": {"name": "Blunder Policy", "spritenum": 716, "fling": {"basePower": 80}, "num": 1121, "gen": 8, "desc": "If the holder misses due to accuracy, its Speed is raised by 2 stages. Single use.", "shortDesc": "If the holder misses due to accuracy, its Speed is raised by 2 stages. Single use."}, "boosterenergy": {"name": "Booster Energy", "spritenum": 745, "fling": {"basePower": 30}, "num": 1880, "gen": 9, "desc": "Activates the Protosynthesis or Quark Drive Abilities. Single use.", "shortDesc": "Activates the Protosynthesis or Quark Drive Abilities. Single use."}, "bottlecap": {"name": "Bottle Cap", "spritenum": 696, "fling": {"basePower": 30}, "num": 795, "gen": 7, "desc": "Used for Hyper Training. One of a Pokemon's stats is calculated with an IV of 31.", "shortDesc": "Used for Hyper Training. One of a Pokemon's stats is calculated with an IV of 31."}, "brightpowder": {"name": "<PERSON>", "spritenum": 51, "fling": {"basePower": 10}, "onModifyAccuracyPriority": -2, "num": 213, "gen": 2, "desc": "The accuracy of attacks against the holder is 0.9x.", "shortDesc": "The accuracy of attacks against the holder is 0.9x."}, "buggem": {"name": "<PERSON><PERSON>em", "spritenum": 53, "isGem": true, "num": 558, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Bug-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Bug-type attack will have 1.3x power. Single use."}, "bugmemory": {"name": "Bug Memory", "spritenum": 673, "onMemory": "Bug", "forcedForme": "<PERSON><PERSON>-<PERSON><PERSON>", "itemUser": ["<PERSON><PERSON>-<PERSON><PERSON>"], "num": 909, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Bug type.", "shortDesc": "Holder's Multi-Attack is Bug type."}, "buginiumz": {"name": "Buginium Z", "spritenum": 642, "onPlate": "Bug", "onTakeItem": false, "zMove": true, "zMoveType": "Bug", "forcedForme": "Arceus-<PERSON>ug", "num": 787, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Bug move, this item allows it to use a Bug Z-Move.", "shortDesc": "If holder has a Bug move, this item allows it to use a Bug Z-Move."}, "burndrive": {"name": "Burn Drive", "spritenum": 54, "onDrive": "Fire", "forcedForme": "Genesect-Burn", "itemUser": ["Genesect-Burn"], "num": 118, "gen": 5, "isNonstandard": "Past", "desc": "Holder's Techno Blast is Fire type.", "shortDesc": "Holder's Techno Blast is Fire type."}, "cameruptite": {"name": "Cameruptite", "spritenum": 625, "megaStone": "Camerupt-Mega", "megaEvolves": "Camerupt", "itemUser": ["Camerupt"], "num": 767, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Camerupt, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Camerupt, this item allows it to Mega Evolve in battle."}, "cellbattery": {"name": "Cell Battery", "spritenum": 60, "fling": {"basePower": 30}, "boosts": {"atk": 1}, "num": 546, "gen": 5, "desc": "Raises holder's Attack by 1 if hit by an Electric-type attack. Single use.", "shortDesc": "Raises holder's Attack by 1 if hit by an Electric-type attack. Single use."}, "charcoal": {"name": "Charc<PERSON>l", "spritenum": 61, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 249, "gen": 2, "desc": "Holder's Fire-type attacks have 1.2x power.", "shortDesc": "Holder's Fire-type attacks have 1.2x power."}, "charizarditex": {"name": "Charizardite X", "spritenum": 585, "megaStone": "Charizard-Mega-X", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 660, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Charizard, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Charizard, this item allows it to Mega Evolve in battle."}, "charizarditey": {"name": "Charizardite Y", "spritenum": 586, "megaStone": "Charizard-Mega-Y", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 678, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Charizard, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Charizard, this item allows it to Mega Evolve in battle."}, "chartiberry": {"name": "<PERSON><PERSON>", "spritenum": 62, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Rock"}, "num": 195, "gen": 4, "desc": "Halves damage taken from a supereffective Rock-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Rock-type attack. Single use."}, "cheriberry": {"name": "<PERSON><PERSON>", "spritenum": 63, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Fire"}, "num": 149, "gen": 3, "desc": "Holder cures itself if it is paralyzed. Single use.", "shortDesc": "Holder cures itself if it is paralyzed. Single use."}, "cherishball": {"name": "Cherish Ball", "spritenum": 64, "num": 16, "gen": 4, "isPokeball": true, "isNonstandard": "Unobtainable", "desc": "A rare Poke Ball that has been crafted to commemorate an occasion.", "shortDesc": "A rare Poke Ball that has been crafted to commemorate an occasion."}, "chestoberry": {"name": "Chest<PERSON> Berry", "spritenum": 65, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Water"}, "num": 150, "gen": 3, "desc": "Holder wakes up if it is asleep. Single use.", "shortDesc": "Holder wakes up if it is asleep. Single use."}, "chilanberry": {"name": "<PERSON><PERSON>", "spritenum": 66, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Normal"}, "num": 200, "gen": 4, "desc": "Halves damage taken from a Normal-type attack. Single use.", "shortDesc": "Halves damage taken from a Normal-type attack. Single use."}, "chilldrive": {"name": "Chill Drive", "spritenum": 67, "onDrive": "Ice", "forcedForme": "Genesect-Chill", "itemUser": ["Genesect-Chill"], "num": 119, "gen": 5, "isNonstandard": "Past", "desc": "Holder's Techno Blast is Ice type.", "shortDesc": "Holder's Techno Blast is Ice type."}, "chippedpot": {"name": "Chipped Pot", "spritenum": 720, "fling": {"basePower": 80}, "num": 1254, "gen": 8, "desc": "Evolves Sinistea-Antique into Polteageist-Antique when used.", "shortDesc": "Evolves Sinistea-Antique into Polteageist-Antique when used."}, "choiceband": {"name": "Choice Band", "spritenum": 68, "fling": {"basePower": 10}, "onModifyAtkPriority": 1, "isChoice": true, "num": 220, "gen": 3, "desc": "Holder's Attack is 1.5x, but it can only select the first move it executes.", "shortDesc": "Holder's Attack is 1.5x, but it can only select the first move it executes."}, "choicescarf": {"name": "Choice Scarf", "spritenum": 69, "fling": {"basePower": 10}, "isChoice": true, "num": 287, "gen": 4, "desc": "Holder's Speed is 1.5x, but it can only select the first move it executes.", "shortDesc": "Holder's Speed is 1.5x, but it can only select the first move it executes."}, "choicespecs": {"name": "Choice Specs", "spritenum": 70, "fling": {"basePower": 10}, "onModifySpAPriority": 1, "isChoice": true, "num": 297, "gen": 4, "desc": "Holder's Sp. Atk is 1.5x, but it can only select the first move it executes.", "shortDesc": "Holder's Sp. Atk is 1.5x, but it can only select the first move it executes."}, "chopleberry": {"name": "<PERSON><PERSON>", "spritenum": 71, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Fighting"}, "num": 189, "gen": 4, "desc": "Halves damage taken from a supereffective Fighting-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Fighting-type attack. Single use."}, "clawfossil": {"name": "<PERSON><PERSON> F<PERSON>il", "spritenum": 72, "fling": {"basePower": 100}, "num": 100, "gen": 3, "isNonstandard": "Past", "desc": "Can be revived into Anorith.", "shortDesc": "Can be revived into Anorith."}, "clearamulet": {"name": "Clear Amulet", "spritenum": 747, "fling": {"basePower": 30}, "num": 1882, "gen": 9, "desc": "Prevents other Pokemon from lowering the holder's stat stages.", "shortDesc": "Prevents other Pokemon from lowering the holder's stat stages."}, "cloversweet": {"name": "Clover Sweet", "spritenum": 707, "fling": {"basePower": 10}, "num": 1112, "gen": 8, "desc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around.", "shortDesc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around."}, "cobaberry": {"name": "Coba Berry", "spritenum": 76, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Flying"}, "num": 192, "gen": 4, "desc": "Halves damage taken from a supereffective Flying-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Flying-type attack. Single use."}, "colburberry": {"name": "<PERSON><PERSON>", "spritenum": 78, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Dark"}, "num": 198, "gen": 4, "desc": "Halves damage taken from a supereffective Dark-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Dark-type attack. Single use."}, "cornerstonemask": {"name": "Cornerstone Mask", "spritenum": 758, "fling": {"basePower": 60}, "onBasePowerPriority": 15, "forcedForme": "Ogerpon-Cornerstone", "itemUser": ["Ogerpon-Cornerstone"], "num": 2406, "gen": 9, "desc": "Ogerpon-Cornerstone: 1.2x power attacks Terastallize to gain Embody Aspect.", "shortDesc": "Ogerpon-Cornerstone: 1.2x power attacks Terastallize to gain Embody Aspect."}, "cornnberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 81, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Bug"}, "onEat": false, "num": 175, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "coverfossil": {"name": "Cover Fossil", "spritenum": 85, "fling": {"basePower": 100}, "num": 572, "gen": 5, "isNonstandard": "Past", "desc": "Can be revived into Tirtouga.", "shortDesc": "Can be revived into Tirtouga."}, "covertcloak": {"name": "<PERSON><PERSON>", "spritenum": 750, "fling": {"basePower": 30}, "num": 1885, "gen": 9, "desc": "Holder is not affected by the secondary effect of another Po<PERSON><PERSON>'s attack.", "shortDesc": "Holder is not affected by the secondary effect of another Po<PERSON><PERSON>'s attack."}, "crackedpot": {"name": "Cracked <PERSON>", "spritenum": 719, "fling": {"basePower": 80}, "num": 1253, "gen": 8, "desc": "Evolves Sinistea into Polteageist when used.", "shortDesc": "Evolves Sinistea into Polteageist when used."}, "custapberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 86, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Ghost"}, "onFractionalPriorityPriority": -2, "num": 210, "gen": 4, "desc": "Holder moves first in its priority bracket when at 1/4 max HP or less. Single use.", "shortDesc": "Holder moves first in its priority bracket when at 1/4 max HP or less. Single use."}, "damprock": {"name": "Damp Rock", "spritenum": 88, "fling": {"basePower": 60}, "num": 285, "gen": 4, "desc": "Holder's use of Rain Dance lasts 8 turns instead of 5.", "shortDesc": "Holder's use of Rain Dance lasts 8 turns instead of 5."}, "darkgem": {"name": "Dark Gem", "spritenum": 89, "isGem": true, "num": 562, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Dark-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Dark-type attack will have 1.3x power. Single use."}, "darkmemory": {"name": "Dark Memory", "spritenum": 683, "onMemory": "Dark", "forcedForme": "<PERSON><PERSON>-<PERSON>", "itemUser": ["<PERSON><PERSON>-<PERSON>"], "num": 919, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Dark type.", "shortDesc": "Holder's Multi-Attack is Dark type."}, "darkiniumz": {"name": "Darkinium Z", "spritenum": 646, "onPlate": "Dark", "onTakeItem": false, "zMove": true, "zMoveType": "Dark", "forcedForme": "Arceus-Dark", "num": 791, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Dark move, this item allows it to use a Dark Z-Move.", "shortDesc": "If holder has a Dark move, this item allows it to use a Dark Z-Move."}, "dawnstone": {"name": "<PERSON>", "spritenum": 92, "fling": {"basePower": 80}, "num": 109, "gen": 4, "desc": "Evolves male <PERSON><PERSON><PERSON> into <PERSON><PERSON><PERSON> and female <PERSON><PERSON><PERSON> into <PERSON><PERSON><PERSON> when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "decidiumz": {"name": "Decidium Z", "spritenum": 650, "onTakeItem": false, "zMove": "Sinister Arrow Raid", "zMoveFrom": "Spirit Shackle", "itemUser": ["<PERSON><PERSON><PERSON><PERSON>"], "num": 798, "gen": 7, "isNonstandard": "Past", "desc": "If held by a Decidueye with Spirit Shackle, it can use Sinister Arrow Raid.", "shortDesc": "If held by a Decidueye with Spirit Shackle, it can use Sinister Arrow Raid."}, "deepseascale": {"name": "Deep Sea Scale", "spritenum": 93, "fling": {"basePower": 30}, "onModifySpDPriority": 2, "itemUser": ["<PERSON><PERSON><PERSON><PERSON>"], "num": 227, "gen": 3, "isNonstandard": "Past", "desc": "If held by a Clamperl, its Sp. Def is doubled. Evolves Clamperl into Gorebyss when traded.", "shortDesc": "If held by a Clamperl, its Sp. Def is doubled."}, "deepseatooth": {"name": "Deep Sea Tooth", "spritenum": 94, "fling": {"basePower": 90}, "onModifySpAPriority": 1, "itemUser": ["<PERSON><PERSON><PERSON><PERSON>"], "num": 226, "gen": 3, "isNonstandard": "Past", "desc": "If held by a Clamperl, its Sp. Atk is doubled. Evolves Clamperl into Huntail when traded.", "shortDesc": "If held by a Clamperl, its Sp. Atk is doubled."}, "destinyknot": {"name": "<PERSON> Knot", "spritenum": 95, "fling": {"basePower": 10}, "onAttractPriority": -100, "num": 280, "gen": 4, "desc": "If holder becomes infatuated, the other Pokemon also becomes infatuated.", "shortDesc": "If holder becomes infatuated, the other Pokemon also becomes infatuated."}, "diancite": {"name": "Diancite", "spritenum": 624, "megaStone": "Diancie-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 764, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Diancie, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Diancie, this item allows it to Mega Evolve in battle."}, "diveball": {"name": "Dive Ball", "spritenum": 101, "num": 7, "gen": 3, "isPokeball": true, "desc": "A Poke Ball that works especially well on Pokemon that live underwater.", "shortDesc": "A Poke Ball that works especially well on Pokemon that live underwater."}, "domefossil": {"name": "Dome Fossil", "spritenum": 102, "fling": {"basePower": 100}, "num": 102, "gen": 3, "isNonstandard": "Past", "desc": "Can be revived into Kabuto.", "shortDesc": "Can be revived into Kabuto."}, "dousedrive": {"name": "Douse Drive", "spritenum": 103, "onDrive": "Water", "forcedForme": "Genesect-Douse", "itemUser": ["Genesect-Douse"], "num": 116, "gen": 5, "isNonstandard": "Past", "desc": "Holder's Techno Blast is Water type.", "shortDesc": "Holder's Techno Blast is Water type."}, "dracoplate": {"name": "Draco Plate", "spritenum": 105, "onPlate": "Dragon", "onBasePowerPriority": 15, "forcedForme": "Arceus-Dragon", "num": 311, "gen": 4, "desc": "Holder's Dragon-type attacks have 1.2x power. Judgment is Dragon type.", "shortDesc": "Holder's Dragon-type attacks have 1.2x power. Judgment is Dragon type."}, "dragonfang": {"name": "Dragon Fang", "spritenum": 106, "fling": {"basePower": 70}, "onBasePowerPriority": 15, "num": 250, "gen": 2, "desc": "Holder's Dragon-type attacks have 1.2x power.", "shortDesc": "Holder's Dragon-type attacks have 1.2x power."}, "dragongem": {"name": "Dragon Gem", "spritenum": 107, "isGem": true, "num": 561, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Dragon-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Dragon-type attack will have 1.3x power. Single use."}, "dragonmemory": {"name": "Dragon Memory", "spritenum": 682, "onMemory": "Dragon", "forcedForme": "<PERSON><PERSON>-<PERSON>", "itemUser": ["<PERSON><PERSON>-<PERSON>"], "num": 918, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Dragon type.", "shortDesc": "Holder's Multi-Attack is Dragon type."}, "dragonscale": {"name": "Dragon Scale", "spritenum": 108, "fling": {"basePower": 30}, "num": 235, "gen": 2, "desc": "Evolves Seadra into Kingdra when traded.", "shortDesc": "Evolves Seadra into Kingdra when traded."}, "dragoniumz": {"name": "Dragonium Z", "spritenum": 645, "onPlate": "Dragon", "onTakeItem": false, "zMove": true, "zMoveType": "Dragon", "forcedForme": "Arceus-Dragon", "num": 790, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Dragon move, this item allows it to use a Dragon Z-Move.", "shortDesc": "If holder has a Dragon move, this item allows it to use a Dragon Z-Move."}, "dreadplate": {"name": "Dread Plate", "spritenum": 110, "onPlate": "Dark", "onBasePowerPriority": 15, "forcedForme": "Arceus-Dark", "num": 312, "gen": 4, "desc": "Holder's Dark-type attacks have 1.2x power. Judgment is Dark type.", "shortDesc": "Holder's Dark-type attacks have 1.2x power. Judgment is Dark type."}, "dreamball": {"name": "Dream Ball", "spritenum": 111, "num": 576, "gen": 5, "isPokeball": true, "desc": "A Poke Ball that makes it easier to catch wild Pokémon while they're asleep.", "shortDesc": "A Poke Ball that makes it easier to catch wild Pokémon while they're asleep."}, "dubiousdisc": {"name": "Dubious Disc", "spritenum": 113, "fling": {"basePower": 50}, "num": 324, "gen": 4, "desc": "Evolves Porygon2 into Porygon-Z when traded.", "shortDesc": "Evolves Porygon2 into Porygon-Z when traded."}, "durinberry": {"name": "<PERSON><PERSON>", "spritenum": 114, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Water"}, "onEat": false, "num": 182, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "duskball": {"name": "Dusk Ball", "spritenum": 115, "num": 13, "gen": 4, "isPokeball": true, "desc": "A Poke Ball that makes it easier to catch wild Pokemon at night or in caves.", "shortDesc": "A Poke Ball that makes it easier to catch wild Pokemon at night or in caves."}, "duskstone": {"name": "Dusk Stone", "spritenum": 116, "fling": {"basePower": 80}, "num": 108, "gen": 4, "desc": "Evolves Murkrow into Honchkrow, <PERSON>sdreav<PERSON> into Mismagius, <PERSON><PERSON> into Chandelure, and <PERSON><PERSON><PERSON> into Aegislash when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "earthplate": {"name": "Earth Plate", "spritenum": 117, "onPlate": "Ground", "onBasePowerPriority": 15, "forcedForme": "Arceus-Ground", "num": 305, "gen": 4, "desc": "Holder's Ground-type attacks have 1.2x power. Judgment is Ground type.", "shortDesc": "Holder's Ground-type attacks have 1.2x power. Judgment is Ground type."}, "eeviumz": {"name": "Eevium Z", "spritenum": 657, "onTakeItem": false, "zMove": "Extreme Evoboost", "zMoveFrom": "Last Resort", "itemUser": ["Eevee"], "num": 805, "gen": 7, "isNonstandard": "Past", "desc": "If held by an Eevee with Last Resort, it can use Extreme Evoboost.", "shortDesc": "If held by an Eevee with Last Resort, it can use Extreme Evoboost."}, "ejectbutton": {"name": "<PERSON><PERSON>", "spritenum": 118, "fling": {"basePower": 30}, "onAfterMoveSecondaryPriority": 2, "num": 547, "gen": 5, "desc": "If holder survives a hit, it immediately switches out to a chosen ally. Single use.", "shortDesc": "If holder survives a hit, it immediately switches out to a chosen ally. Single use."}, "ejectpack": {"name": "Eject Pack", "spritenum": 714, "fling": {"basePower": 50}, "num": 1119, "gen": 8, "desc": "If the holder's stat stages are lowered, it switches to a chosen ally. Single use.", "shortDesc": "If the holder's stat stages are lowered, it switches to a chosen ally. Single use."}, "electirizer": {"name": "Electirizer", "spritenum": 119, "fling": {"basePower": 80}, "num": 322, "gen": 4, "desc": "Evolves Electabuzz into Electivire when traded.", "shortDesc": "Evolves Electabuzz into Electivire when traded."}, "electricgem": {"name": "Electric Gem", "spritenum": 120, "isGem": true, "num": 550, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Electric-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Electric-type attack will have 1.3x power. Single use."}, "electricmemory": {"name": "Electric Memory", "spritenum": 679, "onMemory": "Electric", "forcedForme": "<PERSON><PERSON>-Electric", "itemUser": ["<PERSON><PERSON>-Electric"], "num": 915, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Electric type.", "shortDesc": "Holder's Multi-Attack is Electric type."}, "electricseed": {"name": "Electric Seed", "spritenum": 664, "fling": {"basePower": 10}, "boosts": {"def": 1}, "num": 881, "gen": 7, "desc": "If the terrain is Electric Terrain, raises holder's Defense by 1 stage. Single use.", "shortDesc": "If the terrain is Electric Terrain, raises holder's Defense by 1 stage. Single use."}, "electriumz": {"name": "Electrium Z", "spritenum": 634, "onPlate": "Electric", "onTakeItem": false, "zMove": true, "zMoveType": "Electric", "forcedForme": "Arceus-Electric", "num": 779, "gen": 7, "isNonstandard": "Past", "desc": "If holder has an Electric move, this item allows it to use an Electric Z-Move.", "shortDesc": "If holder has an Electric move, this item allows it to use an Electric Z-Move."}, "enigmaberry": {"name": "<PERSON><PERSON>", "spritenum": 124, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Bug"}, "num": 208, "gen": 3, "desc": "Restores 1/4 max HP after holder is hit by a supereffective move. Single use.", "shortDesc": "Restores 1/4 max HP after holder is hit by a supereffective move. Single use."}, "eviolite": {"name": "Eviolite", "spritenum": 130, "fling": {"basePower": 40}, "onModifyDefPriority": 2, "onModifySpDPriority": 2, "num": 538, "gen": 5, "desc": "If holder's species can evolve, its Defense and Sp. Def are 1.5x.", "shortDesc": "If holder's species can evolve, its Defense and Sp. Def are 1.5x."}, "expertbelt": {"name": "Expert Belt", "spritenum": 132, "fling": {"basePower": 10}, "num": 268, "gen": 4, "desc": "Holder's attacks that are super effective against the target do 1.2x damage.", "shortDesc": "Holder's attacks that are super effective against the target do 1.2x damage."}, "fairiumz": {"name": "Fairium Z", "spritenum": 648, "onPlate": "Fairy", "onTakeItem": false, "zMove": true, "zMoveType": "Fairy", "forcedForme": "<PERSON><PERSON>-<PERSON>", "num": 793, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Fairy move, this item allows it to use a Fairy Z-Move.", "shortDesc": "If holder has a Fairy move, this item allows it to use a Fairy Z-Move."}, "fairyfeather": {"name": "<PERSON> Feather", "spritenum": 754, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 2401, "gen": 9, "desc": "Holder's Fairy-type attacks have 1.2x power.", "shortDesc": "Holder's Fairy-type attacks have 1.2x power."}, "fairygem": {"name": "Fairy Gem", "spritenum": 611, "isGem": true, "num": 715, "gen": 6, "isNonstandard": "Past", "desc": "Holder's first successful Fairy-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Fairy-type attack will have 1.3x power. Single use."}, "fairymemory": {"name": "Fairy Memory", "spritenum": 684, "onMemory": "Fairy", "forcedForme": "<PERSON><PERSON>-<PERSON>", "itemUser": ["<PERSON><PERSON>-<PERSON>"], "num": 920, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Fairy type.", "shortDesc": "Holder's Multi-Attack is Fairy type."}, "fastball": {"name": "Fast Ball", "spritenum": 137, "num": 492, "gen": 2, "isPokeball": true, "desc": "A Poke Ball that makes it easier to catch <PERSON><PERSON><PERSON> which are quick to run away.", "shortDesc": "A Poke Ball that makes it easier to catch <PERSON><PERSON><PERSON> which are quick to run away."}, "fightinggem": {"name": "Fighting Gem", "spritenum": 139, "isGem": true, "num": 553, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Fighting-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Fighting-type attack will have 1.3x power. Single use."}, "fightingmemory": {"name": "Fighting Memory", "spritenum": 668, "onMemory": "Fighting", "forcedForme": "<PERSON><PERSON>-Fighting", "itemUser": ["<PERSON><PERSON>-Fighting"], "num": 904, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Fighting type.", "shortDesc": "Holder's Multi-Attack is Fighting type."}, "fightiniumz": {"name": "Fightinium Z", "spritenum": 637, "onPlate": "Fighting", "onTakeItem": false, "zMove": true, "zMoveType": "Fighting", "forcedForme": "Arceus-Fighting", "num": 782, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Fighting move, this item allows it to use a Fighting Z-Move.", "shortDesc": "If holder has a Fighting move, this item allows it to use a Fighting Z-Move."}, "figyberry": {"name": "<PERSON><PERSON>", "spritenum": 140, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Bug"}, "num": 159, "gen": 3, "desc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -Atk Nature. Single use.", "shortDesc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -Atk Nature. Single use."}, "firegem": {"name": "Fire Gem", "spritenum": 141, "isGem": true, "num": 548, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Fire-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Fire-type attack will have 1.3x power. Single use."}, "firememory": {"name": "Fire Memory", "spritenum": 676, "onMemory": "Fire", "forcedForme": "<PERSON><PERSON>-Fire", "itemUser": ["<PERSON><PERSON>-Fire"], "num": 912, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Fire type.", "shortDesc": "Holder's Multi-Attack is Fire type."}, "firestone": {"name": "Fire Stone", "spritenum": 142, "fling": {"basePower": 30}, "num": 82, "gen": 1, "desc": "Evolves Vulpix into Ninetales, Growlithe into Arcanine, Eevee into Flareon, and Pansear into Simisear when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "firiumz": {"name": "Firium Z", "spritenum": 632, "onPlate": "Fire", "onTakeItem": false, "zMove": true, "zMoveType": "Fire", "forcedForme": "Arceus-Fire", "num": 777, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Fire move, this item allows it to use a Fire Z-Move.", "shortDesc": "If holder has a Fire move, this item allows it to use a Fire Z-Move."}, "fistplate": {"name": "Fist Plate", "spritenum": 143, "onPlate": "Fighting", "onBasePowerPriority": 15, "forcedForme": "Arceus-Fighting", "num": 303, "gen": 4, "desc": "Holder's Fighting-type attacks have 1.2x power. Judgment is Fighting type.", "shortDesc": "Holder's Fighting-type attacks have 1.2x power. Judgment is Fighting type."}, "flameorb": {"name": "Flame Orb", "spritenum": 145, "fling": {"basePower": 30, "status": "brn"}, "onResidualOrder": 28, "onResidualSubOrder": 3, "num": 273, "gen": 4, "desc": "At the end of every turn, this item attempts to burn the holder.", "shortDesc": "At the end of every turn, this item attempts to burn the holder."}, "flameplate": {"name": "Flame Plate", "spritenum": 146, "onPlate": "Fire", "onBasePowerPriority": 15, "forcedForme": "Arceus-Fire", "num": 298, "gen": 4, "desc": "Holder's Fire-type attacks have 1.2x power. Judgment is Fire type.", "shortDesc": "Holder's Fire-type attacks have 1.2x power. Judgment is Fire type."}, "floatstone": {"name": "Float Stone", "spritenum": 147, "fling": {"basePower": 30}, "num": 539, "gen": 5, "desc": "Holder's weight is halved.", "shortDesc": "Holder's weight is halved."}, "flowersweet": {"name": "Flower Sweet", "spritenum": 708, "fling": {"basePower": 0}, "num": 1113, "gen": 8, "desc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around.", "shortDesc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around."}, "flyinggem": {"name": "Flying Gem", "spritenum": 149, "isGem": true, "num": 556, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Flying-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Flying-type attack will have 1.3x power. Single use."}, "flyingmemory": {"name": "Flying Memory", "spritenum": 669, "onMemory": "Flying", "forcedForme": "<PERSON><PERSON>-Flying", "itemUser": ["<PERSON><PERSON>-Flying"], "num": 905, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Flying type.", "shortDesc": "Holder's Multi-Attack is Flying type."}, "flyiniumz": {"name": "Flyinium Z", "spritenum": 640, "onPlate": "Flying", "onTakeItem": false, "zMove": true, "zMoveType": "Flying", "forcedForme": "Arceus-Flying", "num": 785, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Flying move, this item allows it to use a Flying Z-Move.", "shortDesc": "If holder has a Flying move, this item allows it to use a Flying Z-Move."}, "focusband": {"name": "Focus Band", "spritenum": 150, "fling": {"basePower": 10}, "onDamagePriority": -40, "num": 230, "gen": 2, "desc": "Holder has a 10% chance to survive an attack that would KO it with 1 HP.", "shortDesc": "Holder has a 10% chance to survive an attack that would KO it with 1 HP."}, "focussash": {"name": "Focus Sash", "spritenum": 151, "fling": {"basePower": 10}, "onDamagePriority": -40, "num": 275, "gen": 4, "desc": "If holder's HP is full, will survive an attack that would KO it with 1 HP. Single use.", "shortDesc": "If holder's HP is full, will survive an attack that would KO it with 1 HP. Single use."}, "fossilizedbird": {"name": "Fossilized Bird", "spritenum": 700, "fling": {"basePower": 100}, "num": 1105, "gen": 8, "isNonstandard": "Past", "desc": "Can revive into Dracozolt with Fossilized Drake or Arctozolt with Fossilized Dino.", "shortDesc": "Can revive into Dracozolt with Fossilized Drake or Arctozolt with Fossilized Dino."}, "fossilizeddino": {"name": "Fossilized Dino", "spritenum": 703, "fling": {"basePower": 100}, "num": 1108, "gen": 8, "isNonstandard": "Past", "desc": "Can revive into Arctovish with Fossilized Fish or Arctozolt with Fossilized Bird.", "shortDesc": "Can revive into Arctovish with Fossilized Fish or Arctozolt with Fossilized Bird."}, "fossilizeddrake": {"name": "Fossilized Drake", "spritenum": 702, "fling": {"basePower": 100}, "num": 1107, "gen": 8, "isNonstandard": "Past", "desc": "Can revive into Dracozolt with Fossilized Bird or Dracovish with Fossilized Fish.", "shortDesc": "Can revive into Dracozolt with Fossilized Bird or Dracovish with Fossilized Fish."}, "fossilizedfish": {"name": "Fossilized Fish", "spritenum": 701, "fling": {"basePower": 100}, "num": 1106, "gen": 8, "isNonstandard": "Past", "desc": "Can revive into Dracovish with Fossilized <PERSON> or Arctov<PERSON> with Fossilized Dino.", "shortDesc": "Can revive into Dracovish with Fossilized <PERSON> or Arctov<PERSON> with Fossilized Dino."}, "friendball": {"name": "Friend Ball", "spritenum": 153, "num": 497, "gen": 2, "isPokeball": true, "desc": "A Poke Ball that makes caught <PERSON><PERSON><PERSON> more friendly.", "shortDesc": "A Poke Ball that makes caught <PERSON><PERSON><PERSON> more friendly."}, "fullincense": {"name": "Full Incense", "spritenum": 155, "fling": {"basePower": 10}, "onFractionalPriority": -0.1, "num": 316, "gen": 4, "isNonstandard": "Past", "desc": "Holder moves last in its priority bracket.", "shortDesc": "Holder moves last in its priority bracket."}, "galaricacuff": {"name": "<PERSON><PERSON>", "spritenum": 739, "fling": {"basePower": 30}, "num": 1582, "gen": 8, "desc": "Evolves Galarian Slowpoke into Galarian Slowbro when used.", "shortDesc": "Evolves Galarian Slowpoke into Galarian Slowbro when used."}, "galaricawreath": {"name": "Galarica Wreath", "spritenum": 740, "fling": {"basePower": 30}, "num": 1592, "gen": 8, "desc": "Evolves Galarian Slowpoke into Galarian Slowking when used.", "shortDesc": "Evolves Galarian Slowpoke into Galarian Slowking when used."}, "galladite": {"name": "Galladite", "spritenum": 616, "megaStone": "Gallade-Mega", "megaEvolves": "Gallade", "itemUser": ["Gallade"], "num": 756, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Gallade, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Gallade, this item allows it to Mega Evolve in battle."}, "ganlonberry": {"name": "Ganlon Berry", "spritenum": 158, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Ice"}, "num": 202, "gen": 3, "desc": "Raises holder's Defense by 1 stage when at 1/4 max HP or less. Single use.", "shortDesc": "Raises holder's Defense by 1 stage when at 1/4 max HP or less. Single use."}, "garchompite": {"name": "Garchompite", "spritenum": 589, "megaStone": "Garchomp-Mega", "megaEvolves": "Garchomp", "itemUser": ["Garchomp"], "num": 683, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Garchomp, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Garchomp, this item allows it to Mega Evolve in battle."}, "gardevoirite": {"name": "Gardevoirite", "spritenum": 587, "megaStone": "Gardevoir-Mega", "megaEvolves": "Gardevoir", "itemUser": ["Gardevoir"], "num": 657, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Gardevoir, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Gardevoir, this item allows it to Mega Evolve in battle."}, "gengarite": {"name": "Gengarite", "spritenum": 588, "megaStone": "Gengar-Mega", "megaEvolves": "Gengar", "itemUser": ["Gengar"], "num": 656, "gen": 6, "isNonstandard": "Past", "desc": "If held by a <PERSON>gar, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a <PERSON>gar, this item allows it to Mega Evolve in battle."}, "ghostgem": {"name": "Ghost Gem", "spritenum": 161, "isGem": true, "num": 560, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Ghost-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Ghost-type attack will have 1.3x power. Single use."}, "ghostmemory": {"name": "Ghost Memory", "spritenum": 674, "onMemory": "Ghost", "forcedForme": "<PERSON><PERSON>-<PERSON>", "itemUser": ["<PERSON><PERSON>-<PERSON>"], "num": 910, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Ghost type.", "shortDesc": "Holder's Multi-Attack is Ghost type."}, "ghostiumz": {"name": "Ghostium Z", "spritenum": 644, "onPlate": "Ghost", "onTakeItem": false, "zMove": true, "zMoveType": "Ghost", "forcedForme": "Arceus-Ghost", "num": 789, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Ghost move, this item allows it to use a Ghost Z-Move.", "shortDesc": "If holder has a Ghost move, this item allows it to use a Ghost Z-Move."}, "glalitite": {"name": "Glalitite", "spritenum": 623, "megaStone": "Glalie-Mega", "megaEvolves": "G<PERSON><PERSON>", "itemUser": ["G<PERSON><PERSON>"], "num": 763, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Glalie, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Glalie, this item allows it to Mega Evolve in battle."}, "goldbottlecap": {"name": "Gold Bottle Cap", "spritenum": 697, "fling": {"basePower": 30}, "num": 796, "gen": 7, "desc": "Used for Hyper Training. All of a Pokemon's stats are calculated with an IV of 31.", "shortDesc": "Used for Hyper Training. All of a Pokemon's stats are calculated with an IV of 31."}, "grassgem": {"name": "Grass Gem", "spritenum": 172, "isGem": true, "num": 551, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Grass-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Grass-type attack will have 1.3x power. Single use."}, "grassmemory": {"name": "Grass Memory", "spritenum": 678, "onMemory": "Grass", "forcedForme": "<PERSON><PERSON>-<PERSON>", "itemUser": ["<PERSON><PERSON>-<PERSON>"], "num": 914, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Grass type.", "shortDesc": "Holder's Multi-Attack is Grass type."}, "grassiumz": {"name": "Grassium Z", "spritenum": 635, "onPlate": "Grass", "onTakeItem": false, "zMove": true, "zMoveType": "Grass", "forcedForme": "Arceus<PERSON><PERSON>", "num": 780, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Grass move, this item allows it to use a Grass Z-Move.", "shortDesc": "If holder has a Grass move, this item allows it to use a Grass Z-Move."}, "grassyseed": {"name": "<PERSON><PERSON> Seed", "spritenum": 667, "fling": {"basePower": 10}, "boosts": {"def": 1}, "num": 884, "gen": 7, "desc": "If the terrain is Grassy Terrain, raises holder's Defense by 1 stage. Single use.", "shortDesc": "If the terrain is Grassy Terrain, raises holder's Defense by 1 stage. Single use."}, "greatball": {"name": "Great Ball", "spritenum": 174, "num": 3, "gen": 1, "isPokeball": true, "desc": "A high-performance Ball that provides a higher catch rate than a Poke Ball.", "shortDesc": "A high-performance Ball that provides a higher catch rate than a Poke Ball."}, "grepaberry": {"name": "Gre<PERSON> Berry", "spritenum": 178, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Flying"}, "onEat": false, "num": 173, "gen": 3, "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "gripclaw": {"name": "<PERSON><PERSON>", "spritenum": 179, "fling": {"basePower": 90}, "num": 286, "gen": 4, "desc": "Holder's partial-trapping moves always last 7 turns.", "shortDesc": "Holder's partial-trapping moves always last 7 turns."}, "griseouscore": {"name": "Griseous Core", "spritenum": 743, "onBasePowerPriority": 15, "forcedForme": "Giratina-Origin", "itemUser": ["Giratina-Origin"], "num": 1779, "gen": 8, "desc": "If held by a Giratina, its Ghost- and Dragon-type attacks have 1.2x power.", "shortDesc": "If held by a Giratina, its Ghost- and Dragon-type attacks have 1.2x power."}, "griseousorb": {"name": "Griseous <PERSON><PERSON>", "spritenum": 180, "fling": {"basePower": 60}, "onBasePowerPriority": 15, "itemUser": ["Giratina"], "num": 112, "gen": 4, "desc": "If held by a Giratina, its Ghost- and Dragon-type attacks have 1.2x power.", "shortDesc": "If held by a Giratina, its Ghost- and Dragon-type attacks have 1.2x power."}, "groundgem": {"name": "Ground Gem", "spritenum": 182, "isGem": true, "num": 555, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Ground-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Ground-type attack will have 1.3x power. Single use."}, "groundmemory": {"name": "Ground Memory", "spritenum": 671, "onMemory": "Ground", "forcedForme": "<PERSON><PERSON>-Ground", "itemUser": ["<PERSON><PERSON>-Ground"], "num": 907, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Ground type.", "shortDesc": "Holder's Multi-Attack is Ground type."}, "groundiumz": {"name": "Groundium Z", "spritenum": 639, "onPlate": "Ground", "onTakeItem": false, "zMove": true, "zMoveType": "Ground", "forcedForme": "Arceus-Ground", "num": 784, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Ground move, this item allows it to use a Ground Z-Move.", "shortDesc": "If holder has a Ground move, this item allows it to use a Ground Z-Move."}, "gyaradosite": {"name": "Gyaradosite", "spritenum": 589, "megaStone": "Gyarados-Mega", "megaEvolves": "<PERSON><PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON><PERSON>"], "num": 676, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Gyarados, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Gyarados, this item allows it to Mega Evolve in battle."}, "habanberry": {"name": "<PERSON><PERSON>", "spritenum": 185, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Dragon"}, "num": 197, "gen": 4, "desc": "Halves damage taken from a supereffective Dragon-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Dragon-type attack. Single use."}, "hardstone": {"name": "Hard Stone", "spritenum": 187, "fling": {"basePower": 100}, "onBasePowerPriority": 15, "num": 238, "gen": 2, "desc": "Holder's Rock-type attacks have 1.2x power.", "shortDesc": "Holder's Rock-type attacks have 1.2x power."}, "healball": {"name": "Heal Ball", "spritenum": 188, "num": 14, "gen": 4, "isPokeball": true, "desc": "A remedial Poke Ball that restores the caught <PERSON>kemon's HP and status problem.", "shortDesc": "A remedial Poke Ball that restores the caught <PERSON>kemon's HP and status problem."}, "hearthflamemask": {"name": "Hearthflame Mask", "spritenum": 760, "fling": {"basePower": 60}, "onBasePowerPriority": 15, "forcedForme": "Ogerpon-Hearthflame", "itemUser": ["Ogerpon-Hearthflame"], "num": 2408, "gen": 9, "desc": "Ogerpon-Hearthflame: 1.2x power attacks Terastallize to gain Embody Aspect.", "shortDesc": "Ogerpon-Hearthflame: 1.2x power attacks Terastallize to gain Embody Aspect."}, "heatrock": {"name": "Heat Rock", "spritenum": 193, "fling": {"basePower": 60}, "num": 284, "gen": 4, "desc": "Holder's use of Sunny Day lasts 8 turns instead of 5.", "shortDesc": "Holder's use of Sunny Day lasts 8 turns instead of 5."}, "heavyball": {"name": "Heavy Ball", "spritenum": 194, "num": 495, "gen": 2, "isPokeball": true, "desc": "A Poke Ball for catching very heavy Poke<PERSON>.", "shortDesc": "A Poke Ball for catching very heavy Poke<PERSON>."}, "heavydutyboots": {"name": "Heavy-Duty Boots", "spritenum": 715, "fling": {"basePower": 80}, "num": 1120, "gen": 8, "desc": "When switching in, the holder is unaffected by hazards on its side of the field.", "shortDesc": "When switching in, the holder is unaffected by hazards on its side of the field."}, "helixfossil": {"name": "Helix Fossil", "spritenum": 195, "fling": {"basePower": 100}, "num": 101, "gen": 3, "isNonstandard": "Past", "desc": "Can be revived into Omanyte.", "shortDesc": "Can be revived into Omanyte."}, "heracronite": {"name": "Heracronite", "spritenum": 590, "megaStone": "Heracross-Mega", "megaEvolves": "Heracross", "itemUser": ["Heracross"], "num": 680, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Heracross, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Heracross, this item allows it to Mega Evolve in battle."}, "hondewberry": {"name": "Hondew Berry", "spritenum": 213, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Ground"}, "onEat": false, "num": 172, "gen": 3, "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "houndoominite": {"name": "Houndoominite", "spritenum": 591, "megaStone": "Houndoom-Mega", "megaEvolves": "Hound<PERSON>", "itemUser": ["Hound<PERSON>"], "num": 666, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Houndoom, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Houndoom, this item allows it to Mega Evolve in battle."}, "iapapaberry": {"name": "Iapapa Berry", "spritenum": 217, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Dark"}, "num": 163, "gen": 3, "desc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -Def Nature. Single use.", "shortDesc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -Def Nature. Single use."}, "icegem": {"name": "Ice Gem", "spritenum": 218, "isGem": true, "num": 552, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Ice-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Ice-type attack will have 1.3x power. Single use."}, "icememory": {"name": "Ice Memory", "spritenum": 681, "onMemory": "Ice", "forcedForme": "<PERSON><PERSON>-Ice", "itemUser": ["<PERSON><PERSON>-Ice"], "num": 917, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Ice type.", "shortDesc": "Holder's Multi-Attack is Ice type."}, "icestone": {"name": "Ice Stone", "spritenum": 693, "fling": {"basePower": 30}, "num": 849, "gen": 7, "desc": "Evolves Alolan Sandshrew into Alolan Sandslash, Alolan Vulpix into Alolan Ninetales, Eevee into Glaceon, and <PERSON><PERSON> into Galarian Darmanitan when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "icicleplate": {"name": "Icicle Plate", "spritenum": 220, "onPlate": "Ice", "onBasePowerPriority": 15, "forcedForme": "Arceus-Ice", "num": 302, "gen": 4, "desc": "Holder's Ice-type attacks have 1.2x power. Judgment is Ice type.", "shortDesc": "Holder's Ice-type attacks have 1.2x power. Judgment is Ice type."}, "iciumz": {"name": "Icium Z", "spritenum": 636, "onPlate": "Ice", "onTakeItem": false, "zMove": true, "zMoveType": "Ice", "forcedForme": "Arceus-Ice", "num": 781, "gen": 7, "isNonstandard": "Past", "desc": "If holder has an Ice move, this item allows it to use an Ice Z-Move.", "shortDesc": "If holder has an Ice move, this item allows it to use an Ice Z-Move."}, "icyrock": {"name": "<PERSON><PERSON>", "spritenum": 221, "fling": {"basePower": 40}, "num": 282, "gen": 4, "desc": "Holder's use of Hail lasts 8 turns instead of 5.", "shortDesc": "Holder's use of Hail lasts 8 turns instead of 5."}, "inciniumz": {"name": "Incinium Z", "spritenum": 651, "onTakeItem": false, "zMove": "Malicious <PERSON>", "zMoveFrom": "Darkest Lariat", "itemUser": ["Incineroar"], "num": 799, "gen": 7, "isNonstandard": "Past", "desc": "If held by an Incineroar with Darkest Lariat, it can use Malicious Moonsault.", "shortDesc": "If held by an Incineroar with Darkest Lariat, it can use Malicious Moonsault."}, "insectplate": {"name": "Insect Plate", "spritenum": 223, "onPlate": "Bug", "onBasePowerPriority": 15, "forcedForme": "Arceus-<PERSON>ug", "num": 308, "gen": 4, "desc": "Holder's Bug-type attacks have 1.2x power. Judgment is Bug type.", "shortDesc": "Holder's Bug-type attacks have 1.2x power. Judgment is Bug type."}, "ironball": {"name": "Iron Ball", "spritenum": 224, "fling": {"basePower": 130}, "num": 278, "gen": 4, "desc": "Holder is grounded, Speed halved. If Flying type, takes neutral Ground damage.", "shortDesc": "Holder is grounded, Speed halved. If Flying type, takes neutral Ground damage."}, "ironplate": {"name": "Iron Plate", "spritenum": 225, "onPlate": "Steel", "onBasePowerPriority": 15, "forcedForme": "Arceus-Steel", "num": 313, "gen": 4, "desc": "Holder's Steel-type attacks have 1.2x power. Judgment is Steel type.", "shortDesc": "Holder's Steel-type attacks have 1.2x power. Judgment is Steel type."}, "jabocaberry": {"name": "Jaboca Berry", "spritenum": 230, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Dragon"}, "num": 211, "gen": 4, "desc": "If holder is hit by a physical move, attacker loses 1/8 of its max HP. Single use.", "shortDesc": "If holder is hit by a physical move, attacker loses 1/8 of its max HP. Single use."}, "jawfossil": {"name": "Jaw Fossil", "spritenum": 694, "fling": {"basePower": 100}, "num": 710, "gen": 6, "isNonstandard": "Past", "desc": "Can be revived into Tyrunt.", "shortDesc": "Can be revived into Tyrunt."}, "kasibberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 233, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Ghost"}, "num": 196, "gen": 4, "desc": "Halves damage taken from a supereffective Ghost-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Ghost-type attack. Single use."}, "kebiaberry": {"name": "<PERSON><PERSON>", "spritenum": 234, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Poison"}, "num": 190, "gen": 4, "desc": "Halves damage taken from a supereffective Poison-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Poison-type attack. Single use."}, "keeberry": {"name": "<PERSON><PERSON>", "spritenum": 593, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Fairy"}, "num": 687, "gen": 6, "desc": "Raises holder's Defense by 1 stage after it is hit by a physical attack. Single use.", "shortDesc": "Raises holder's Defense by 1 stage after it is hit by a physical attack. Single use."}, "kelpsyberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 235, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Fighting"}, "onEat": false, "num": 170, "gen": 3, "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "kangaskhanite": {"name": "Kangaskhanite", "spritenum": 592, "megaStone": "Kangaskhan-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 675, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Kangaskhan, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Kangaskhan, this item allows it to Mega Evolve in battle."}, "kingsrock": {"name": "King's Rock", "spritenum": 236, "fling": {"basePower": 30, "volatileStatus": "flinch"}, "onModifyMovePriority": -1, "num": 221, "gen": 2, "desc": "Holder's attacks without a chance to make the target flinch gain a 10% chance to make the target flinch. Evolves Poliwhirl into Politoed and Slowpoke into Slowking when traded.", "shortDesc": "Holder's attacks without a chance to flinch gain a 10% chance to flinch."}, "kommoniumz": {"name": "Kommonium Z", "spritenum": 690, "onTakeItem": false, "zMove": "Clangorous Soulblaze", "zMoveFrom": "Clanging Scales", "itemUser": ["Kommo-o", "Kommo-o-Totem"], "num": 926, "gen": 7, "isNonstandard": "Past", "desc": "If held by a Kommo-o with Clanging Scales, it can use Clangorous Soulblaze.", "shortDesc": "If held by a Kommo-o with Clanging Scales, it can use Clangorous Soulblaze."}, "laggingtail": {"name": "Lagging Tail", "spritenum": 237, "fling": {"basePower": 10}, "onFractionalPriority": -0.1, "num": 279, "gen": 4, "desc": "Holder moves last in its priority bracket.", "shortDesc": "Holder moves last in its priority bracket."}, "lansatberry": {"name": "Lansat Berry", "spritenum": 238, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Flying"}, "num": 206, "gen": 3, "desc": "Holder gains the Focus Energy effect when at 1/4 max HP or less. Single use.", "shortDesc": "Holder gains the Focus Energy effect when at 1/4 max HP or less. Single use."}, "latiasite": {"name": "Latiasite", "spritenum": 629, "megaStone": "Latias-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 684, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Latias, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Latias, this item allows it to Mega Evolve in battle."}, "latiosite": {"name": "Latiosite", "spritenum": 630, "megaStone": "Latios-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 685, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Latios, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Latios, this item allows it to Mega Evolve in battle."}, "laxincense": {"name": "Lax Incense", "spritenum": 240, "fling": {"basePower": 10}, "onModifyAccuracyPriority": -2, "num": 255, "gen": 3, "isNonstandard": "Past", "desc": "The accuracy of attacks against the holder is 0.9x.", "shortDesc": "The accuracy of attacks against the holder is 0.9x."}, "leafstone": {"name": "Leaf Stone", "spritenum": 241, "fling": {"basePower": 30}, "num": 85, "gen": 1, "desc": "Evolves Gloom into Vileplume, <PERSON><PERSON><PERSON>bell into Victreebel, Exeggcute into Exeggutor or Alolan Exeggutor, Eevee into Leafeon, Nuzleaf into Shiftry, and Pansage into Simisage when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "leek": {"name": "<PERSON><PERSON>", "fling": {"basePower": 60}, "spritenum": 475, "itemUser": ["Farfetch’d", "Farfetch’d-<PERSON>r", "<PERSON><PERSON><PERSON>’d"], "num": 259, "gen": 8, "isNonstandard": "Past", "desc": "If held by a Farfetch’d or Sirfetch’d, its critical hit ratio is raised by 2 stages.", "shortDesc": "If held by a Farfetch’d or Sirfetch’d, its critical hit ratio is raised by 2 stages."}, "leftovers": {"name": "Leftovers", "spritenum": 242, "fling": {"basePower": 10}, "onResidualOrder": 5, "onResidualSubOrder": 4, "num": 234, "gen": 2, "desc": "At the end of every turn, holder restores 1/16 of its max HP.", "shortDesc": "At the end of every turn, holder restores 1/16 of its max HP."}, "leppaberry": {"name": "Leppa Berry", "spritenum": 244, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Fighting"}, "num": 154, "gen": 3, "desc": "Restores 10 PP to the first of the holder's moves to reach 0 PP. Single use.", "shortDesc": "Restores 10 PP to the first of the holder's moves to reach 0 PP. Single use."}, "levelball": {"name": "Level Ball", "spritenum": 246, "num": 493, "gen": 2, "isPokeball": true, "desc": "A Poke Ball for catching Pokemon that are a lower level than your own.", "shortDesc": "A Poke Ball for catching Pokemon that are a lower level than your own."}, "liechiberry": {"name": "<PERSON><PERSON>", "spritenum": 248, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Grass"}, "num": 201, "gen": 3, "desc": "Raises holder's Attack by 1 stage when at 1/4 max HP or less. Single use.", "shortDesc": "Raises holder's Attack by 1 stage when at 1/4 max HP or less. Single use."}, "lifeorb": {"name": "Life Orb", "spritenum": 249, "fling": {"basePower": 30}, "num": 270, "gen": 4, "desc": "Holder's attacks do 1.3x damage, and it loses 1/10 its max HP after the attack.", "shortDesc": "Holder's attacks do 1.3x damage, and it loses 1/10 its max HP after the attack."}, "lightball": {"name": "Light Ball", "spritenum": 251, "fling": {"basePower": 30, "status": "par"}, "onModifyAtkPriority": 1, "onModifySpAPriority": 1, "itemUser": ["<PERSON><PERSON><PERSON>", "Pikachu-Cosplay", "Pikachu-Rock-Star", "Pikachu-<PERSON>", "Pikachu-Pop-Star", "<PERSON><PERSON><PERSON>-<PERSON>", "Pikachu-Libre", "<PERSON><PERSON><PERSON>-Original", "Pi<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Pikachu-Sinno<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pikachu-<PERSON>ola", "<PERSON><PERSON><PERSON>-Partner", "<PERSON><PERSON><PERSON>-Starter", "<PERSON><PERSON><PERSON>-World"], "num": 236, "gen": 2, "desc": "If held by a Pikachu, its Attack and Sp. Atk are doubled.", "shortDesc": "If held by a Pikachu, its Attack and Sp. Atk are doubled."}, "lightclay": {"name": "Light Clay", "spritenum": 252, "fling": {"basePower": 30}, "num": 269, "gen": 4, "desc": "Holder's use of Aurora Veil, Light Screen, or Reflect lasts 8 turns instead of 5.", "shortDesc": "Holder's use of Aurora Veil, Light Screen, or Reflect lasts 8 turns instead of 5."}, "loadeddice": {"name": "Loaded Dice", "spritenum": 751, "fling": {"basePower": 30}, "num": 1886, "gen": 9, "desc": "Holder's moves that hit 2-5 times hit 4-5 times Population Bomb hits 4-10 times.", "shortDesc": "Holder's moves that hit 2-5 times hit 4-5 times Population Bomb hits 4-10 times."}, "lopunnite": {"name": "Lopunnite", "spritenum": 626, "megaStone": "Lopunny-Mega", "megaEvolves": "Lopunny", "itemUser": ["Lopunny"], "num": 768, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Lopunny, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Lopunny, this item allows it to Mega Evolve in battle."}, "loveball": {"name": "Love Ball", "spritenum": 258, "num": 496, "gen": 2, "isPokeball": true, "desc": "Poke Ball for catching Pokemon that are the opposite gender of your Pokemon.", "shortDesc": "Poke Ball for catching Pokemon that are the opposite gender of your Pokemon."}, "lovesweet": {"name": "Love Sweet", "spritenum": 705, "fling": {"basePower": 10}, "num": 1110, "gen": 8, "desc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around.", "shortDesc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around."}, "lucarionite": {"name": "Lucarionite", "spritenum": 594, "megaStone": "Lucario-Mega", "megaEvolves": "<PERSON><PERSON>", "itemUser": ["<PERSON><PERSON>"], "num": 673, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Lucario, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Lucario, this item allows it to Mega Evolve in battle."}, "luckypunch": {"name": "Lucky Punch", "spritenum": 261, "fling": {"basePower": 40}, "itemUser": ["<PERSON><PERSON>"], "num": 256, "gen": 2, "isNonstandard": "Past", "desc": "If held by a Chansey, its critical hit ratio is raised by 2 stages.", "shortDesc": "If held by a Chansey, its critical hit ratio is raised by 2 stages."}, "lumberry": {"name": "<PERSON><PERSON>", "spritenum": 262, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Flying"}, "onAfterSetStatusPriority": -1, "num": 157, "gen": 3, "desc": "Holder cures itself if it has a non-volatile status or is confused. Single use.", "shortDesc": "Holder cures itself if it has a non-volatile status or is confused. Single use."}, "luminousmoss": {"name": "Luminous Moss", "spritenum": 595, "fling": {"basePower": 30}, "boosts": {"spd": 1}, "num": 648, "gen": 6, "desc": "Raises holder's Sp. Def by 1 stage if hit by a Water-type attack. Single use.", "shortDesc": "Raises holder's Sp. Def by 1 stage if hit by a Water-type attack. Single use."}, "lunaliumz": {"name": "Lunalium Z", "spritenum": 686, "onTakeItem": false, "zMove": "Menacing <PERSON><PERSON>", "zMoveFrom": "Moongeist <PERSON><PERSON>", "itemUser": ["<PERSON><PERSON>", "Necrozma-Dawn-Wings"], "num": 922, "gen": 7, "isNonstandard": "Past", "desc": "Lunala or Dawn Wings Necrozma with Moongeist <PERSON><PERSON> can use a special Z-Move.", "shortDesc": "Lunala or Dawn Wings Necrozma with Moongeist <PERSON><PERSON> can use a special Z-Move."}, "lureball": {"name": "<PERSON><PERSON>", "spritenum": 264, "num": 494, "gen": 2, "isPokeball": true, "desc": "A Poke Ball for catching <PERSON><PERSON><PERSON> hooked by a <PERSON> when fishing.", "shortDesc": "A Poke Ball for catching <PERSON><PERSON><PERSON> hooked by a <PERSON> when fishing."}, "lustrousglobe": {"name": "Lustrous Globe", "spritenum": 742, "onBasePowerPriority": 15, "forcedForme": "Palkia-Origin", "itemUser": ["Palkia-Origin"], "num": 1778, "gen": 8, "desc": "If held by a Palkia, its Water- and Dragon-type attacks have 1.2x power.", "shortDesc": "If held by a Palkia, its Water- and Dragon-type attacks have 1.2x power."}, "lustrousorb": {"name": "<PERSON><PERSON><PERSON> Or<PERSON>", "spritenum": 265, "fling": {"basePower": 60}, "onBasePowerPriority": 15, "itemUser": ["Pa<PERSON><PERSON>"], "num": 136, "gen": 4, "desc": "If held by a Palkia, its Water- and Dragon-type attacks have 1.2x power.", "shortDesc": "If held by a Palkia, its Water- and Dragon-type attacks have 1.2x power."}, "luxuryball": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 266, "num": 11, "gen": 3, "isPokeball": true, "desc": "A comfortable Poke Ball that makes a caught wild Pokemon quickly grow friendly.", "shortDesc": "A comfortable Poke Ball that makes a caught wild Pokemon quickly grow friendly."}, "lycaniumz": {"name": "Lycanium Z", "spritenum": 689, "onTakeItem": false, "zMove": "Splintered Stormshards", "zMoveFrom": "<PERSON>", "itemUser": ["Lycanroc", "Lycanroc-Midnight", "Lycanroc-Dusk"], "num": 925, "gen": 7, "isNonstandard": "Past", "desc": "If held by a Lycanroc forme with Stone Edge, it can use Splintered Stormshards.", "shortDesc": "If held by a Lycanroc forme with Stone Edge, it can use Splintered Stormshards."}, "machobrace": {"name": "Macho Brace", "spritenum": 269, "ignoreKlutz": true, "fling": {"basePower": 60}, "num": 215, "gen": 3, "isNonstandard": "Past", "desc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect.", "shortDesc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect."}, "magmarizer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "spritenum": 272, "fling": {"basePower": 80}, "num": 323, "gen": 4, "desc": "Evolves Magmar into Magmortar when traded.", "shortDesc": "Evolves Magmar into Magmortar when traded."}, "magnet": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 273, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 242, "gen": 2, "desc": "Holder's Electric-type attacks have 1.2x power.", "shortDesc": "Holder's Electric-type attacks have 1.2x power."}, "magoberry": {"name": "Ma<PERSON>", "spritenum": 274, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Ghost"}, "num": 161, "gen": 3, "desc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -Spe Nature. Single use.", "shortDesc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -Spe Nature. Single use."}, "magostberry": {"name": "Magost Berry", "spritenum": 275, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Rock"}, "onEat": false, "num": 176, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "mail": {"name": "Mail", "spritenum": 403, "num": 137, "gen": 2, "isNonstandard": "Past", "desc": "Cannot be given to or taken from a Pokemon, except by Covet/Knock Off/Thief.", "shortDesc": "Cannot be given to or taken from a Pokemon, except by Covet/Knock Off/Thief."}, "maliciousarmor": {"name": "Malicious Armor", "spritenum": 744, "fling": {"basePower": 30}, "num": 1861, "gen": 9, "desc": "Evolves Charcadet into Ceruledge when used.", "shortDesc": "Evolves Charcadet into Ceruledge when used."}, "manectite": {"name": "Manectite", "spritenum": 596, "megaStone": "Manectric-Mega", "megaEvolves": "Manectric", "itemUser": ["Manectric"], "num": 682, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Manectric, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Manectric, this item allows it to Mega Evolve in battle."}, "marangaberry": {"name": "Maranga Berry", "spritenum": 597, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Dark"}, "num": 688, "gen": 6, "desc": "Raises holder's Sp. Def by 1 stage after it is hit by a special attack. Single use.", "shortDesc": "Raises holder's Sp. Def by 1 stage after it is hit by a special attack. Single use."}, "marshadiumz": {"name": "Marshadium Z", "spritenum": 654, "onTakeItem": false, "zMove": "Soul-Stealing 7-Star Strike", "zMoveFrom": "Spectral Thief", "itemUser": ["Marshadow"], "num": 802, "gen": 7, "isNonstandard": "Past", "desc": "If held by Marshadow with Spectral Thief, it can use Soul-Stealing 7-Star Strike.", "shortDesc": "If held by Marshadow with Spectral Thief, it can use Soul-Stealing 7-Star Strike."}, "masterball": {"name": "Master Ball", "spritenum": 276, "num": 1, "gen": 1, "isPokeball": true, "desc": "The best Ball with the ultimate performance. It will catch any wild Pokemon.", "shortDesc": "The best Ball with the ultimate performance. It will catch any wild Pokemon."}, "masterpieceteacup": {"name": "Masterpiece Teacup", "spritenum": 757, "fling": {"basePower": 80}, "num": 2404, "gen": 9, "desc": "Evolves Poltchageist-Artisan into Sinistcha-Masterpiece when used.", "shortDesc": "Evolves Poltchageist-Artisan into Sinistcha-Masterpiece when used."}, "mawilite": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 598, "megaStone": "Mawile-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 681, "gen": 6, "isNonstandard": "Past", "desc": "If held by a <PERSON><PERSON><PERSON>, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a <PERSON><PERSON><PERSON>, this item allows it to Mega Evolve in battle."}, "meadowplate": {"name": "Meadow Plate", "spritenum": 282, "onPlate": "Grass", "onBasePowerPriority": 15, "forcedForme": "Arceus<PERSON><PERSON>", "num": 301, "gen": 4, "desc": "Holder's Grass-type attacks have 1.2x power. Judgment is Grass type.", "shortDesc": "Holder's Grass-type attacks have 1.2x power. Judgment is Grass type."}, "medichamite": {"name": "Medichamite", "spritenum": 599, "megaStone": "Medicham-Mega", "megaEvolves": "Medicham", "itemUser": ["Medicham"], "num": 665, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Medicham, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Medicham, this item allows it to Mega Evolve in battle."}, "mentalherb": {"name": "Mental Herb", "spritenum": 285, "fling": {"basePower": 10}, "num": 219, "gen": 3, "desc": "Cures holder of Attract, Disable, Encore, Heal Block, Taunt, Torment. Single use.", "shortDesc": "Cures holder of Attract, Disable, Encore, Heal Block, Taunt, Torment. Single use."}, "metagrossite": {"name": "Metagrossite", "spritenum": 618, "megaStone": "Metagross-Mega", "megaEvolves": "Metagross", "itemUser": ["Metagross"], "num": 758, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Metagross, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Metagross, this item allows it to Mega Evolve in battle."}, "metalalloy": {"name": "Metal Alloy", "spritenum": 761, "num": 2482, "gen": 9, "desc": "Evolves Duraludon into Archaludon when used.", "shortDesc": "Evolves Duraludon into Archaludon when used."}, "metalcoat": {"name": "Metal Coat", "spritenum": 286, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 233, "gen": 2, "desc": "Hold<PERSON>'s Steel-type attacks have 1.2x power. Evolves Onix into Steelix and Scyther into Scizor when traded.", "shortDesc": "Holder's Steel-type attacks have 1.2x power."}, "metalpowder": {"name": "Metal Powder", "fling": {"basePower": 10}, "spritenum": 287, "onModifyDefPriority": 2, "itemUser": ["<PERSON><PERSON>"], "num": 257, "gen": 2, "isNonstandard": "Past", "desc": "If held by a Ditto that hasn't Transformed, its Defense is doubled.", "shortDesc": "If held by a Ditto that hasn't Transformed, its Defense is doubled."}, "metronome": {"name": "Metronome", "spritenum": 289, "fling": {"basePower": 30}, "condition": {"onTryMovePriority": -2}, "num": 277, "gen": 4, "desc": "Damage of moves used on consecutive turns is increased. Max 2x after 5 turns.", "shortDesc": "Damage of moves used on consecutive turns is increased. Max 2x after 5 turns."}, "mewniumz": {"name": "Mewnium Z", "spritenum": 658, "onTakeItem": false, "zMove": "Genesis Supernova", "zMoveFrom": "Psychic", "itemUser": ["Mew"], "num": 806, "gen": 7, "isNonstandard": "Past", "desc": "If held by a Mew with Psychic, it can use Genesis Supernova.", "shortDesc": "If held by a Mew with Psychic, it can use Genesis Supernova."}, "mewtwonitex": {"name": "Mewtwonite X", "spritenum": 600, "megaStone": "Mewtwo-Mega-X", "megaEvolves": "Mewtwo", "itemUser": ["Mewtwo"], "num": 662, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Mewtwo, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Mewtwo, this item allows it to Mega Evolve in battle."}, "mewtwonitey": {"name": "Mewtwonite Y", "spritenum": 601, "megaStone": "Mewtwo-Mega-Y", "megaEvolves": "Mewtwo", "itemUser": ["Mewtwo"], "num": 663, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Mewtwo, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Mewtwo, this item allows it to Mega Evolve in battle."}, "micleberry": {"name": "<PERSON><PERSON>", "spritenum": 290, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Rock"}, "condition": {"duration": 2}, "num": 209, "gen": 4, "desc": "Holder's next move has 1.2x accuracy when at 1/4 max HP or less. Single use.", "shortDesc": "Holder's next move has 1.2x accuracy when at 1/4 max HP or less. Single use."}, "mimikiumz": {"name": "Mimikium Z", "spritenum": 688, "onTakeItem": false, "zMove": "Let's Snuggle Forever", "zMoveFrom": "Play Rough", "itemUser": ["Mimikyu", "Mimikyu-Busted", "Mimikyu-Totem", "Mimikyu-Busted-Totem"], "num": 924, "isNonstandard": "Past", "gen": 7, "desc": "If held by a Mimikyu with Play Rough, it can use Let's Snuggle Forever.", "shortDesc": "If held by a Mimikyu with Play Rough, it can use Let's Snuggle Forever."}, "mindplate": {"name": "Mind Plate", "spritenum": 291, "onPlate": "Psychic", "onBasePowerPriority": 15, "forcedForme": "Arceus-Psychic", "num": 307, "gen": 4, "desc": "Holder's Psychic-type attacks have 1.2x power. Judgment is Psychic type.", "shortDesc": "Holder's Psychic-type attacks have 1.2x power. Judgment is Psychic type."}, "miracleseed": {"name": "Miracle Seed", "fling": {"basePower": 30}, "spritenum": 292, "onBasePowerPriority": 15, "num": 239, "gen": 2, "desc": "Holder's Grass-type attacks have 1.2x power.", "shortDesc": "Holder's Grass-type attacks have 1.2x power."}, "mirrorherb": {"name": "Mirror Herb", "spritenum": 748, "fling": {"basePower": 30}, "num": 1883, "gen": 9, "desc": "When an opposing Pokemon raises a stat stage, the holder copies it. Single use.", "shortDesc": "When an opposing Pokemon raises a stat stage, the holder copies it. Single use."}, "mistyseed": {"name": "<PERSON>", "spritenum": 666, "fling": {"basePower": 10}, "boosts": {"spd": 1}, "num": 883, "gen": 7, "desc": "If the terrain is Misty Terrain, raises holder's Sp. Def by 1 stage. Single use.", "shortDesc": "If the terrain is Misty Terrain, raises holder's Sp. Def by 1 stage. Single use."}, "moonball": {"name": "<PERSON> Ball", "spritenum": 294, "num": 498, "gen": 2, "isPokeball": true, "desc": "A Poke Ball for catching <PERSON><PERSON><PERSON> that evolve using the Moon Stone.", "shortDesc": "A Poke Ball for catching <PERSON><PERSON><PERSON> that evolve using the Moon Stone."}, "moonstone": {"name": "<PERSON> Stone", "spritenum": 295, "fling": {"basePower": 30}, "num": 81, "gen": 1, "desc": "Evolves Nidorina into Nidoqueen, <PERSON><PERSON><PERSON> into Nidoking, <PERSON>lefair<PERSON> into Clefable, <PERSON><PERSON>lypuff into Wigglytuff, <PERSON><PERSON> into Delcatty, and <PERSON><PERSON> into <PERSON><PERSON><PERSON> when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "muscleband": {"name": "Muscle Band", "spritenum": 297, "fling": {"basePower": 10}, "onBasePowerPriority": 16, "num": 266, "gen": 4, "desc": "Holder's physical attacks have 1.1x power.", "shortDesc": "Holder's physical attacks have 1.1x power."}, "mysticwater": {"name": "Mystic Water", "spritenum": 300, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 243, "gen": 2, "desc": "Holder's Water-type attacks have 1.2x power.", "shortDesc": "Holder's Water-type attacks have 1.2x power."}, "nanabberry": {"name": "<PERSON><PERSON>", "spritenum": 302, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Water"}, "onEat": false, "num": 166, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "nestball": {"name": "Nest Ball", "spritenum": 303, "num": 8, "gen": 3, "isPokeball": true, "desc": "A Poke Ball that works especially well on weaker Pokemon in the wild.", "shortDesc": "A Poke Ball that works especially well on weaker Pokemon in the wild."}, "netball": {"name": "Net Ball", "spritenum": 304, "num": 6, "gen": 3, "isPokeball": true, "desc": "A Poke Ball that works especially well on Water- and Bug-type Pokemon.", "shortDesc": "A Poke Ball that works especially well on Water- and Bug-type Pokemon."}, "nevermeltice": {"name": "Never-Melt Ice", "spritenum": 305, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 246, "gen": 2, "desc": "Holder's Ice-type attacks have 1.2x power.", "shortDesc": "Holder's Ice-type attacks have 1.2x power."}, "nomelberry": {"name": "<PERSON><PERSON>", "spritenum": 306, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Dragon"}, "onEat": false, "num": 178, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "normalgem": {"name": "Normal Gem", "spritenum": 307, "isGem": true, "num": 564, "gen": 5, "desc": "Holder's first successful Normal-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Normal-type attack will have 1.3x power. Single use."}, "normaliumz": {"name": "Normalium Z", "spritenum": 631, "onTakeItem": false, "zMove": true, "zMoveType": "Normal", "num": 776, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Normal move, this item allows it to use a Normal Z-Move.", "shortDesc": "If holder has a Normal move, this item allows it to use a Normal Z-Move."}, "occaberry": {"name": "<PERSON><PERSON>", "spritenum": 311, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Fire"}, "num": 184, "gen": 4, "desc": "Halves damage taken from a supereffective Fire-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Fire-type attack. Single use."}, "oddincense": {"name": "Odd Incense", "spritenum": 312, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 314, "gen": 4, "isNonstandard": "Past", "desc": "Holder's Psychic-type attacks have 1.2x power.", "shortDesc": "Holder's Psychic-type attacks have 1.2x power."}, "oldamber": {"name": "Old Amber", "spritenum": 314, "fling": {"basePower": 100}, "num": 103, "gen": 3, "isNonstandard": "Past", "desc": "Can be revived into Aerodactyl.", "shortDesc": "Can be revived into Aerodactyl."}, "oranberry": {"name": "<PERSON><PERSON>", "spritenum": 319, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Poison"}, "num": 155, "gen": 3, "desc": "Restores 10 HP when at 1/2 max HP or less. Single use.", "shortDesc": "Restores 10 HP when at 1/2 max HP or less. Single use."}, "ovalstone": {"name": "Oval Stone", "spritenum": 321, "fling": {"basePower": 80}, "num": 110, "gen": 4, "desc": "Evolves Happ<PERSON><PERSON> into Chansey when held and leveled up during the day.", "shortDesc": "Evolves Happ<PERSON><PERSON> into Chansey when held and leveled up during the day."}, "pamtreberry": {"name": "Pam<PERSON> Berry", "spritenum": 323, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Steel"}, "onEat": false, "num": 180, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "parkball": {"name": "Park Ball", "spritenum": 325, "num": 500, "gen": 4, "isPokeball": true, "isNonstandard": "Unobtainable", "desc": "A special Poke Ball for the Pal Park.", "shortDesc": "A special Poke Ball for the Pal Park."}, "passhoberry": {"name": "Passho Berry", "spritenum": 329, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Water"}, "num": 185, "gen": 4, "desc": "Halves damage taken from a supereffective Water-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Water-type attack. Single use."}, "payapaberry": {"name": "Payapa Berry", "spritenum": 330, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Psychic"}, "num": 193, "gen": 4, "desc": "Halves damage taken from a supereffective Psychic-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Psychic-type attack. Single use."}, "pechaberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 333, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Electric"}, "num": 151, "gen": 3, "desc": "Holder is cured if it is poisoned. Single use.", "shortDesc": "Holder is cured if it is poisoned. Single use."}, "persimberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 334, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Ground"}, "num": 156, "gen": 3, "desc": "Holder is cured if it is confused. Single use.", "shortDesc": "Holder is cured if it is confused. Single use."}, "petayaberry": {"name": "<PERSON><PERSON>", "spritenum": 335, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Poison"}, "num": 204, "gen": 3, "desc": "Raises holder's Sp. Atk by 1 stage when at 1/4 max HP or less. Single use.", "shortDesc": "Raises holder's Sp. Atk by 1 stage when at 1/4 max HP or less. Single use."}, "pidgeotite": {"name": "Pidgeotite", "spritenum": 622, "megaStone": "Pidgeot-Mega", "megaEvolves": "Pidgeot", "itemUser": ["Pidgeot"], "num": 762, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Pidgeot, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Pidgeot, this item allows it to Mega Evolve in battle."}, "pikaniumz": {"name": "Pikanium Z", "spritenum": 649, "onTakeItem": false, "zMove": "Catastropika", "zMoveFrom": "Volt Tackle", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 794, "gen": 7, "isNonstandard": "Past", "desc": "If held by a Pikachu with Volt Tackle, it can use Catastropika.", "shortDesc": "If held by a Pikachu with Volt Tackle, it can use Catastropika."}, "pikashuniumz": {"name": "Pikashunium Z", "spritenum": 659, "onTakeItem": false, "zMove": "10,000,000 Volt Thunderbolt", "zMoveFrom": "Thunderbolt", "itemUser": ["<PERSON><PERSON><PERSON>-Original", "Pi<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "Pikachu-Sinno<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pikachu-<PERSON>ola", "<PERSON><PERSON><PERSON>-Partner"], "num": 836, "isNonstandard": "Past", "gen": 7, "desc": "If held by cap <PERSON><PERSON><PERSON> with Thunderbolt, it can use 10,000,000 Volt Thunderbolt.", "shortDesc": "If held by cap <PERSON><PERSON><PERSON> with Thunderbolt, it can use 10,000,000 Volt Thunderbolt."}, "pinapberry": {"name": "Pinap Berry", "spritenum": 337, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Grass"}, "onEat": false, "num": 168, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "pinsirite": {"name": "Pinsirite", "spritenum": 602, "megaStone": "Pinsir-Mega", "megaEvolves": "Pinsir", "itemUser": ["Pinsir"], "num": 671, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Pinsir, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Pinsir, this item allows it to Mega Evolve in battle."}, "pixieplate": {"name": "Pixie Plate", "spritenum": 610, "onPlate": "Fairy", "onBasePowerPriority": 15, "forcedForme": "<PERSON><PERSON>-<PERSON>", "num": 644, "gen": 6, "desc": "Holder's Fairy-type attacks have 1.2x power. Judgment is Fairy type.", "shortDesc": "Holder's Fairy-type attacks have 1.2x power. Judgment is Fairy type."}, "plumefossil": {"name": "Plume Fossil", "spritenum": 339, "fling": {"basePower": 100}, "num": 573, "gen": 5, "isNonstandard": "Past", "desc": "Can be revived into Archen.", "shortDesc": "Can be revived into Archen."}, "poisonbarb": {"name": "<PERSON>ison Barb", "spritenum": 343, "fling": {"basePower": 70, "status": "psn"}, "onBasePowerPriority": 15, "num": 245, "gen": 2, "desc": "Holder's Poison-type attacks have 1.2x power.", "shortDesc": "Holder's Poison-type attacks have 1.2x power."}, "poisongem": {"name": "Poison Gem", "spritenum": 344, "isGem": true, "num": 554, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Poison-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Poison-type attack will have 1.3x power. Single use."}, "poisonmemory": {"name": "Poison Memory", "spritenum": 670, "onMemory": "Poison", "forcedForme": "<PERSON><PERSON>-<PERSON><PERSON>", "itemUser": ["<PERSON><PERSON>-<PERSON><PERSON>"], "num": 906, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Poison type.", "shortDesc": "Holder's Multi-Attack is Poison type."}, "poisoniumz": {"name": "Poisonium Z", "spritenum": 638, "onPlate": "Poison", "onTakeItem": false, "zMove": true, "zMoveType": "Poison", "forcedForme": "Arceus-Poison", "num": 783, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Poison move, this item allows it to use a Poison Z-Move.", "shortDesc": "If holder has a Poison move, this item allows it to use a Poison Z-Move."}, "pokeball": {"name": "Poke Ball", "spritenum": 345, "num": 4, "gen": 1, "isPokeball": true, "desc": "A device for catching wild Pokemon. It is designed as a capsule system.", "shortDesc": "A device for catching wild Pokemon. It is designed as a capsule system."}, "pomegberry": {"name": "Pomeg Berry", "spritenum": 351, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Ice"}, "onEat": false, "num": 169, "gen": 3, "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "poweranklet": {"name": "Power Anklet", "spritenum": 354, "ignoreKlutz": true, "fling": {"basePower": 70}, "num": 293, "gen": 4, "desc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect.", "shortDesc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect."}, "powerband": {"name": "Power Band", "spritenum": 355, "ignoreKlutz": true, "fling": {"basePower": 70}, "num": 292, "gen": 4, "desc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect.", "shortDesc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect."}, "powerbelt": {"name": "Power Belt", "spritenum": 356, "ignoreKlutz": true, "fling": {"basePower": 70}, "num": 290, "gen": 4, "desc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect.", "shortDesc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect."}, "powerbracer": {"name": "Power Bracer", "spritenum": 357, "ignoreKlutz": true, "fling": {"basePower": 70}, "num": 289, "gen": 4, "desc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect.", "shortDesc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect."}, "powerherb": {"name": "Power Herb", "spritenum": 358, "fling": {"basePower": 10}, "num": 271, "gen": 4, "desc": "Holder's two-turn moves complete in one turn (except <PERSON> Drop). Single use.", "shortDesc": "Holder's two-turn moves complete in one turn (except <PERSON> Drop). Single use."}, "powerlens": {"name": "Power Lens", "spritenum": 359, "ignoreKlutz": true, "fling": {"basePower": 70}, "num": 291, "gen": 4, "desc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect.", "shortDesc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect."}, "powerweight": {"name": "Power Weight", "spritenum": 360, "ignoreKlutz": true, "fling": {"basePower": 70}, "num": 294, "gen": 4, "desc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect.", "shortDesc": "Holder's Speed is halved. The Klutz Ability does not ignore this effect."}, "premierball": {"name": "Premier Ball", "spritenum": 363, "num": 12, "gen": 3, "isPokeball": true, "desc": "A rare Poke Ball that has been crafted to commemorate an event.", "shortDesc": "A rare Poke Ball that has been crafted to commemorate an event."}, "primariumz": {"name": "Primarium Z", "spritenum": 652, "onTakeItem": false, "zMove": "Oceanic Operetta", "zMoveFrom": "Sparkling Aria", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 800, "gen": 7, "isNonstandard": "Past", "desc": "If held by a Primarina with Sparkling Aria, it can use Oceanic Operetta.", "shortDesc": "If held by a Primarina with Sparkling Aria, it can use Oceanic Operetta."}, "prismscale": {"name": "Prism Scale", "spritenum": 365, "fling": {"basePower": 30}, "num": 537, "gen": 5, "desc": "Evolves Feebas into Milotic when traded.", "shortDesc": "Evolves Feebas into Milotic when traded."}, "protectivepads": {"name": "Protective Pads", "spritenum": 663, "fling": {"basePower": 30}, "num": 880, "gen": 7, "desc": "Holder's moves are protected from adverse contact effects, except Pickpocket.", "shortDesc": "Holder's moves are protected from adverse contact effects, except Pickpocket."}, "protector": {"name": "Protector", "spritenum": 367, "fling": {"basePower": 80}, "num": 321, "gen": 4, "desc": "Evolves Rhydon into Rhyperior when traded.", "shortDesc": "Evolves Rhydon into Rhyperior when traded."}, "psychicgem": {"name": "Psychic Gem", "spritenum": 369, "isGem": true, "num": 557, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Psychic-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Psychic-type attack will have 1.3x power. Single use."}, "psychicmemory": {"name": "Psychic Memory", "spritenum": 680, "onMemory": "Psychic", "forcedForme": "Silvally-Psychic", "itemUser": ["Silvally-Psychic"], "num": 916, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Psychic type.", "shortDesc": "Holder's Multi-Attack is Psychic type."}, "psychicseed": {"name": "Psychic Seed", "spritenum": 665, "fling": {"basePower": 10}, "boosts": {"spd": 1}, "num": 882, "gen": 7, "desc": "If the terrain is Psychic Terrain, raises holder's Sp. Def by 1 stage. Single use.", "shortDesc": "If the terrain is Psychic Terrain, raises holder's Sp. Def by 1 stage. Single use."}, "psychiumz": {"name": "Psychium Z", "spritenum": 641, "onPlate": "Psychic", "onTakeItem": false, "zMove": true, "zMoveType": "Psychic", "forcedForme": "Arceus-Psychic", "num": 786, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Psychic move, this item allows it to use a Psychic Z-Move.", "shortDesc": "If holder has a Psychic move, this item allows it to use a Psychic Z-Move."}, "punchingglove": {"name": "Punching Glove", "spritenum": 749, "fling": {"basePower": 30}, "onBasePowerPriority": 23, "onModifyMovePriority": 1, "num": 1884, "gen": 9, "desc": "Holder's punch-based attacks have 1.1x power and do not make contact.", "shortDesc": "Holder's punch-based attacks have 1.1x power and do not make contact."}, "qualotberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 371, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Poison"}, "onEat": false, "num": 171, "gen": 3, "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "quickball": {"name": "Quick Ball", "spritenum": 372, "num": 15, "gen": 4, "isPokeball": true, "desc": "A Poke Ball that provides a better catch rate at the start of a wild encounter.", "shortDesc": "A Poke Ball that provides a better catch rate at the start of a wild encounter."}, "quickclaw": {"onFractionalPriorityPriority": -2, "name": "Quick Claw", "spritenum": 373, "fling": {"basePower": 80}, "num": 217, "gen": 2, "desc": "Each turn, holder has a 20% chance to move first in its priority bracket.", "shortDesc": "Each turn, holder has a 20% chance to move first in its priority bracket."}, "quickpowder": {"name": "<PERSON> Powder", "spritenum": 374, "fling": {"basePower": 10}, "itemUser": ["<PERSON><PERSON>"], "num": 274, "gen": 4, "isNonstandard": "Past", "desc": "If held by a Ditto that hasn't Transformed, its Speed is doubled.", "shortDesc": "If held by a Ditto that hasn't Transformed, its Speed is doubled."}, "rabutaberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 375, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Ghost"}, "onEat": false, "num": 177, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "rarebone": {"name": "Rare Bone", "spritenum": 379, "fling": {"basePower": 100}, "num": 106, "gen": 4, "desc": "No competitive use other than when used with Fling.", "shortDesc": "No competitive use other than when used with Fling."}, "rawstberry": {"name": "Rawst <PERSON>", "spritenum": 381, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Grass"}, "num": 152, "gen": 3, "desc": "Holder is cured if it is burned. Single use.", "shortDesc": "Holder is cured if it is burned. Single use."}, "razorclaw": {"name": "<PERSON><PERSON>", "spritenum": 382, "fling": {"basePower": 80}, "num": 326, "gen": 4, "desc": "Holder's critical hit ratio is raised by 1 stage. Evolves Sneasel into Weavile when held and leveled up during the night.", "shortDesc": "Holder's critical hit ratio is raised by 1 stage."}, "razorfang": {"name": "<PERSON><PERSON>", "spritenum": 383, "fling": {"basePower": 30, "volatileStatus": "flinch"}, "onModifyMovePriority": -1, "num": 327, "gen": 4, "desc": "Holder's attacks without a chance to make the target flinch gain a 10% chance to make the target flinch. <PERSON><PERSON>lves <PERSON><PERSON><PERSON> into Gliscor when held and leveled up during the night.", "shortDesc": "Holder's attacks without a chance to flinch gain a 10% chance to flinch."}, "razzberry": {"name": "Razz <PERSON>", "spritenum": 384, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Steel"}, "onEat": false, "num": 164, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "reapercloth": {"name": "Reaper Cloth", "spritenum": 385, "fling": {"basePower": 10}, "num": 325, "gen": 4, "desc": "Evolves Dusclops into Dusknoir when traded.", "shortDesc": "Evolves Dusclops into Dusknoir when traded."}, "redcard": {"name": "Red Card", "spritenum": 387, "fling": {"basePower": 10}, "num": 542, "gen": 5, "desc": "If holder survives a hit, attacker is forced to switch to a random ally. Single use.", "shortDesc": "If holder survives a hit, attacker is forced to switch to a random ally. Single use."}, "redorb": {"name": "Red Orb", "spritenum": 390, "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 534, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Groudon, this item triggers its Primal Reversion in battle.", "shortDesc": "If held by a Groudon, this item triggers its Primal Reversion in battle."}, "repeatball": {"name": "Repeat Ball", "spritenum": 401, "num": 9, "gen": 3, "isPokeball": true, "desc": "A Poke Ball that works well on Pokemon species that were previously caught.", "shortDesc": "A Poke Ball that works well on Pokemon species that were previously caught."}, "ribbonsweet": {"name": "Ribbon Sweet", "spritenum": 710, "fling": {"basePower": 10}, "num": 1115, "gen": 8, "desc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around.", "shortDesc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around."}, "rindoberry": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 409, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Grass"}, "num": 187, "gen": 4, "desc": "Halves damage taken from a supereffective Grass-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Grass-type attack. Single use."}, "ringtarget": {"name": "Ring Target", "spritenum": 410, "fling": {"basePower": 10}, "onNegateImmunity": false, "num": 543, "gen": 5, "desc": "The holder's type immunities granted solely by its typing are negated.", "shortDesc": "The holder's type immunities granted solely by its typing are negated."}, "rockgem": {"name": "Rock Gem", "spritenum": 415, "isGem": true, "num": 559, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Rock-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Rock-type attack will have 1.3x power. Single use."}, "rockincense": {"name": "Rock Incense", "spritenum": 416, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 315, "gen": 4, "isNonstandard": "Past", "desc": "Holder's Rock-type attacks have 1.2x power.", "shortDesc": "Holder's Rock-type attacks have 1.2x power."}, "rockmemory": {"name": "Rock Memory", "spritenum": 672, "onMemory": "Rock", "forcedForme": "<PERSON><PERSON>-Rock", "itemUser": ["<PERSON><PERSON>-Rock"], "num": 908, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Rock type.", "shortDesc": "Holder's Multi-Attack is Rock type."}, "rockiumz": {"name": "Rockium Z", "spritenum": 643, "onPlate": "Rock", "onTakeItem": false, "zMove": true, "zMoveType": "Rock", "forcedForme": "Arceus-Rock", "num": 788, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Rock move, this item allows it to use a Rock Z-Move.", "shortDesc": "If holder has a Rock move, this item allows it to use a Rock Z-Move."}, "rockyhelmet": {"name": "<PERSON>", "spritenum": 417, "fling": {"basePower": 60}, "onDamagingHitOrder": 2, "num": 540, "gen": 5, "desc": "If holder is hit by a contact move, the attacker loses 1/6 of its max HP.", "shortDesc": "If holder is hit by a contact move, the attacker loses 1/6 of its max HP."}, "roomservice": {"name": "Room Service", "spritenum": 717, "fling": {"basePower": 100}, "boosts": {"spe": -1}, "num": 1122, "gen": 8, "desc": "If Trick Room is active, the holder's Speed is lowered by 1 stage. Single use.", "shortDesc": "If Trick Room is active, the holder's Speed is lowered by 1 stage. Single use."}, "rootfossil": {"name": "Root Fossil", "spritenum": 418, "fling": {"basePower": 100}, "num": 99, "gen": 3, "isNonstandard": "Past", "desc": "Can be revived into <PERSON><PERSON>.", "shortDesc": "Can be revived into <PERSON><PERSON>."}, "roseincense": {"name": "Rose Incense", "spritenum": 419, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 318, "gen": 4, "isNonstandard": "Past", "desc": "Holder's Grass-type attacks have 1.2x power.", "shortDesc": "Holder's Grass-type attacks have 1.2x power."}, "roseliberry": {"name": "<PERSON><PERSON>", "spritenum": 603, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Fairy"}, "num": 686, "gen": 6, "desc": "Halves damage taken from a supereffective Fairy-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Fairy-type attack. Single use."}, "rowapberry": {"name": "Rowap Berry", "spritenum": 420, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Dark"}, "num": 212, "gen": 4, "desc": "If holder is hit by a special move, attacker loses 1/8 of its max HP. Single use.", "shortDesc": "If holder is hit by a special move, attacker loses 1/8 of its max HP. Single use."}, "rustedshield": {"name": "Rusted Shield", "spritenum": 699, "itemUser": ["Zamazenta-Crowned"], "num": 1104, "gen": 8, "desc": "If held by a Zamazenta, this item changes its forme to Crowned Shield.", "shortDesc": "If held by a Zamazenta, this item changes its forme to Crowned Shield."}, "rustedsword": {"name": "Rusted Sword", "spritenum": 698, "itemUser": ["Zacian-Crowned"], "num": 1103, "gen": 8, "desc": "If held by a Zacian, this item changes its forme to Crowned Sword.", "shortDesc": "If held by a Zacian, this item changes its forme to Crowned Sword."}, "sablenite": {"name": "Sablenite", "spritenum": 614, "megaStone": "Sableye-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 754, "gen": 6, "isNonstandard": "Past", "desc": "If held by a <PERSON>bleye, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a <PERSON>bleye, this item allows it to Mega Evolve in battle."}, "sachet": {"name": "Sachet", "spritenum": 691, "fling": {"basePower": 80}, "num": 647, "gen": 6, "isNonstandard": "Past", "desc": "Evolves Spritzee into Aromatisse when traded.", "shortDesc": "Evolves Spritzee into Aromatisse when traded."}, "safariball": {"name": "Safari Ball", "spritenum": 425, "num": 5, "gen": 1, "isPokeball": true, "desc": "A special Poke Ball that is used only in the Safari Zone and Great Marsh.", "shortDesc": "A special Poke Ball that is used only in the Safari Zone and Great Marsh."}, "safetygoggles": {"name": "Safety Goggles", "spritenum": 604, "fling": {"basePower": 80}, "num": 650, "gen": 6, "desc": "Holder is immune to powder moves and damage from Sandstorm or Hail.", "shortDesc": "Holder is immune to powder moves and damage from Sandstorm or Hail."}, "sailfossil": {"name": "Sail Fossil", "spritenum": 695, "fling": {"basePower": 100}, "num": 711, "gen": 6, "isNonstandard": "Past", "desc": "Can be revived into Amaura.", "shortDesc": "Can be revived into Amaura."}, "salacberry": {"name": "<PERSON><PERSON>", "spritenum": 426, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Fighting"}, "num": 203, "gen": 3, "desc": "Raises holder's Speed by 1 stage when at 1/4 max HP or less. Single use.", "shortDesc": "Raises holder's Speed by 1 stage when at 1/4 max HP or less. Single use."}, "salamencite": {"name": "Salamencite", "spritenum": 627, "megaStone": "Salamence-Mega", "megaEvolves": "Salamence", "itemUser": ["Salamence"], "num": 769, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Salamence, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Salamence, this item allows it to Mega Evolve in battle."}, "sceptilite": {"name": "Sceptilite", "spritenum": 613, "megaStone": "Sceptile-Mega", "megaEvolves": "Sceptile", "itemUser": ["Sceptile"], "num": 753, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Sceptile, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Sceptile, this item allows it to Mega Evolve in battle."}, "scizorite": {"name": "Scizorite", "spritenum": 605, "megaStone": "Scizor-Mega", "megaEvolves": "Scizor", "itemUser": ["Scizor"], "num": 670, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Scizor, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Scizor, this item allows it to Mega Evolve in battle."}, "scopelens": {"name": "Scope Lens", "spritenum": 429, "fling": {"basePower": 30}, "num": 232, "gen": 2, "desc": "Holder's critical hit ratio is raised by 1 stage.", "shortDesc": "Holder's critical hit ratio is raised by 1 stage."}, "seaincense": {"name": "Sea Incense", "spritenum": 430, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 254, "gen": 3, "isNonstandard": "Past", "desc": "Holder's Water-type attacks have 1.2x power.", "shortDesc": "Holder's Water-type attacks have 1.2x power."}, "sharpbeak": {"name": "<PERSON>ak", "spritenum": 436, "fling": {"basePower": 50}, "onBasePowerPriority": 15, "num": 244, "gen": 2, "desc": "Holder's Flying-type attacks have 1.2x power.", "shortDesc": "Holder's Flying-type attacks have 1.2x power."}, "sharpedonite": {"name": "Sharpedonite", "spritenum": 619, "megaStone": "Sharpedo-Mega", "megaEvolves": "<PERSON><PERSON>", "itemUser": ["<PERSON><PERSON>"], "num": 759, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Sharpedo, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Sharpedo, this item allows it to Mega Evolve in battle."}, "shedshell": {"name": "Shed Shell", "spritenum": 437, "fling": {"basePower": 10}, "onTrapPokemonPriority": -10, "num": 295, "gen": 4, "desc": "Holder may switch out even when trapped by another Pokemon, or by Ingrain.", "shortDesc": "Holder may switch out even when trapped by another Pokemon, or by Ingrain."}, "shellbell": {"name": "Shell Bell", "spritenum": 438, "fling": {"basePower": 30}, "onAfterMoveSecondarySelfPriority": -1, "num": 253, "gen": 3, "desc": "After an attack, holder gains 1/8 of the damage in HP dealt to other Pokemon.", "shortDesc": "After an attack, holder gains 1/8 of the damage in HP dealt to other Pokemon."}, "shinystone": {"name": "<PERSON><PERSON>", "spritenum": 439, "fling": {"basePower": 80}, "num": 107, "gen": 4, "desc": "Evolves Togetic into Togekiss, Rose<PERSON> into Roserade, Minccino into Cinccino, and Floette into Florges when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "shockdrive": {"name": "Shock Drive", "spritenum": 442, "onDrive": "Electric", "forcedForme": "Genesect-Shock", "itemUser": ["Genesect-Shock"], "num": 117, "gen": 5, "isNonstandard": "Past", "desc": "Holder's Techno Blast is Electric type.", "shortDesc": "Holder's Techno Blast is Electric type."}, "shucaberry": {"name": "Shu<PERSON> Berry", "spritenum": 443, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Ground"}, "num": 191, "gen": 4, "desc": "Halves damage taken from a supereffective Ground-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Ground-type attack. Single use."}, "silkscarf": {"name": "<PERSON>", "spritenum": 444, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 251, "gen": 3, "desc": "Holder's Normal-type attacks have 1.2x power.", "shortDesc": "Holder's Normal-type attacks have 1.2x power."}, "silverpowder": {"name": "Silver Powder", "spritenum": 447, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 222, "gen": 2, "desc": "Holder's Bug-type attacks have 1.2x power.", "shortDesc": "Holder's Bug-type attacks have 1.2x power."}, "sitrusberry": {"name": "<PERSON><PERSON>", "spritenum": 448, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Psychic"}, "num": 158, "gen": 3, "desc": "Restores 1/4 max HP when at 1/2 max HP or less. Single use.", "shortDesc": "Restores 1/4 max HP when at 1/2 max HP or less. Single use."}, "skullfossil": {"name": "Skull Fossil", "spritenum": 449, "fling": {"basePower": 100}, "num": 105, "gen": 4, "isNonstandard": "Past", "desc": "Can be revived into Cranidos.", "shortDesc": "Can be revived into Cranidos."}, "skyplate": {"name": "Sky Plate", "spritenum": 450, "onPlate": "Flying", "onBasePowerPriority": 15, "forcedForme": "Arceus-Flying", "num": 306, "gen": 4, "desc": "Holder's Flying-type attacks have 1.2x power. Judgment is Flying type.", "shortDesc": "Holder's Flying-type attacks have 1.2x power. Judgment is Flying type."}, "slowbronite": {"name": "Slowbronite", "spritenum": 620, "megaStone": "Slowbro-Mega", "megaEvolves": "Slowbro", "itemUser": ["Slowbro"], "num": 760, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Slowbro, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Slowbro, this item allows it to Mega Evolve in battle."}, "smoothrock": {"name": "Smooth Rock", "spritenum": 453, "fling": {"basePower": 10}, "num": 283, "gen": 4, "desc": "Holder's use of Sandstorm lasts 8 turns instead of 5.", "shortDesc": "Holder's use of Sandstorm lasts 8 turns instead of 5."}, "snorliumz": {"name": "Snorlium Z", "spritenum": 656, "onTakeItem": false, "zMove": "Pulverizing Pancake", "zMoveFrom": "Giga Impact", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 804, "gen": 7, "isNonstandard": "Past", "desc": "If held by a Snorlax with Giga Impact, it can use Pulverizing Pancake.", "shortDesc": "If held by a Snorlax with Giga Impact, it can use Pulverizing Pancake."}, "snowball": {"name": "Snowball", "spritenum": 606, "fling": {"basePower": 30}, "boosts": {"atk": 1}, "num": 649, "gen": 6, "desc": "Raises holder's Attack by 1 if hit by an Ice-type attack. Single use.", "shortDesc": "Raises holder's Attack by 1 if hit by an Ice-type attack. Single use."}, "softsand": {"name": "Soft Sand", "spritenum": 456, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 237, "gen": 2, "desc": "Holder's Ground-type attacks have 1.2x power.", "shortDesc": "Holder's Ground-type attacks have 1.2x power."}, "solganiumz": {"name": "Solganium Z", "spritenum": 685, "onTakeItem": false, "zMove": "Searing Sunraze Smash", "zMoveFrom": "Sunsteel Strike", "itemUser": ["Solgaleo", "Necrozma-Dusk-Mane"], "num": 921, "gen": 7, "isNonstandard": "Past", "desc": "Solgaleo or Dusk Mane Necrozma with Sunsteel Strike can use a special Z-Move.", "shortDesc": "Solgaleo or Dusk Mane Necrozma with Sunsteel Strike can use a special Z-Move."}, "souldew": {"name": "Soul Dew", "spritenum": 459, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "itemUser": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "num": 225, "gen": 3, "desc": "If held by a Latias/Latios, its Dragon- and Psychic-type moves have 1.2x power.", "shortDesc": "If held by a Latias/Latios, its Dragon- and Psychic-type moves have 1.2x power."}, "spelltag": {"name": "Spell Tag", "spritenum": 461, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 247, "gen": 2, "desc": "Holder's Ghost-type attacks have 1.2x power.", "shortDesc": "Holder's Ghost-type attacks have 1.2x power."}, "spelonberry": {"name": "Spelon Berry", "spritenum": 462, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Dark"}, "onEat": false, "num": 179, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "splashplate": {"name": "Splash Plate", "spritenum": 463, "onPlate": "Water", "onBasePowerPriority": 15, "forcedForme": "Arceus-Water", "num": 299, "gen": 4, "desc": "Holder's Water-type attacks have 1.2x power. Judgment is Water type.", "shortDesc": "Holder's Water-type attacks have 1.2x power. Judgment is Water type."}, "spookyplate": {"name": "Spooky Plate", "spritenum": 464, "onPlate": "Ghost", "onBasePowerPriority": 15, "forcedForme": "Arceus-Ghost", "num": 310, "gen": 4, "desc": "Holder's Ghost-type attacks have 1.2x power. Judgment is Ghost type.", "shortDesc": "Holder's Ghost-type attacks have 1.2x power. Judgment is Ghost type."}, "sportball": {"name": "Sport Ball", "spritenum": 465, "num": 499, "gen": 2, "isPokeball": true, "desc": "A special Poke Ball for the Bug-Catching Contest.", "shortDesc": "A special Poke Ball for the Bug-Catching Contest."}, "starfberry": {"name": "<PERSON><PERSON>", "spritenum": 472, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Psychic"}, "num": 207, "gen": 3, "desc": "Raises a random stat by 2 when at 1/4 max HP or less (not acc/eva). Single use.", "shortDesc": "Raises a random stat by 2 when at 1/4 max HP or less (not acc/eva). Single use."}, "starsweet": {"name": "Star Sweet", "spritenum": 709, "fling": {"basePower": 10}, "num": 1114, "gen": 8, "desc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around.", "shortDesc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around."}, "steelixite": {"name": "Steelixite", "spritenum": 621, "megaStone": "Steelix-Mega", "megaEvolves": "Steelix", "itemUser": ["Steelix"], "num": 761, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Steelix, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Steelix, this item allows it to Mega Evolve in battle."}, "steelgem": {"name": "Steel Gem", "spritenum": 473, "isGem": true, "num": 563, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Steel-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Steel-type attack will have 1.3x power. Single use."}, "steelmemory": {"name": "Steel Memory", "spritenum": 675, "onMemory": "Steel", "forcedForme": "<PERSON><PERSON>-Steel", "itemUser": ["<PERSON><PERSON>-Steel"], "num": 911, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Steel type.", "shortDesc": "Holder's Multi-Attack is Steel type."}, "steeliumz": {"name": "Steelium Z", "spritenum": 647, "onPlate": "Steel", "onTakeItem": false, "zMove": true, "zMoveType": "Steel", "forcedForme": "Arceus-Steel", "num": 792, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Steel move, this item allows it to use a Steel Z-Move.", "shortDesc": "If holder has a Steel move, this item allows it to use a Steel Z-Move."}, "stick": {"name": "Stick", "fling": {"basePower": 60}, "spritenum": 475, "itemUser": ["Farfetch’d"], "num": 259, "gen": 2, "isNonstandard": "Past", "desc": "If held by a Farfetch’d, its critical hit ratio is raised by 2 stages.", "shortDesc": "If held by a Farfetch’d, its critical hit ratio is raised by 2 stages."}, "stickybarb": {"name": "<PERSON><PERSON>", "spritenum": 476, "fling": {"basePower": 80}, "onResidualOrder": 28, "onResidualSubOrder": 3, "num": 288, "gen": 4, "desc": "Each turn, holder loses 1/8 max HP. An attacker making contact can receive it.", "shortDesc": "Each turn, holder loses 1/8 max HP. An attacker making contact can receive it."}, "stoneplate": {"name": "Stone Plate", "spritenum": 477, "onPlate": "Rock", "onBasePowerPriority": 15, "forcedForme": "Arceus-Rock", "num": 309, "gen": 4, "desc": "Holder's Rock-type attacks have 1.2x power. Judgment is Rock type.", "shortDesc": "Holder's Rock-type attacks have 1.2x power. Judgment is Rock type."}, "strangeball": {"name": "<PERSON>", "spritenum": 308, "num": 1785, "gen": 8, "isPokeball": true, "isNonstandard": "Unobtainable"}, "strawberrysweet": {"name": "Strawberry Sweet", "spritenum": 704, "fling": {"basePower": 10}, "num": 1109, "gen": 8, "desc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around.", "shortDesc": "Evolves Milcery into <PERSON><PERSON><PERSON> when held and spun around."}, "sunstone": {"name": "Sun Stone", "spritenum": 480, "fling": {"basePower": 30}, "num": 80, "gen": 2, "desc": "Evolves Gloom into Bellossom, Sunkern into Sunflora, <PERSON><PERSON> into Whimsicott, <PERSON><PERSON><PERSON> into Lilligant, and Helioptile into Heliolisk when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "swampertite": {"name": "Swampertite", "spritenum": 612, "megaStone": "<PERSON><PERSON>-<PERSON>", "megaEvolves": "<PERSON><PERSON>", "itemUser": ["<PERSON><PERSON>"], "num": 752, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Swampert, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Swampert, this item allows it to Mega Evolve in battle."}, "sweetapple": {"name": "Sweet Apple", "spritenum": 711, "fling": {"basePower": 30}, "num": 1116, "gen": 8, "desc": "Evolves Applin into Appletun when used.", "shortDesc": "Evolves Applin into Appletun when used."}, "syrupyapple": {"name": "Syrupy Apple", "spritenum": 755, "fling": {"basePower": 30}, "num": 2402, "gen": 9, "desc": "Evolves Applin into Dipplin when used.", "shortDesc": "Evolves Applin into Dipplin when used."}, "tamatoberry": {"name": "Tamato Berry", "spritenum": 486, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Psychic"}, "onEat": false, "num": 174, "gen": 3, "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "tangaberry": {"name": "<PERSON><PERSON>", "spritenum": 487, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Bug"}, "num": 194, "gen": 4, "desc": "Halves damage taken from a supereffective Bug-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Bug-type attack. Single use."}, "tapuniumz": {"name": "Tapunium Z", "spritenum": 653, "onTakeItem": false, "zMove": "Guardian of Alola", "zMoveFrom": "Nature's Madness", "itemUser": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Tapu Bulu", "<PERSON><PERSON>"], "num": 801, "gen": 7, "isNonstandard": "Past", "desc": "If held by a Tapu with Nature's Madness, it can use Guardian of Alola.", "shortDesc": "If held by a Tapu with Nature's Madness, it can use Guardian of Alola."}, "tartapple": {"name": "Tart Apple", "spritenum": 712, "fling": {"basePower": 30}, "num": 1117, "gen": 8, "desc": "Evolves Applin into Flapple when used.", "shortDesc": "Evolves Applin into Flapple when used."}, "terrainextender": {"name": "<PERSON><PERSON> Extender", "spritenum": 662, "fling": {"basePower": 60}, "num": 879, "gen": 7, "desc": "Holder's use of Electric/Grassy/Misty/Psychic Terrain lasts 8 turns instead of 5.", "shortDesc": "Holder's use of Electric/Grassy/Misty/Psychic Terrain lasts 8 turns instead of 5."}, "thickclub": {"name": "Thick Club", "spritenum": 491, "fling": {"basePower": 90}, "onModifyAtkPriority": 1, "itemUser": ["Marowak", "Marowak-Alola", "Marowak-Alola-Totem", "<PERSON><PERSON><PERSON>"], "num": 258, "gen": 2, "isNonstandard": "Past", "desc": "If held by a Cubone or a Marowak, its Attack is doubled.", "shortDesc": "If held by a Cubone or a Marowak, its Attack is doubled."}, "throatspray": {"name": "Throat Spray", "spritenum": 713, "fling": {"basePower": 30}, "boosts": {"spa": 1}, "num": 1118, "gen": 8, "desc": "Raises holder's Special Attack by 1 stage after it uses a sound move. Single use.", "shortDesc": "Raises holder's Special Attack by 1 stage after it uses a sound move. Single use."}, "thunderstone": {"name": "Thunder Stone", "spritenum": 492, "fling": {"basePower": 30}, "num": 83, "gen": 1, "desc": "Evolves Pikachu into Raichu or Alolan Raichu, Eevee into Jolteon, Eelektrik into Eelektross, and Charjabug into Vikavolt when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "timerball": {"name": "Timer Ball", "spritenum": 494, "num": 10, "gen": 3, "isPokeball": true, "desc": "A Poke Ball that becomes better the more turns there are in a battle.", "shortDesc": "A Poke Ball that becomes better the more turns there are in a battle."}, "toxicorb": {"name": "Toxic Orb", "spritenum": 515, "fling": {"basePower": 30, "status": "tox"}, "onResidualOrder": 28, "onResidualSubOrder": 3, "num": 272, "gen": 4, "desc": "At the end of every turn, this item attempts to badly poison the holder.", "shortDesc": "At the end of every turn, this item attempts to badly poison the holder."}, "toxicplate": {"name": "Toxic Plate", "spritenum": 516, "onPlate": "Poison", "onBasePowerPriority": 15, "forcedForme": "Arceus-Poison", "num": 304, "gen": 4, "desc": "Holder's Poison-type attacks have 1.2x power. Judgment is Poison type.", "shortDesc": "Holder's Poison-type attacks have 1.2x power. Judgment is Poison type."}, "tr00": {"name": "TR00", "fling": {"basePower": 10}, "spritenum": 721, "num": 1130, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Swords Dance. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Swords Dance. One use."}, "tr01": {"name": "TR01", "fling": {"basePower": 85}, "spritenum": 721, "num": 1131, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Body Slam. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Body Slam. One use."}, "tr02": {"name": "TR02", "fling": {"basePower": 90}, "spritenum": 730, "num": 1132, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Flamethrower. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Flamethrower. One use."}, "tr03": {"name": "TR03", "fling": {"basePower": 110}, "spritenum": 731, "num": 1133, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Hydro Pump. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Hydro Pump. One use."}, "tr04": {"name": "TR04", "fling": {"basePower": 90}, "spritenum": 731, "num": 1134, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Surf. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Surf. One use."}, "tr05": {"name": "TR05", "fling": {"basePower": 90}, "spritenum": 735, "num": 1135, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Ice Beam. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Ice Beam. One use."}, "tr06": {"name": "TR06", "fling": {"basePower": 110}, "spritenum": 735, "num": 1136, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Blizzard. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Blizzard. One use."}, "tr07": {"name": "TR07", "fling": {"basePower": 10}, "spritenum": 722, "num": 1137, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Low Kick. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Low Kick. One use."}, "tr08": {"name": "TR08", "fling": {"basePower": 90}, "spritenum": 733, "num": 1138, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Thunderbolt. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Thunderbolt. One use."}, "tr09": {"name": "TR09", "fling": {"basePower": 110}, "spritenum": 733, "num": 1139, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Thunder. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Thunder. One use."}, "tr10": {"name": "TR10", "fling": {"basePower": 100}, "spritenum": 725, "num": 1140, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Earthquake. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Earthquake. One use."}, "tr11": {"name": "TR11", "fling": {"basePower": 90}, "spritenum": 734, "num": 1141, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Psychic. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Psychic. One use."}, "tr12": {"name": "TR12", "fling": {"basePower": 10}, "spritenum": 734, "num": 1142, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Agility. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Agility. One use."}, "tr13": {"name": "TR13", "fling": {"basePower": 10}, "spritenum": 721, "num": 1143, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain Poke<PERSON> the move Focus Energy. One use.", "shortDesc": "Teaches certain Poke<PERSON> the move Focus Energy. One use."}, "tr14": {"name": "TR14", "fling": {"basePower": 10}, "spritenum": 721, "num": 1144, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Metronome. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Metronome. One use."}, "tr15": {"name": "TR15", "fling": {"basePower": 110}, "spritenum": 730, "num": 1145, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Fire Blast. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Fire Blast. One use."}, "tr16": {"name": "TR16", "fling": {"basePower": 80}, "spritenum": 731, "num": 1146, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Waterfall. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Waterfall. One use."}, "tr17": {"name": "TR17", "fling": {"basePower": 10}, "spritenum": 734, "num": 1147, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Amnesia. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Amnesia. One use."}, "tr18": {"name": "TR18", "fling": {"basePower": 80}, "spritenum": 727, "num": 1148, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Leech Life. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Leech Life. One use."}, "tr19": {"name": "TR19", "fling": {"basePower": 80}, "spritenum": 721, "num": 1149, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Tri Attack. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Tri Attack. One use."}, "tr20": {"name": "TR20", "fling": {"basePower": 10}, "spritenum": 721, "num": 1150, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Substitute. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Substitute. One use."}, "tr21": {"name": "TR21", "fling": {"basePower": 10}, "spritenum": 722, "num": 1151, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Reversal. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Reversal. One use."}, "tr22": {"name": "TR22", "fling": {"basePower": 90}, "spritenum": 724, "num": 1152, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Sludge Bomb. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Sludge Bomb. One use."}, "tr23": {"name": "TR23", "fling": {"basePower": 10}, "spritenum": 725, "num": 1153, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Spike<PERSON>. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Spike<PERSON>. One use."}, "tr24": {"name": "TR24", "fling": {"basePower": 120}, "spritenum": 736, "num": 1154, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Outrage. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Outrage. One use."}, "tr25": {"name": "TR25", "fling": {"basePower": 80}, "spritenum": 734, "num": 1155, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Psyshock. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Psyshock. One use."}, "tr26": {"name": "TR26", "fling": {"basePower": 10}, "spritenum": 721, "num": 1156, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Endure. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Endure. One use."}, "tr27": {"name": "TR27", "fling": {"basePower": 10}, "spritenum": 721, "num": 1157, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Sleep Talk. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Sleep Talk. One use."}, "tr28": {"name": "TR28", "fling": {"basePower": 120}, "spritenum": 727, "num": 1158, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move <PERSON><PERSON>. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move <PERSON><PERSON>. One use."}, "tr29": {"name": "TR29", "fling": {"basePower": 10}, "spritenum": 721, "num": 1159, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Baton Pass. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Baton Pass. One use."}, "tr30": {"name": "TR30", "fling": {"basePower": 10}, "spritenum": 721, "num": 1160, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Encore. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Encore. One use."}, "tr31": {"name": "TR31", "fling": {"basePower": 100}, "spritenum": 729, "num": 1161, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Iron Tail. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Iron Tail. One use."}, "tr32": {"name": "TR32", "fling": {"basePower": 80}, "spritenum": 737, "num": 1162, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Crunch. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Crunch. One use."}, "tr33": {"name": "TR33", "fling": {"basePower": 80}, "spritenum": 728, "num": 1163, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Shadow Ball. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Shadow Ball. One use."}, "tr34": {"name": "TR34", "fling": {"basePower": 120}, "spritenum": 734, "num": 1164, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Future Sight. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Future Sight. One use."}, "tr35": {"name": "TR35", "fling": {"basePower": 90}, "spritenum": 721, "num": 1165, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Uproar. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Uproar. One use."}, "tr36": {"name": "TR36", "fling": {"basePower": 95}, "spritenum": 730, "num": 1166, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Heat Wave. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Heat Wave. One use."}, "tr37": {"name": "TR37", "fling": {"basePower": 10}, "spritenum": 737, "num": 1167, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Taunt. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Taunt. One use."}, "tr38": {"name": "TR38", "fling": {"basePower": 10}, "spritenum": 734, "num": 1168, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Trick. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Trick. One use."}, "tr39": {"name": "TR39", "fling": {"basePower": 120}, "spritenum": 722, "num": 1169, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Superpower. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Superpower. One use."}, "tr40": {"name": "TR40", "fling": {"basePower": 10}, "spritenum": 734, "num": 1170, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Skill Swap. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Skill Swap. One use."}, "tr41": {"name": "TR41", "fling": {"basePower": 85}, "spritenum": 730, "num": 1171, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Blaze Kick. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Blaze Kick. One use."}, "tr42": {"name": "TR42", "fling": {"basePower": 90}, "spritenum": 721, "num": 1172, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Hyper Voice. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Hyper Voice. One use."}, "tr43": {"name": "TR43", "fling": {"basePower": 130}, "spritenum": 730, "num": 1173, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Overheat. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Overheat. One use."}, "tr44": {"name": "TR44", "fling": {"basePower": 10}, "spritenum": 734, "num": 1174, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Cosmic Power. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Cosmic Power. One use."}, "tr45": {"name": "TR45", "fling": {"basePower": 90}, "spritenum": 731, "num": 1175, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Muddy Water. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Muddy Water. One use."}, "tr46": {"name": "TR46", "fling": {"basePower": 10}, "spritenum": 729, "num": 1176, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Iron Defense. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Iron Defense. One use."}, "tr47": {"name": "TR47", "fling": {"basePower": 80}, "spritenum": 736, "num": 1177, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Dragon Claw. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Dragon Claw. One use."}, "tr48": {"name": "TR48", "fling": {"basePower": 10}, "spritenum": 722, "num": 1178, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Bulk Up. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Bulk Up. One use."}, "tr49": {"name": "TR49", "fling": {"basePower": 10}, "spritenum": 734, "num": 1179, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Calm Mind. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Calm Mind. One use."}, "tr50": {"name": "TR50", "fling": {"basePower": 90}, "spritenum": 732, "num": 1180, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Leaf Blade. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Leaf Blade. One use."}, "tr51": {"name": "TR51", "fling": {"basePower": 10}, "spritenum": 736, "num": 1181, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Dragon Dance. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Dragon Dance. One use."}, "tr52": {"name": "TR52", "fling": {"basePower": 10}, "spritenum": 729, "num": 1182, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Gyro Ball. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Gyro Ball. One use."}, "tr53": {"name": "TR53", "fling": {"basePower": 120}, "spritenum": 722, "num": 1183, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Close Combat. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Close Combat. One use."}, "tr54": {"name": "TR54", "fling": {"basePower": 10}, "spritenum": 724, "num": 1184, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Toxic Spikes. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Toxic Spikes. One use."}, "tr55": {"name": "TR55", "fling": {"basePower": 120}, "spritenum": 730, "num": 1185, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Flare Blitz. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Flare Blitz. One use."}, "tr56": {"name": "TR56", "fling": {"basePower": 80}, "spritenum": 722, "num": 1186, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Aura Sphere. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Aura Sphere. One use."}, "tr57": {"name": "TR57", "fling": {"basePower": 80}, "spritenum": 724, "num": 1187, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Poison Jab. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Poison Jab. One use."}, "tr58": {"name": "TR58", "fling": {"basePower": 80}, "spritenum": 737, "num": 1188, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Dark Pulse. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Dark Pulse. One use."}, "tr59": {"name": "TR59", "fling": {"basePower": 80}, "spritenum": 732, "num": 1189, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Seed Bomb. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Seed Bomb. One use."}, "tr60": {"name": "TR60", "fling": {"basePower": 80}, "spritenum": 727, "num": 1190, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move X-Scissor. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move X-Scissor. One use."}, "tr61": {"name": "TR61", "fling": {"basePower": 90}, "spritenum": 727, "num": 1191, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Bug Buzz. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Bug Buzz. One use."}, "tr62": {"name": "TR62", "fling": {"basePower": 85}, "spritenum": 736, "num": 1192, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Dragon Pulse. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Dragon Pulse. One use."}, "tr63": {"name": "TR63", "fling": {"basePower": 80}, "spritenum": 726, "num": 1193, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Power Gem. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Power Gem. One use."}, "tr64": {"name": "TR64", "fling": {"basePower": 120}, "spritenum": 722, "num": 1194, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Focus Blast. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Focus Blast. One use."}, "tr65": {"name": "TR65", "fling": {"basePower": 90}, "spritenum": 732, "num": 1195, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Energy Ball. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Energy Ball. One use."}, "tr66": {"name": "TR66", "fling": {"basePower": 120}, "spritenum": 723, "num": 1196, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Brave Bird. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Brave Bird. One use."}, "tr67": {"name": "TR67", "fling": {"basePower": 90}, "spritenum": 725, "num": 1197, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Earth Power. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Earth Power. One use."}, "tr68": {"name": "TR68", "fling": {"basePower": 10}, "spritenum": 737, "num": 1198, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Nasty Plot. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Nasty Plot. One use."}, "tr69": {"name": "TR69", "fling": {"basePower": 80}, "spritenum": 734, "num": 1199, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Zen Headbutt. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Zen Headbutt. One use."}, "tr70": {"name": "TR70", "fling": {"basePower": 80}, "spritenum": 729, "num": 1200, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Flash Cannon. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Flash Cannon. One use."}, "tr71": {"name": "TR71", "fling": {"basePower": 130}, "spritenum": 732, "num": 1201, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Leaf Storm. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Leaf Storm. One use."}, "tr72": {"name": "TR72", "fling": {"basePower": 120}, "spritenum": 732, "num": 1202, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Power Whip. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Power Whip. One use."}, "tr73": {"name": "TR73", "fling": {"basePower": 120}, "spritenum": 724, "num": 1203, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Gunk Shot. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Gunk Shot. One use."}, "tr74": {"name": "TR74", "fling": {"basePower": 80}, "spritenum": 729, "num": 1204, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Iron Head. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Iron Head. One use."}, "tr75": {"name": "TR75", "fling": {"basePower": 100}, "spritenum": 726, "num": 1205, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Stone Edge. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Stone Edge. One use."}, "tr76": {"name": "TR76", "fling": {"basePower": 10}, "spritenum": 726, "num": 1206, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Stealth Rock. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Stealth Rock. One use."}, "tr77": {"name": "TR77", "fling": {"basePower": 10}, "spritenum": 732, "num": 1207, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Grass Knot. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Grass Knot. One use."}, "tr78": {"name": "TR78", "fling": {"basePower": 95}, "spritenum": 724, "num": 1208, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Sludge Wave. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Sludge Wave. One use."}, "tr79": {"name": "TR79", "fling": {"basePower": 10}, "spritenum": 729, "num": 1209, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Heavy Slam. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Heavy Slam. One use."}, "tr80": {"name": "TR80", "fling": {"basePower": 10}, "spritenum": 733, "num": 1210, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Electro Ball. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Electro Ball. One use."}, "tr81": {"name": "TR81", "fling": {"basePower": 95}, "spritenum": 737, "num": 1211, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Foul Play. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Foul Play. One use."}, "tr82": {"name": "TR82", "fling": {"basePower": 20}, "spritenum": 734, "num": 1212, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain Poke<PERSON> the move Stored Power. One use.", "shortDesc": "Teaches certain Poke<PERSON> the move Stored Power. One use."}, "tr83": {"name": "TR83", "fling": {"basePower": 10}, "spritenum": 734, "num": 1213, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Ally Switch. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Ally Switch. One use."}, "tr84": {"name": "TR84", "fling": {"basePower": 80}, "spritenum": 731, "num": 1214, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Scald. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Scald. One use."}, "tr85": {"name": "TR85", "fling": {"basePower": 10}, "spritenum": 721, "num": 1215, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Work Up. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Work Up. One use."}, "tr86": {"name": "TR86", "fling": {"basePower": 90}, "spritenum": 733, "num": 1216, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Wild Charge. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Wild Charge. One use."}, "tr87": {"name": "TR87", "fling": {"basePower": 80}, "spritenum": 725, "num": 1217, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Drill Run. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Drill Run. One use."}, "tr88": {"name": "TR88", "fling": {"basePower": 10}, "spritenum": 730, "num": 1218, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Heat Crash. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Heat Crash. One use."}, "tr89": {"name": "TR89", "fling": {"basePower": 110}, "spritenum": 723, "num": 1219, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Hurricane. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Hurricane. One use."}, "tr90": {"name": "TR90", "fling": {"basePower": 90}, "spritenum": 738, "num": 1220, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Play Rough. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Play Rough. One use."}, "tr91": {"name": "TR91", "fling": {"basePower": 10}, "spritenum": 724, "num": 1221, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move <PERSON><PERSON><PERSON> Drench. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move <PERSON><PERSON><PERSON> Drench. One use."}, "tr92": {"name": "TR92", "fling": {"basePower": 80}, "spritenum": 738, "num": 1222, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Dazzling Gleam. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Dazzling Gleam. One use."}, "tr93": {"name": "TR93", "fling": {"basePower": 85}, "spritenum": 737, "num": 1223, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Darkest Lariat. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Darkest Lariat. One use."}, "tr94": {"name": "TR94", "fling": {"basePower": 95}, "spritenum": 725, "num": 1224, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move High Horsepower. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move High Horsepower. One use."}, "tr95": {"name": "TR95", "fling": {"basePower": 80}, "spritenum": 737, "num": 1225, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Throat Chop. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Throat Chop. One use."}, "tr96": {"name": "TR96", "fling": {"basePower": 90}, "spritenum": 727, "num": 1226, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move <PERSON><PERSON> Puff. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move <PERSON><PERSON> Puff. One use."}, "tr97": {"name": "TR97", "fling": {"basePower": 85}, "spritenum": 734, "num": 1227, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Psychic Fangs. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Psychic Fangs. One use."}, "tr98": {"name": "TR98", "fling": {"basePower": 85}, "spritenum": 731, "num": 1228, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Liquidation. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Liquidation. One use."}, "tr99": {"name": "TR99", "fling": {"basePower": 80}, "spritenum": 722, "num": 1229, "gen": 8, "isNonstandard": "Past", "desc": "Teaches certain <PERSON><PERSON><PERSON> the move Body Press. One use.", "shortDesc": "Teaches certain <PERSON><PERSON><PERSON> the move Body Press. One use."}, "twistedspoon": {"name": "<PERSON><PERSON><PERSON>", "spritenum": 520, "fling": {"basePower": 30}, "onBasePowerPriority": 15, "num": 248, "gen": 2, "desc": "Holder's Psychic-type attacks have 1.2x power.", "shortDesc": "Holder's Psychic-type attacks have 1.2x power."}, "tyranitarite": {"name": "Tyranitarite", "spritenum": 607, "megaStone": "Tyranitar-Mega", "megaEvolves": "Tyranitar", "itemUser": ["Tyranitar"], "num": 669, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Tyranitar, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Tyranitar, this item allows it to Mega Evolve in battle."}, "ultraball": {"name": "Ultra Ball", "spritenum": 521, "num": 2, "gen": 1, "isPokeball": true, "desc": "An ultra-performance Ball that provides a higher catch rate than a Great Ball.", "shortDesc": "An ultra-performance Ball that provides a higher catch rate than a Great Ball."}, "ultranecroziumz": {"name": "Ultranecrozium Z", "spritenum": 687, "onTakeItem": false, "zMove": "Light That Burns the Sky", "zMoveFrom": "<PERSON><PERSON>", "itemUser": ["Necrozma-Ultra"], "num": 923, "gen": 7, "isNonstandard": "Past", "desc": "Dusk Mane/Dawn Wings Necrozma: Ultra Burst, then Z-Move w/ <PERSON><PERSON>.", "shortDesc": "Dusk Mane/Dawn Wings Necrozma: Ultra Burst, then Z-Move w/ <PERSON><PERSON>."}, "unremarkableteacup": {"name": "Unremarkable Teacup", "spritenum": 756, "fling": {"basePower": 80}, "num": 2403, "gen": 9, "desc": "Evolves Poltchageist into Sinistcha when used.", "shortDesc": "Evolves Poltchageist into Sinistcha when used."}, "upgrade": {"name": "Up-Grade", "spritenum": 523, "fling": {"basePower": 30}, "num": 252, "gen": 2, "desc": "Evolves Porygon into Porygon2 when traded.", "shortDesc": "Evolves Porygon into Porygon2 when traded."}, "utilityumbrella": {"name": "Utility Umbrella", "spritenum": 718, "fling": {"basePower": 60}, "num": 1123, "gen": 8, "desc": "The holder ignores rain- and sun-based effects. Damage and accuracy calculations from attacks used by the holder are affected by rain and sun, but not attacks used against the holder.", "shortDesc": "The holder ignores rain- and sun-based effects."}, "venusaurite": {"name": "Venusaurite", "spritenum": 608, "megaStone": "Venusaur-Mega", "megaEvolves": "<PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON>"], "num": 659, "gen": 6, "isNonstandard": "Past", "desc": "If held by a Venusaur, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a Venusaur, this item allows it to Mega Evolve in battle."}, "wacanberry": {"name": "Wacan Berry", "spritenum": 526, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Electric"}, "num": 186, "gen": 4, "desc": "Halves damage taken from a supereffective Electric-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Electric-type attack. Single use."}, "watergem": {"name": "Water Gem", "spritenum": 528, "isGem": true, "num": 549, "gen": 5, "isNonstandard": "Past", "desc": "Holder's first successful Water-type attack will have 1.3x power. Single use.", "shortDesc": "Holder's first successful Water-type attack will have 1.3x power. Single use."}, "watermemory": {"name": "Water Memory", "spritenum": 677, "onMemory": "Water", "forcedForme": "<PERSON>lly-Water", "itemUser": ["<PERSON>lly-Water"], "num": 913, "gen": 7, "isNonstandard": "Past", "desc": "Holder's Multi-Attack is Water type.", "shortDesc": "Holder's Multi-Attack is Water type."}, "waterstone": {"name": "Water Stone", "spritenum": 529, "fling": {"basePower": 30}, "num": 84, "gen": 1, "desc": "Evolves Poliwhirl into Poliwrath, Shellder into Cloyster, Staryu into Starmie, Eevee into Vaporeon, Lombre into Ludicolo, and Panpour into Simipour when used.", "shortDesc": "Evolves certain species of Pokemon when used."}, "wateriumz": {"name": "Waterium Z", "spritenum": 633, "onPlate": "Water", "onTakeItem": false, "zMove": true, "zMoveType": "Water", "forcedForme": "Arceus-Water", "num": 778, "gen": 7, "isNonstandard": "Past", "desc": "If holder has a Water move, this item allows it to use a Water Z-Move.", "shortDesc": "If holder has a Water move, this item allows it to use a Water Z-Move."}, "watmelberry": {"name": "Watmel Berry", "spritenum": 530, "isBerry": true, "naturalGift": {"basePower": 100, "type": "Fire"}, "onEat": false, "num": 181, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "waveincense": {"name": "Wave Incense", "spritenum": 531, "fling": {"basePower": 10}, "onBasePowerPriority": 15, "num": 317, "gen": 4, "isNonstandard": "Past", "desc": "Holder's Water-type attacks have 1.2x power.", "shortDesc": "Holder's Water-type attacks have 1.2x power."}, "weaknesspolicy": {"name": "Weakness Policy", "spritenum": 609, "fling": {"basePower": 80}, "boosts": {"atk": 2, "spa": 2}, "num": 639, "gen": 6, "desc": "If holder is hit super effectively, raises Attack, Sp. Atk by 2 stages. Single use.", "shortDesc": "If holder is hit super effectively, raises Attack, Sp. Atk by 2 stages. Single use."}, "wellspringmask": {"name": "Wellspring Mask", "spritenum": 759, "fling": {"basePower": 60}, "onBasePowerPriority": 15, "forcedForme": "Ogerpon-Wellspring", "itemUser": ["Ogerpon-Wellspring"], "num": 2407, "gen": 9, "desc": "Ogerpon-Wells<PERSON>ring: 1.2x power attacks Terastallize to gain Embody Aspect.", "shortDesc": "Ogerpon-Wells<PERSON>ring: 1.2x power attacks Terastallize to gain Embody Aspect."}, "wepearberry": {"name": "<PERSON><PERSON>", "spritenum": 533, "isBerry": true, "naturalGift": {"basePower": 90, "type": "Electric"}, "onEat": false, "num": 167, "gen": 3, "isNonstandard": "Past", "desc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck.", "shortDesc": "Cannot be eaten by the holder. No effect when eaten with Bug Bite or Pluck."}, "whippeddream": {"name": "Whipped Dream", "spritenum": 692, "fling": {"basePower": 80}, "num": 646, "gen": 6, "isNonstandard": "Past", "desc": "Evolves Swirlix into Slurpuff when traded.", "shortDesc": "Evolves Swirlix into Slurpuff when traded."}, "whiteherb": {"name": "<PERSON> Herb", "spritenum": 535, "fling": {"basePower": 10}, "num": 214, "gen": 3, "desc": "Restores all lowered stat stages to 0 when one is less than 0. Single use.", "shortDesc": "Restores all lowered stat stages to 0 when one is less than 0. Single use."}, "widelens": {"name": "Wide Lens", "spritenum": 537, "fling": {"basePower": 10}, "onSourceModifyAccuracyPriority": -2, "num": 265, "gen": 4, "desc": "The accuracy of attacks by the holder is 1.1x.", "shortDesc": "The accuracy of attacks by the holder is 1.1x."}, "wikiberry": {"name": "Wiki Berry", "spritenum": 538, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Rock"}, "num": 160, "gen": 3, "desc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -SpA Nature. Single use.", "shortDesc": "Restores 1/3 max HP at 1/4 max HP or less confuses if -SpA Nature. Single use."}, "wiseglasses": {"name": "<PERSON>", "spritenum": 539, "fling": {"basePower": 10}, "onBasePowerPriority": 16, "num": 267, "gen": 4, "desc": "Holder's special attacks have 1.1x power.", "shortDesc": "Holder's special attacks have 1.1x power."}, "yacheberry": {"name": "<PERSON><PERSON>", "spritenum": 567, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Ice"}, "num": 188, "gen": 4, "desc": "Halves damage taken from a supereffective Ice-type attack. Single use.", "shortDesc": "Halves damage taken from a supereffective Ice-type attack. Single use."}, "zapplate": {"name": "Zap Plate", "spritenum": 572, "onPlate": "Electric", "onBasePowerPriority": 15, "forcedForme": "Arceus-Electric", "num": 300, "gen": 4, "desc": "Holder's Electric-type attacks have 1.2x power. Judgment is Electric type.", "shortDesc": "Holder's Electric-type attacks have 1.2x power. Judgment is Electric type."}, "zoomlens": {"name": "Zoom Lens", "spritenum": 574, "fling": {"basePower": 10}, "onSourceModifyAccuracyPriority": -2, "num": 276, "gen": 4, "desc": "The accuracy of attacks by the holder is 1.2x if it moves after its target.", "shortDesc": "The accuracy of attacks by the holder is 1.2x if it moves after its target."}, "berserkgene": {"name": "Berserk Gene", "spritenum": 388, "boosts": {"atk": 2}, "num": 0, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) On switch-in, raises holder's Attack by 2 and confuses it. Single use.", "shortDesc": "(Gen 2) On switch-in, raises holder's Attack by 2 and confuses it. Single use."}, "berry": {"name": "<PERSON>", "spritenum": 319, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Poison"}, "onResidualOrder": 5, "num": 155, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Restores 10 HP when at 1/2 max HP or less. Single use.", "shortDesc": "(Gen 2) Restores 10 HP when at 1/2 max HP or less. Single use."}, "bitterberry": {"name": "Bitter Berry", "spritenum": 334, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Ground"}, "num": 156, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Holder is cured if it is confused. Single use.", "shortDesc": "(Gen 2) Holder is cured if it is confused. Single use."}, "burntberry": {"name": "<PERSON><PERSON>", "spritenum": 13, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Ice"}, "num": 153, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Holder is cured if it is frozen. Single use.", "shortDesc": "(Gen 2) Holder is cured if it is frozen. Single use."}, "goldberry": {"name": "Gold Berry", "spritenum": 448, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Psychic"}, "onResidualOrder": 5, "num": 158, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Restores 30 HP when at 1/2 max HP or less. Single use.", "shortDesc": "(Gen 2) Restores 30 HP when at 1/2 max HP or less. Single use."}, "iceberry": {"name": "<PERSON> Berry", "spritenum": 381, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Grass"}, "num": 152, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Holder is cured if it is burned. Single use.", "shortDesc": "(Gen 2) Holder is cured if it is burned. Single use."}, "mintberry": {"name": "Mint Berry", "spritenum": 65, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Water"}, "num": 150, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Holder wakes up if it is asleep. Single use.", "shortDesc": "(Gen 2) Holder wakes up if it is asleep. Single use."}, "miracleberry": {"name": "<PERSON>", "spritenum": 262, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Flying"}, "num": 157, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Holder cures itself if it is confused or has a status condition. Single use.", "shortDesc": "(Gen 2) Holder cures itself if it is confused or has a status condition. Single use."}, "mysteryberry": {"name": "Mystery Berry", "spritenum": 244, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Fighting"}, "num": 154, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Restores 5 PP to the first of the holder's moves to reach 0 PP. Single use.", "shortDesc": "(Gen 2) Restores 5 PP to the first of the holder's moves to reach 0 PP. Single use."}, "pinkbow": {"name": "Pink Bow", "spritenum": 444, "num": 251, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) <PERSON><PERSON>'s Normal-type attacks have 1.1x power.", "shortDesc": "(Gen 2) <PERSON><PERSON>'s Normal-type attacks have 1.1x power."}, "polkadotbow": {"name": "Polkadot Bow", "spritenum": 444, "num": 251, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) <PERSON><PERSON>'s Normal-type attacks have 1.1x power.", "shortDesc": "(Gen 2) <PERSON><PERSON>'s Normal-type attacks have 1.1x power."}, "przcureberry": {"name": "PRZ Cure Berry", "spritenum": 63, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Fire"}, "num": 149, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Holder cures itself if it is paralyzed. Single use.", "shortDesc": "(Gen 2) Holder cures itself if it is paralyzed. Single use."}, "psncureberry": {"name": "PSN Cure Berry", "spritenum": 333, "isBerry": true, "naturalGift": {"basePower": 80, "type": "Electric"}, "num": 151, "gen": 2, "isNonstandard": "Past", "desc": "(Gen 2) Holder is cured if it is poisoned. Single use.", "shortDesc": "(Gen 2) Holder is cured if it is poisoned. Single use."}, "crucibellite": {"name": "Crucibellite", "spritenum": 577, "megaStone": "Crucibelle-Mega", "megaEvolves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "itemUser": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "num": -1, "gen": 6, "isNonstandard": "CAP", "desc": "If held by a <PERSON><PERSON><PERSON><PERSON><PERSON>, this item allows it to Mega Evolve in battle.", "shortDesc": "If held by a <PERSON><PERSON><PERSON><PERSON><PERSON>, this item allows it to Mega Evolve in battle."}, "vilevial": {"name": "<PERSON><PERSON>", "spritenum": 752, "fling": {"basePower": 60}, "onBasePowerPriority": 15, "forcedForme": "Venomicon-Epilogue", "itemUser": ["Venomicon-Epilogue"], "num": -2, "gen": 8, "isNonstandard": "CAP", "desc": "If held by a Venomicon, its Poison- and Flying-type attacks have 1.2x power.", "shortDesc": "If held by a Venomicon, its Poison- and Flying-type attacks have 1.2x power."}}
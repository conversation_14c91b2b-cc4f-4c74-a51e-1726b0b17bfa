{"zero": [{"name": "masterball", "cost": 0}, {"name": "safariball", "cost": 0}, {"name": "cherishball", "cost": 0}, {"name": "adamantorb", "cost": 0}, {"name": "lustrousorb", "cost": 0}, {"name": "expshare", "cost": 0}, {"name": "souldew", "cost": 0}, {"name": "hm01", "cost": 0}, {"name": "hm02", "cost": 0}, {"name": "hm03", "cost": 0}, {"name": "hm04", "cost": 0}, {"name": "hm05", "cost": 0}, {"name": "hm06", "cost": 0}, {"name": "hm07", "cost": 0}, {"name": "hm08", "cost": 0}, {"name": "explorerkit", "cost": 0}, {"name": "lootsack", "cost": 0}, {"name": "rulebook", "cost": 0}, {"name": "pokeradar", "cost": 0}, {"name": "pointcard", "cost": 0}, {"name": "journal", "cost": 0}, {"name": "sealcase", "cost": 0}, {"name": "fashioncase", "cost": 0}, {"name": "sealbag", "cost": 0}, {"name": "palpad", "cost": 0}, {"name": "workskey", "cost": 0}, {"name": "oldcharm", "cost": 0}, {"name": "galactickey", "cost": 0}, {"name": "redchain", "cost": 0}, {"name": "townmap", "cost": 0}, {"name": "vsseeker", "cost": 0}, {"name": "coincase", "cost": 0}, {"name": "oldrod", "cost": 0}, {"name": "goodrod", "cost": 0}, {"name": "superrod", "cost": 0}, {"name": "sprayduck", "cost": 0}, {"name": "poffincase", "cost": 0}, {"name": "bicycle", "cost": 0}, {"name": "suitekey", "cost": 0}, {"name": "oaksletter", "cost": 0}, {"name": "lunarwing", "cost": 0}, {"name": "membercard", "cost": 0}, {"name": "azureflute", "cost": 0}, {"name": "ssticket", "cost": 0}, {"name": "contestpass", "cost": 0}, {"name": "magmastone", "cost": 0}, {"name": "parcel", "cost": 0}, {"name": "coupon1", "cost": 0}, {"name": "coupon2", "cost": 0}, {"name": "coupon3", "cost": 0}, {"name": "storagekey", "cost": 0}, {"name": "secretpotion", "cost": 0}, {"name": "griseousorb", "cost": 0}, {"name": "vsrecorder", "cost": 0}, {"name": "gracidea", "cost": 0}, {"name": "secretkey", "cost": 0}, {"name": "apricornbox", "cost": 0}, {"name": "berrypots", "cost": 0}, {"name": "squirtbottle", "cost": 0}, {"name": "lureball", "cost": 0}, {"name": "levelball", "cost": 0}, {"name": "moonball", "cost": 0}, {"name": "heavyball", "cost": 0}, {"name": "fastball", "cost": 0}, {"name": "friendball", "cost": 0}, {"name": "loveball", "cost": 0}, {"name": "parkball", "cost": 0}, {"name": "dowsingmachine", "cost": 0}, {"name": "redorb", "cost": 0}, {"name": "blueorb", "cost": 0}, {"name": "jadeorb", "cost": 0}, {"name": "enigmastone", "cost": 0}, {"name": "unownreport", "cost": 0}, {"name": "bluecard", "cost": 0}, {"name": "slowpoketail", "cost": 0}, {"name": "clearbell", "cost": 0}, {"name": "cardkey", "cost": 0}, {"name": "basementkey", "cost": 0}, {"name": "redscale", "cost": 0}, {"name": "lostitem", "cost": 0}, {"name": "pass", "cost": 0}, {"name": "machinepart", "cost": 0}, {"name": "silverwing", "cost": 0}, {"name": "rainbowwing", "cost": 0}, {"name": "<PERSON><PERSON><PERSON>", "cost": 0}, {"name": "gbsounds", "cost": 0}, {"name": "<PERSON>bell", "cost": 0}, {"name": "datacard01", "cost": 0}, {"name": "datacard02", "cost": 0}, {"name": "datacard03", "cost": 0}, {"name": "datacard04", "cost": 0}, {"name": "datacard05", "cost": 0}, {"name": "datacard06", "cost": 0}, {"name": "datacard07", "cost": 0}, {"name": "datacard08", "cost": 0}, {"name": "datacard09", "cost": 0}, {"name": "datacard10", "cost": 0}, {"name": "datacard11", "cost": 0}, {"name": "datacard12", "cost": 0}, {"name": "datacard13", "cost": 0}, {"name": "datacard14", "cost": 0}, {"name": "datacard15", "cost": 0}, {"name": "datacard16", "cost": 0}, {"name": "datacard17", "cost": 0}, {"name": "datacard18", "cost": 0}, {"name": "datacard19", "cost": 0}, {"name": "datacard20", "cost": 0}, {"name": "datacard21", "cost": 0}, {"name": "datacard22", "cost": 0}, {"name": "datacard23", "cost": 0}, {"name": "datacard24", "cost": 0}, {"name": "datacard25", "cost": 0}, {"name": "datacard26", "cost": 0}, {"name": "datacard27", "cost": 0}, {"name": "lockcapsule", "cost": 0}, {"name": "photoalbum", "cost": 0}, {"name": "retromail", "cost": 0}, {"name": "machbike", "cost": 0}, {"name": "acrobike", "cost": 0}, {"name": "wailmerpail", "cost": 0}, {"name": "devongoods", "cost": 0}, {"name": "sootsack", "cost": 0}, {"name": "pokeblockcase", "cost": 0}, {"name": "letter", "cost": 0}, {"name": "eonticket", "cost": 0}, {"name": "scanner", "cost": 0}, {"name": "gogoggles", "cost": 0}, {"name": "meteorite", "cost": 0}, {"name": "rm1key", "cost": 0}, {"name": "rm2key", "cost": 0}, {"name": "rm4key", "cost": 0}, {"name": "rm6key", "cost": 0}, {"name": "devon<PERSON>", "cost": 0}, {"name": "oaksparcel", "cost": 0}, {"name": "pokeflute", "cost": 0}, {"name": "bikevoucher", "cost": 0}, {"name": "goldteeth", "cost": 0}, {"name": "liftkey", "cost": 0}, {"name": "silphscope", "cost": 0}, {"name": "famechecker", "cost": 0}, {"name": "tmcase", "cost": 0}, {"name": "berrypouch", "cost": 0}, {"name": "teachytv", "cost": 0}, {"name": "tripass", "cost": 0}, {"name": "rainbowpass", "cost": 0}, {"name": "tea", "cost": 0}, {"name": "mysticticket", "cost": 0}, {"name": "auroraticket", "cost": 0}, {"name": "powderjar", "cost": 0}, {"name": "ruby", "cost": 0}, {"name": "sapphire", "cost": 0}, {"name": "magmaemblem", "cost": 0}, {"name": "oldseamap", "cost": 0}, {"name": "dousedrive", "cost": 0}, {"name": "shockdrive", "cost": 0}, {"name": "burndrive", "cost": 0}, {"name": "chilldrive", "cost": 0}, {"name": "libertypass", "cost": 0}, {"name": "dreamball", "cost": 0}, {"name": "propcase", "cost": 0}, {"name": "dragonskull", "cost": 0}, {"name": "reliccopper", "cost": 0}, {"name": "<PERSON><PERSON><PERSON>", "cost": 0}, {"name": "relicvase", "cost": 0}, {"name": "relicband", "cost": 0}, {"name": "relicstatue", "cost": 0}, {"name": "reliccrown", "cost": 0}, {"name": "direhit2", "cost": 0}, {"name": "xspeed2", "cost": 0}, {"name": "xspatk2", "cost": 0}, {"name": "xspdef2", "cost": 0}, {"name": "xdefense2", "cost": 0}, {"name": "xattack2", "cost": 0}, {"name": "xaccuracy2", "cost": 0}, {"name": "xspeed3", "cost": 0}, {"name": "xspatk3", "cost": 0}, {"name": "xspdef3", "cost": 0}, {"name": "xdefense3", "cost": 0}, {"name": "xattack3", "cost": 0}, {"name": "xaccuracy3", "cost": 0}, {"name": "xspeed6", "cost": 0}, {"name": "xspatk6", "cost": 0}, {"name": "xspdef6", "cost": 0}, {"name": "xdefense6", "cost": 0}, {"name": "xattack6", "cost": 0}, {"name": "xaccuracy6", "cost": 0}, {"name": "abilityurge", "cost": 0}, {"name": "itemdrop", "cost": 0}, {"name": "itemurge", "cost": 0}, {"name": "reseturge", "cost": 0}, {"name": "direhit3", "cost": 0}, {"name": "lightstone", "cost": 0}, {"name": "darkstone", "cost": 0}, {"name": "xtransceiver", "cost": 0}, {"name": "godstone", "cost": 0}, {"name": "gram1", "cost": 0}, {"name": "gram2", "cost": 0}, {"name": "gram3", "cost": 0}, {"name": "medalbox", "cost": 0}, {"name": "dnasplicers", "cost": 0}, {"name": "permit", "cost": 0}, {"name": "oval<PERSON>rm", "cost": 0}, {"name": "shiny<PERSON><PERSON>", "cost": 0}, {"name": "plasmacard", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "colressmachine", "cost": 0}, {"name": "droppeditem", "cost": 0}, {"name": "revealglass", "cost": 0}, {"name": "gengarite", "cost": 0}, {"name": "gardevoirite", "cost": 0}, {"name": "ampharosite", "cost": 0}, {"name": "venusaurite", "cost": 0}, {"name": "charizarditex", "cost": 0}, {"name": "blastoisinite", "cost": 0}, {"name": "mewtwonitex", "cost": 0}, {"name": "mewtwonitey", "cost": 0}, {"name": "blazikenite", "cost": 0}, {"name": "medichamite", "cost": 0}, {"name": "houndoominite", "cost": 0}, {"name": "aggronite", "cost": 0}, {"name": "banettite", "cost": 0}, {"name": "tyranitarite", "cost": 0}, {"name": "scizorite", "cost": 0}, {"name": "pinsirite", "cost": 0}, {"name": "aerodactylite", "cost": 0}, {"name": "lucarionite", "cost": 0}, {"name": "abomasite", "cost": 0}, {"name": "kangaskhanite", "cost": 0}, {"name": "gyaradosite", "cost": 0}, {"name": "absolite", "cost": 0}, {"name": "charizarditey", "cost": 0}, {"name": "alakazite", "cost": 0}, {"name": "heracronite", "cost": 0}, {"name": "mawi<PERSON>", "cost": 0}, {"name": "manectite", "cost": 0}, {"name": "garchompite", "cost": 0}, {"name": "<PERSON><PERSON><PERSON>", "cost": 0}, {"name": "elevatorkey", "cost": 0}, {"name": "holocaster", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "intriguingstone", "cost": 0}, {"name": "lenscase", "cost": 0}, {"name": "lookerticket", "cost": 0}, {"name": "megaring", "cost": 0}, {"name": "powerplantpass", "cost": 0}, {"name": "profsletter", "cost": 0}, {"name": "rollerskates", "cost": 0}, {"name": "sprinklotad", "cost": 0}, {"name": "tmvpass", "cost": 0}, {"name": "latiasite", "cost": 0}, {"name": "latiosite", "cost": 0}, {"name": "commonstone", "cost": 0}, {"name": "makeupbag", "cost": 0}, {"name": "traveltrunk", "cost": 0}, {"name": "megacharm", "cost": 0}, {"name": "megaglove", "cost": 0}, {"name": "devonparts", "cost": 0}, {"name": "pokeblockkit", "cost": 0}, {"name": "keytoroom1", "cost": 0}, {"name": "keytoroom2", "cost": 0}, {"name": "keytoroom4", "cost": 0}, {"name": "keytoroom6", "cost": 0}, {"name": "devons<PERSON><PERSON>ar", "cost": 0}, {"name": "contestcostumejacket", "cost": 0}, {"name": "magmasuit", "cost": 0}, {"name": "aquasuit", "cost": 0}, {"name": "pairoftickets", "cost": 0}, {"name": "megabracelet", "cost": 0}, {"name": "megapendant", "cost": 0}, {"name": "megaglasses", "cost": 0}, {"name": "megaanchor", "cost": 0}, {"name": "megastickpin", "cost": 0}, {"name": "megatiara", "cost": 0}, {"name": "megaanklet", "cost": 0}, {"name": "swampertite", "cost": 0}, {"name": "sceptilite", "cost": 0}, {"name": "sablenite", "cost": 0}, {"name": "altarianite", "cost": 0}, {"name": "galladite", "cost": 0}, {"name": "audinite", "cost": 0}, {"name": "metagrossite", "cost": 0}, {"name": "sharpedonite", "cost": 0}, {"name": "slowbronite", "cost": 0}, {"name": "steelixite", "cost": 0}, {"name": "pidgeotite", "cost": 0}, {"name": "glalitite", "cost": 0}, {"name": "diancite", "cost": 0}, {"name": "prisonbottle", "cost": 0}, {"name": "megacuff", "cost": 0}, {"name": "cameruptite", "cost": 0}, {"name": "lopunnite", "cost": 0}, {"name": "salamencite", "cost": 0}, {"name": "beedrillite", "cost": 0}, {"name": "keystone", "cost": 0}, {"name": "meteoriteshard", "cost": 0}, {"name": "eonflute", "cost": 0}, {"name": "normaliumzheld", "cost": 0}, {"name": "fi<PERSON>zheld", "cost": 0}, {"name": "wateriumzheld", "cost": 0}, {"name": "electriumzheld", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "iciumzheld", "cost": 0}, {"name": "fightiniumzheld", "cost": 0}, {"name": "poisoniumzheld", "cost": 0}, {"name": "groundiumzheld", "cost": 0}, {"name": "flyiniumzheld", "cost": 0}, {"name": "psychiumzheld", "cost": 0}, {"name": "buginiumzheld", "cost": 0}, {"name": "rockiumzheld", "cost": 0}, {"name": "ghostiumzheld", "cost": 0}, {"name": "dragoniumzheld", "cost": 0}, {"name": "darkiniumzheld", "cost": 0}, {"name": "steeliumzheld", "cost": 0}, {"name": "fairiumzheld", "cost": 0}, {"name": "pikaniumzheld", "cost": 0}, {"name": "zring", "cost": 0}, {"name": "decidiumzheld", "cost": 0}, {"name": "inciniumzheld", "cost": 0}, {"name": "primariumzheld", "cost": 0}, {"name": "tapuniumzheld", "cost": 0}, {"name": "marshadiumz<PERSON>", "cost": 0}, {"name": "aloraichiumzheld", "cost": 0}, {"name": "snor<PERSON>zheld", "cost": 0}, {"name": "eeviumzheld", "cost": 0}, {"name": "mewn<PERSON>zheld", "cost": 0}, {"name": "pikashuniumzheld", "cost": 0}, {"name": "foragebag", "cost": 0}, {"name": "fishingrod", "cost": 0}, {"name": "professorsmask", "cost": 0}, {"name": "sparklingstone", "cost": 0}, {"name": "zygardecube", "cost": 0}, {"name": "ridepager", "cost": 0}, {"name": "beastball", "cost": 0}, {"name": "sunflute", "cost": 0}, {"name": "moonflute", "cost": 0}, {"name": "enigmaticcard", "cost": 0}, {"name": "bikegreen", "cost": 0}, {"name": "storagekeygalacticwarehouse", "cost": 0}, {"name": "basementkeygoldenrod", "cost": 0}, {"name": "xtransceiverred", "cost": 0}, {"name": "xtransceiveryellow", "cost": 0}, {"name": "dnasplicersmerge", "cost": 0}, {"name": "dnasplicerssplit", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "holocastergreen", "cost": 0}, {"name": "bikeyellow", "cost": 0}, {"name": "holocasterred", "cost": 0}, {"name": "basementkeynewmauville", "cost": 0}, {"name": "storagekeyseamauville", "cost": 0}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "contestcostumedress", "cost": 0}, {"name": "meteorite2", "cost": 0}, {"name": "meteorite3", "cost": 0}, {"name": "meteorite4", "cost": 0}, {"name": "normaliumzbag", "cost": 0}, {"name": "firiumzbag", "cost": 0}, {"name": "wateriumzbag", "cost": 0}, {"name": "electriumzbag", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "iciumzbag", "cost": 0}, {"name": "fightiniumzbag", "cost": 0}, {"name": "poisoniumzbag", "cost": 0}, {"name": "groundiumzbag", "cost": 0}, {"name": "flyiniumzbag", "cost": 0}, {"name": "psychiumzbag", "cost": 0}, {"name": "buginiumzbag", "cost": 0}, {"name": "rockiumzbag", "cost": 0}, {"name": "ghostiumzbag", "cost": 0}, {"name": "dragoniumzbag", "cost": 0}, {"name": "darkiniumzbag", "cost": 0}, {"name": "steeliumzbag", "cost": 0}, {"name": "fairiumzbag", "cost": 0}, {"name": "pika<PERSON>zbag", "cost": 0}, {"name": "decidiumzbag", "cost": 0}, {"name": "inciniumzbag", "cost": 0}, {"name": "primariumzbag", "cost": 0}, {"name": "tapuniumzbag", "cost": 0}, {"name": "marshadiumzbag", "cost": 0}, {"name": "aloraichiumzbag", "cost": 0}, {"name": "snor<PERSON>zbag", "cost": 0}, {"name": "eeviumzbag", "cost": 0}, {"name": "mewn<PERSON>zbag", "cost": 0}, {"name": "pikashuniumzbag", "cost": 0}, {"name": "solganiumzheld", "cost": 0}, {"name": "lunaliumzheld", "cost": 0}, {"name": "ultranecroziumzheld", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "lycaniumzheld", "cost": 0}, {"name": "kommoniumzheld", "cost": 0}, {"name": "solganiumzbag", "cost": 0}, {"name": "lunaliumzbag", "cost": 0}, {"name": "ultranecroziumzbag", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "lycaniumzbag", "cost": 0}, {"name": "kommoniumzbag", "cost": 0}, {"name": "zpowerring", "cost": 0}, {"name": "pinkpetal", "cost": 0}, {"name": "orangepetal", "cost": 0}, {"name": "bluepetal", "cost": 0}, {"name": "redpetal", "cost": 0}, {"name": "greenpetal", "cost": 0}, {"name": "yellowpetal", "cost": 0}, {"name": "purplepetal", "cost": 0}, {"name": "rainbowflower", "cost": 0}, {"name": "surgebadge", "cost": 0}, {"name": "nsolarizermerge", "cost": 0}, {"name": "nlunarizermerge", "cost": 0}, {"name": "nsolarizersplit", "cost": 0}, {"name": "nlunarizersplit", "cost": 0}, {"name": "ilimasnormaliumz", "cost": 0}, {"name": "leftpokeball", "cost": 0}, {"name": "<PERSON><PERSON><PERSON>", "cost": 0}, {"name": "rotobargain", "cost": 0}, {"name": "rotop<PERSON><PERSON><PERSON>", "cost": 0}, {"name": "rotoexppoints", "cost": 0}, {"name": "rotofriendship", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "rotostealth", "cost": 0}, {"name": "rotohprest<PERSON>", "cost": 0}, {"name": "rotopprest<PERSON>", "cost": 0}, {"name": "rotoboost", "cost": 0}, {"name": "rotocatch", "cost": 0}, {"name": "autograph", "cost": 0}, {"name": "pokemonbox", "cost": 0}, {"name": "medicinepocket", "cost": 0}, {"name": "candyjar", "cost": 0}, {"name": "poweruppocket", "cost": 0}, {"name": "clothingtrunk", "cost": 0}, {"name": "catchingpocket", "cost": 0}, {"name": "battlepocket", "cost": 0}, {"name": "secretkeyletsgo", "cost": 0}, {"name": "ssticketletsgo", "cost": 0}, {"name": "parcelletsgo", "cost": 0}, {"name": "cardkeyletsgo", "cost": 0}, {"name": "leafletterpikachu", "cost": 0}, {"name": "leaflettereevee", "cost": 0}, {"name": "smallbouquet", "cost": 0}, {"name": "endorsement", "cost": 0}, {"name": "pokemonboxlink", "cost": 0}, {"name": "wishingstar", "cost": 0}, {"name": "dynamaxband", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "<PERSON><PERSON><PERSON>", "cost": 0}, {"name": "campinggear", "cost": 0}, {"name": "rustedsword", "cost": 0}, {"name": "rustedshield", "cost": 0}, {"name": "dynamaxcandy", "cost": 0}, {"name": "hitechearbuds", "cost": 0}, {"name": "wishingchip", "cost": 0}, {"name": "rotombikewatermode", "cost": 0}, {"name": "catching<PERSON>rm", "cost": 0}, {"name": "oldletter", "cost": 0}, {"name": "bandautograph", "cost": 0}, {"name": "soniasbook", "cost": 0}, {"name": "rotomcatalog", "cost": 0}, {"name": "stylecard", "cost": 0}, {"name": "armorpass", "cost": 0}, {"name": "rotombikesparklingwhite", "cost": 0}, {"name": "rotombikeglisteningblack", "cost": 0}, {"name": "expcharm", "cost": 0}, {"name": "mark<PERSON><PERSON>", "cost": 0}, {"name": "reinsofunitymerge", "cost": 0}, {"name": "reinsofunitysplit", "cost": 0}, {"name": "legendaryclue1", "cost": 0}, {"name": "legendaryclue2", "cost": 0}, {"name": "legendaryclue3", "cost": 0}, {"name": "legendarycluequestion", "cost": 0}, {"name": "crownpass", "cost": 0}, {"name": "woodencrown", "cost": 0}, {"name": "radiantpetal", "cost": 0}, {"name": "white<PERSON><PERSON><PERSON>", "cost": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 0}, {"name": "icerootcarrot", "cost": 0}, {"name": "shaderootcarrot", "cost": 0}, {"name": "reinsofunity", "cost": 0}, {"name": "adamantcrystal", "cost": 0}, {"name": "lustrousglobe", "cost": 0}, {"name": "griseouscore", "cost": 0}, {"name": "blankplate", "cost": 0}, {"name": "strangeball", "cost": 0}, {"name": "legendplate", "cost": 0}, {"name": "rotomphone", "cost": 0}, {"name": "sandwich", "cost": 0}, {"name": "koraidon’spokéball", "cost": 0}, {"name": "miraidon’spokéball", "cost": 0}, {"name": "teraorb", "cost": 0}, {"name": "scarletbook", "cost": 0}, {"name": "violetbook", "cost": 0}, {"name": "kofu’swallet", "cost": 0}, {"name": "scrollofdarkness", "cost": 0}, {"name": "scrollofwaters", "cost": 0}, {"name": "boosterenergy", "cost": 0}, {"name": "baguette", "cost": 0}, {"name": "sweether<PERSON>mystic<PERSON>", "cost": 0}, {"name": "saltyherbamystica", "cost": 0}, {"name": "sourherbamystic<PERSON>", "cost": 0}, {"name": "bitterherbamystic<PERSON>", "cost": 0}, {"name": "spicyherbamystica", "cost": 0}, {"name": "tm100", "cost": 0}, {"name": "tm101", "cost": 0}, {"name": "tm102", "cost": 0}, {"name": "tm103", "cost": 0}, {"name": "tm104", "cost": 0}, {"name": "tm105", "cost": 0}, {"name": "tm106", "cost": 0}, {"name": "tm107", "cost": 0}, {"name": "tm108", "cost": 0}, {"name": "tm109", "cost": 0}, {"name": "tm110", "cost": 0}, {"name": "tm111", "cost": 0}, {"name": "tm112", "cost": 0}, {"name": "tm113", "cost": 0}, {"name": "tm114", "cost": 0}, {"name": "tm115", "cost": 0}, {"name": "tm116", "cost": 0}, {"name": "tm117", "cost": 0}, {"name": "tm118", "cost": 0}, {"name": "tm119", "cost": 0}, {"name": "tm120", "cost": 0}, {"name": "tm121", "cost": 0}, {"name": "tm122", "cost": 0}, {"name": "tm123", "cost": 0}, {"name": "tm124", "cost": 0}, {"name": "tm125", "cost": 0}, {"name": "tm126", "cost": 0}, {"name": "tm127", "cost": 0}, {"name": "tm128", "cost": 0}, {"name": "tm129", "cost": 0}, {"name": "tm130", "cost": 0}, {"name": "tm131", "cost": 0}, {"name": "tm132", "cost": 0}, {"name": "tm133", "cost": 0}, {"name": "tm134", "cost": 0}, {"name": "tm135", "cost": 0}, {"name": "tm136", "cost": 0}, {"name": "tm137", "cost": 0}, {"name": "tm138", "cost": 0}, {"name": "tm139", "cost": 0}, {"name": "tm140", "cost": 0}, {"name": "tm141", "cost": 0}, {"name": "tm142", "cost": 0}, {"name": "tm143", "cost": 0}, {"name": "tm144", "cost": 0}, {"name": "tm145", "cost": 0}, {"name": "tm146", "cost": 0}, {"name": "tm147", "cost": 0}, {"name": "tm148", "cost": 0}, {"name": "tm149", "cost": 0}, {"name": "tm150", "cost": 0}, {"name": "tm151", "cost": 0}, {"name": "tm152", "cost": 0}, {"name": "tm153", "cost": 0}, {"name": "tm154", "cost": 0}, {"name": "tm155", "cost": 0}, {"name": "tm156", "cost": 0}, {"name": "tm157", "cost": 0}, {"name": "tm158", "cost": 0}, {"name": "tm159", "cost": 0}, {"name": "tm160", "cost": 0}, {"name": "tm161", "cost": 0}, {"name": "tm162", "cost": 0}, {"name": "tm163", "cost": 0}, {"name": "tm164", "cost": 0}, {"name": "tm165", "cost": 0}, {"name": "tm166", "cost": 0}, {"name": "tm167", "cost": 0}, {"name": "tm168", "cost": 0}, {"name": "tm169", "cost": 0}, {"name": "tm170", "cost": 0}, {"name": "tm171", "cost": 0}, {"name": "picnicset", "cost": 0}, {"name": "academybottle", "cost": 0}, {"name": "academybottle", "cost": 0}, {"name": "academycup", "cost": 0}, {"name": "academycup", "cost": 0}, {"name": "academytablecloth", "cost": 0}, {"name": "academytablecloth", "cost": 0}, {"name": "academyball", "cost": 0}, {"name": "academyball", "cost": 0}, {"name": "b&wgrasstablecloth", "cost": 0}, {"name": "bluedish", "cost": 0}, {"name": "greendish", "cost": 0}, {"name": "orangedish", "cost": 0}, {"name": "reddish", "cost": 0}, {"name": "whitedish", "cost": 0}, {"name": "yellowdish", "cost": 0}, {"name": "rotostick", "cost": 0}, {"name": "tealstylecard", "cost": 0}, {"name": "tealmask", "cost": 0}, {"name": "glimmeringcharm", "cost": 0}, {"name": "cyrstalcluster", "cost": 0}, {"name": "wellspringmask", "cost": 0}, {"name": "hearthflamemask", "cost": 0}, {"name": "cornerstonemask", "cost": 0}, {"name": "tm172", "cost": 0}, {"name": "tm173", "cost": 0}, {"name": "tm174", "cost": 0}, {"name": "tm175", "cost": 0}, {"name": "tm176", "cost": 0}, {"name": "tm177", "cost": 0}, {"name": "tm178", "cost": 0}, {"name": "tm179", "cost": 0}, {"name": "tm180", "cost": 0}, {"name": "tm181", "cost": 0}, {"name": "tm182", "cost": 0}, {"name": "tm183", "cost": 0}, {"name": "tm184", "cost": 0}, {"name": "tm185", "cost": 0}, {"name": "tm186", "cost": 0}, {"name": "tm187", "cost": 0}, {"name": "tm188", "cost": 0}, {"name": "tm189", "cost": 0}, {"name": "tm190", "cost": 0}, {"name": "tm191", "cost": 0}, {"name": "tm192", "cost": 0}, {"name": "tm193", "cost": 0}, {"name": "tm194", "cost": 0}, {"name": "tm195", "cost": 0}, {"name": "tm196", "cost": 0}, {"name": "tm197", "cost": 0}, {"name": "tm198", "cost": 0}, {"name": "tm199", "cost": 0}, {"name": "tm200", "cost": 0}, {"name": "tm201", "cost": 0}, {"name": "tm202", "cost": 0}, {"name": "tm203", "cost": 0}, {"name": "tm204", "cost": 0}, {"name": "tm205", "cost": 0}, {"name": "tm206", "cost": 0}, {"name": "tm207", "cost": 0}, {"name": "tm208", "cost": 0}, {"name": "tm209", "cost": 0}, {"name": "tm210", "cost": 0}, {"name": "tm211", "cost": 0}, {"name": "tm212", "cost": 0}, {"name": "tm213", "cost": 0}, {"name": "tm214", "cost": 0}, {"name": "tm215", "cost": 0}, {"name": "tm216", "cost": 0}, {"name": "tm217", "cost": 0}, {"name": "tm218", "cost": 0}, {"name": "tm219", "cost": 0}, {"name": "tm220", "cost": 0}, {"name": "tm221", "cost": 0}, {"name": "tm222", "cost": 0}, {"name": "tm223", "cost": 0}, {"name": "tm224", "cost": 0}, {"name": "tm225", "cost": 0}, {"name": "tm226", "cost": 0}, {"name": "tm227", "cost": 0}, {"name": "tm228", "cost": 0}, {"name": "tm229", "cost": 0}, {"name": "lastrangeball", "cost": 0}, {"name": "lapokeball", "cost": 0}, {"name": "lagreatball", "cost": 0}, {"name": "laultraball", "cost": 0}, {"name": "laheavyball", "cost": 0}, {"name": "laleadenball", "cost": 0}, {"name": "lagigatonball", "cost": 0}, {"name": "lafeatherball", "cost": 0}, {"name": "lawingball", "cost": 0}, {"name": "lajetball", "cost": 0}, {"name": "laoriginball", "cost": 0}, {"name": "blackaugurite", "cost": 0}, {"name": "peatblock", "cost": 0}], "normal": [{"name": "ultraball", "cost": 800}, {"name": "greatball", "cost": 600}, {"name": "pokeball", "cost": 200}, {"name": "netball", "cost": 1000}, {"name": "diveball", "cost": 1000}, {"name": "nestball", "cost": 1000}, {"name": "repeatball", "cost": 1000}, {"name": "timerball", "cost": 1000}, {"name": "luxuryball", "cost": 3000}, {"name": "premierball", "cost": 20}, {"name": "duskball", "cost": 1000}, {"name": "healball", "cost": 300}, {"name": "quickball", "cost": 1000}, {"name": "potion", "cost": 200}, {"name": "antidote", "cost": 200}, {"name": "burnheal", "cost": 200}, {"name": "iceheal", "cost": 200}, {"name": "awakening", "cost": 200}, {"name": "paralyzeheal", "cost": 200}, {"name": "<PERSON><PERSON><PERSON>", "cost": 3000}, {"name": "maxpotion", "cost": 2500}, {"name": "hyperpotion", "cost": 1500}, {"name": "superpotion", "cost": 700}, {"name": "fullheal", "cost": 400}, {"name": "revive", "cost": 2000}, {"name": "maxrevive", "cost": 4000}, {"name": "freshwater", "cost": 200}, {"name": "sodapop", "cost": 300}, {"name": "lemonade", "cost": 400}, {"name": "moomoomilk", "cost": 600}, {"name": "energypowder", "cost": 500}, {"name": "energyroot", "cost": 1200}, {"name": "healpowder", "cost": 300}, {"name": "<PERSON><PERSON><PERSON>", "cost": 2800}, {"name": "ether", "cost": 1200}, {"name": "maxether", "cost": 2000}, {"name": "elixir", "cost": 3000}, {"name": "maxelixir", "cost": 4500}, {"name": "lavacookie", "cost": 350}, {"name": "berryjuice", "cost": 200}, {"name": "sacredash", "cost": 50000}, {"name": "hpup", "cost": 10000}, {"name": "protein", "cost": 10000}, {"name": "iron", "cost": 10000}, {"name": "carbos", "cost": 10000}, {"name": "calcium", "cost": 10000}, {"name": "rarecandy", "cost": 10000}, {"name": "ppup", "cost": 10000}, {"name": "zinc", "cost": 10000}, {"name": "ppmax", "cost": 10000}, {"name": "oldgateau", "cost": 350}, {"name": "guardspec", "cost": 1500}, {"name": "direhit", "cost": 1000}, {"name": "xattack", "cost": 1000}, {"name": "xdefense", "cost": 2000}, {"name": "xspeed", "cost": 1000}, {"name": "xaccuracy", "cost": 1000}, {"name": "xspatk", "cost": 1000}, {"name": "xspdef", "cost": 2000}, {"name": "<PERSON><PERSON>", "cost": 300}, {"name": "fluffytail", "cost": 100}, {"name": "blueflute", "cost": 20}, {"name": "yellowflute", "cost": 20}, {"name": "redflute", "cost": 20}, {"name": "blackflute", "cost": 20}, {"name": "whiteflute", "cost": 20}, {"name": "shoalsalt", "cost": 20}, {"name": "shoalshell", "cost": 20}, {"name": "redshard", "cost": 1000}, {"name": "blueshard", "cost": 1000}, {"name": "yellowshard", "cost": 1000}, {"name": "greenshard", "cost": 1000}, {"name": "superrepel", "cost": 700}, {"name": "maxrepel", "cost": 900}, {"name": "escaperope", "cost": 300}, {"name": "repel", "cost": 400}, {"name": "sunstone", "cost": 3000}, {"name": "moonstone", "cost": 3000}, {"name": "firestone", "cost": 3000}, {"name": "thunderstone", "cost": 3000}, {"name": "waterstone", "cost": 3000}, {"name": "leafstone", "cost": 3000}, {"name": "tiny<PERSON><PERSON><PERSON>", "cost": 500}, {"name": "big<PERSON><PERSON><PERSON>", "cost": 5000}, {"name": "pearl", "cost": 2000}, {"name": "big<PERSON>l", "cost": 8000}, {"name": "stardust", "cost": 3000}, {"name": "starpiece", "cost": 12000}, {"name": "nugget", "cost": 10000}, {"name": "heartscale", "cost": 100}, {"name": "honey", "cost": 900}, {"name": "growthmulch", "cost": 200}, {"name": "dampmulch", "cost": 200}, {"name": "stablemulch", "cost": 200}, {"name": "gooeymulch", "cost": 200}, {"name": "rootfossil", "cost": 7000}, {"name": "clawfossil", "cost": 7000}, {"name": "helixfossil", "cost": 7000}, {"name": "domefossil", "cost": 7000}, {"name": "oldamber", "cost": 10000}, {"name": "armorfossil", "cost": 7000}, {"name": "skullfossil", "cost": 7000}, {"name": "rarebone", "cost": 5000}, {"name": "shinystone", "cost": 3000}, {"name": "duskstone", "cost": 3000}, {"name": "dawnstone", "cost": 3000}, {"name": "ovalstone", "cost": 2000}, {"name": "oddkeystone", "cost": 2100}, {"name": "grassmail", "cost": 50}, {"name": "flamemail", "cost": 50}, {"name": "bubblemail", "cost": 50}, {"name": "bloommail", "cost": 50}, {"name": "tunnelmail", "cost": 50}, {"name": "steelmail", "cost": 50}, {"name": "heartmail", "cost": 50}, {"name": "snowmail", "cost": 50}, {"name": "spacemail", "cost": 50}, {"name": "airmail", "cost": 50}, {"name": "mosaicmail", "cost": 50}, {"name": "brickmail", "cost": 50}, {"name": "cheriberry", "cost": 80}, {"name": "<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "pechaberry", "cost": 80}, {"name": "rawstberry", "cost": 80}, {"name": "as<PERSON><PERSON>", "cost": 80}, {"name": "le<PERSON><PERSON>", "cost": 80}, {"name": "oranberry", "cost": 80}, {"name": "persimberry", "cost": 80}, {"name": "lumberry", "cost": 80}, {"name": "<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "figyberry", "cost": 80}, {"name": "wikiberry", "cost": 80}, {"name": "magoberry", "cost": 80}, {"name": "aguavberry", "cost": 80}, {"name": "iapapaberry", "cost": 80}, {"name": "razzberry", "cost": 200}, {"name": "blukberry", "cost": 20}, {"name": "na<PERSON><PERSON><PERSON>", "cost": 200}, {"name": "<PERSON><PERSON><PERSON>", "cost": 20}, {"name": "pin<PERSON>berry", "cost": 200}, {"name": "pomegberry", "cost": 80}, {"name": "kelpsyberry", "cost": 80}, {"name": "qualotberry", "cost": 80}, {"name": "hondewberry", "cost": 80}, {"name": "grepaberry", "cost": 80}, {"name": "tamatoberry", "cost": 80}, {"name": "cornnberry", "cost": 20}, {"name": "magostberry", "cost": 20}, {"name": "rabutaberry", "cost": 20}, {"name": "nomelberry", "cost": 20}, {"name": "spelonberry", "cost": 20}, {"name": "pam<PERSON><PERSON>", "cost": 20}, {"name": "watmelberry", "cost": 20}, {"name": "durinberry", "cost": 20}, {"name": "belueberry", "cost": 20}, {"name": "occaberry", "cost": 80}, {"name": "pass<PERSON>berry", "cost": 80}, {"name": "wacanberry", "cost": 80}, {"name": "rindoberry", "cost": 80}, {"name": "yacheberry", "cost": 80}, {"name": "chopleberry", "cost": 80}, {"name": "kebiaberry", "cost": 80}, {"name": "shucaberry", "cost": 80}, {"name": "cobaberry", "cost": 80}, {"name": "payapaberry", "cost": 80}, {"name": "tangaberry", "cost": 80}, {"name": "chartiberry", "cost": 80}, {"name": "kasibberry", "cost": 80}, {"name": "habanberry", "cost": 80}, {"name": "colburberry", "cost": 80}, {"name": "babiriberry", "cost": 80}, {"name": "chilanberry", "cost": 80}, {"name": "lie<PERSON><PERSON>", "cost": 80}, {"name": "gan<PERSON><PERSON>", "cost": 80}, {"name": "salacberry", "cost": 80}, {"name": "petayaberry", "cost": 80}, {"name": "apicotberry", "cost": 80}, {"name": "lansatberry", "cost": 80}, {"name": "starfberry", "cost": 80}, {"name": "enigmaberry", "cost": 80}, {"name": "micleberry", "cost": 80}, {"name": "custapberry", "cost": 80}, {"name": "jabocaberry", "cost": 80}, {"name": "row<PERSON><PERSON>", "cost": 80}, {"name": "brightpowder", "cost": 4000}, {"name": "whiteherb", "cost": 4000}, {"name": "machobrace", "cost": 3000}, {"name": "quickclaw", "cost": 4000}, {"name": "soothebell", "cost": 4000}, {"name": "mentalherb", "cost": 4000}, {"name": "choiceband", "cost": 4000}, {"name": "kingsrock", "cost": 5000}, {"name": "silverpowder", "cost": 1000}, {"name": "amuletcoin", "cost": 10000}, {"name": "cleansetag", "cost": 5000}, {"name": "deepseatooth", "cost": 2000}, {"name": "deepseascale", "cost": 2000}, {"name": "smokeball", "cost": 4000}, {"name": "everstone", "cost": 3000}, {"name": "focusband", "cost": 4000}, {"name": "<PERSON><PERSON>g", "cost": 10000}, {"name": "<PERSON><PERSON><PERSON>", "cost": 4000}, {"name": "metalcoat", "cost": 2000}, {"name": "leftovers", "cost": 4000}, {"name": "dragonscale", "cost": 2000}, {"name": "lightball", "cost": 1000}, {"name": "softsand", "cost": 1000}, {"name": "hardstone", "cost": 1000}, {"name": "miracleseed", "cost": 1000}, {"name": "blackglasses", "cost": 1000}, {"name": "blackbelt", "cost": 1000}, {"name": "magnet", "cost": 1000}, {"name": "mysticwater", "cost": 1000}, {"name": "sharpbeak", "cost": 1000}, {"name": "poisonbarb", "cost": 1000}, {"name": "nevermeltice", "cost": 1000}, {"name": "spelltag", "cost": 1000}, {"name": "twistedspoon", "cost": 1000}, {"name": "charcoal", "cost": 1000}, {"name": "dragonfang", "cost": 1000}, {"name": "silkscarf", "cost": 1000}, {"name": "upgrade", "cost": 2000}, {"name": "shellbell", "cost": 4000}, {"name": "seaincense", "cost": 2000}, {"name": "laxincense", "cost": 5000}, {"name": "luckypunch", "cost": 1000}, {"name": "metalpowder", "cost": 1000}, {"name": "thickclub", "cost": 1000}, {"name": "stick", "cost": 1000}, {"name": "redscarf", "cost": 100}, {"name": "bluescarf", "cost": 100}, {"name": "pinkscarf", "cost": 100}, {"name": "greenscarf", "cost": 100}, {"name": "yellowscarf", "cost": 100}, {"name": "widelens", "cost": 4000}, {"name": "muscleband", "cost": 4000}, {"name": "wiseglasses", "cost": 4000}, {"name": "expertbelt", "cost": 4000}, {"name": "lightclay", "cost": 4000}, {"name": "lifeorb", "cost": 4000}, {"name": "powerherb", "cost": 4000}, {"name": "toxicorb", "cost": 4000}, {"name": "flameorb", "cost": 4000}, {"name": "quickpowder", "cost": 1000}, {"name": "focussash", "cost": 4000}, {"name": "zoomlens", "cost": 4000}, {"name": "metronome", "cost": 4000}, {"name": "ironball", "cost": 4000}, {"name": "laggingtail", "cost": 4000}, {"name": "destinyknot", "cost": 4000}, {"name": "blacksludge", "cost": 4000}, {"name": "icyrock", "cost": 4000}, {"name": "smoothrock", "cost": 4000}, {"name": "heatrock", "cost": 4000}, {"name": "damprock", "cost": 4000}, {"name": "grip<PERSON>law", "cost": 4000}, {"name": "choicescarf", "cost": 4000}, {"name": "stickybarb", "cost": 4000}, {"name": "powerbracer", "cost": 3000}, {"name": "powerbelt", "cost": 3000}, {"name": "powerlens", "cost": 3000}, {"name": "powerband", "cost": 3000}, {"name": "poweranklet", "cost": 3000}, {"name": "powerweight", "cost": 3000}, {"name": "shedshell", "cost": 4000}, {"name": "bigroot", "cost": 4000}, {"name": "choicespecs", "cost": 4000}, {"name": "flameplate", "cost": 1000}, {"name": "splashplate", "cost": 1000}, {"name": "zapplate", "cost": 1000}, {"name": "meadowplate", "cost": 1000}, {"name": "icicleplate", "cost": 1000}, {"name": "fistplate", "cost": 1000}, {"name": "toxicplate", "cost": 1000}, {"name": "earthplate", "cost": 1000}, {"name": "skyplate", "cost": 1000}, {"name": "mindplate", "cost": 1000}, {"name": "insectplate", "cost": 1000}, {"name": "stoneplate", "cost": 1000}, {"name": "spookyplate", "cost": 1000}, {"name": "dracoplate", "cost": 1000}, {"name": "dreadplate", "cost": 1000}, {"name": "ironplate", "cost": 1000}, {"name": "oddincense", "cost": 2000}, {"name": "rockincense", "cost": 2000}, {"name": "fullincense", "cost": 5000}, {"name": "waveincense", "cost": 2000}, {"name": "roseincense", "cost": 2000}, {"name": "luckincense", "cost": 11000}, {"name": "pureincense", "cost": 6000}, {"name": "protector", "cost": 2000}, {"name": "electirizer", "cost": 2000}, {"name": "magmarizer", "cost": 2000}, {"name": "dubiousdisc", "cost": 2000}, {"name": "reapercloth", "cost": 2000}, {"name": "razorclaw", "cost": 5000}, {"name": "razorfang", "cost": 5000}, {"name": "tm01", "cost": 40000}, {"name": "tm02", "cost": 1000}, {"name": "tm03", "cost": 50000}, {"name": "tm04", "cost": 50000}, {"name": "tm05", "cost": 50000}, {"name": "tm06", "cost": 1000}, {"name": "tm07", "cost": 1000}, {"name": "tm08", "cost": 50000}, {"name": "tm09", "cost": 50000}, {"name": "tm10", "cost": 1000}, {"name": "tm11", "cost": 1000}, {"name": "tm12", "cost": 50000}, {"name": "tm13", "cost": 10000}, {"name": "tm14", "cost": 1000}, {"name": "tm15", "cost": 1000}, {"name": "tm16", "cost": 1000}, {"name": "tm17", "cost": 10000}, {"name": "tm18", "cost": 10000}, {"name": "tm19", "cost": 10000}, {"name": "tm20", "cost": 100000}, {"name": "tm21", "cost": 1000}, {"name": "tm22", "cost": 1000}, {"name": "tm23", "cost": 10000}, {"name": "tm24", "cost": 1000}, {"name": "tm25", "cost": 10000}, {"name": "tm26", "cost": 1000}, {"name": "tm27", "cost": 1000}, {"name": "tm28", "cost": 100000}, {"name": "tm29", "cost": 1000}, {"name": "tm30", "cost": 1000}, {"name": "tm31", "cost": 1000}, {"name": "tm32", "cost": 10000}, {"name": "tm33", "cost": 10000}, {"name": "tm34", "cost": 10000}, {"name": "tm35", "cost": 10000}, {"name": "tm36", "cost": 1000}, {"name": "tm37", "cost": 1000}, {"name": "tm38", "cost": 1000}, {"name": "tm39", "cost": 1000}, {"name": "tm40", "cost": 1000}, {"name": "tm41", "cost": 10000}, {"name": "tm42", "cost": 1000}, {"name": "tm43", "cost": 1000}, {"name": "tm44", "cost": 100000}, {"name": "tm45", "cost": 1000}, {"name": "tm46", "cost": 30000}, {"name": "tm47", "cost": 1000}, {"name": "tm48", "cost": 1000}, {"name": "tm49", "cost": 1000}, {"name": "tm50", "cost": 10000}, {"name": "tm51", "cost": 1000}, {"name": "tm52", "cost": 100000}, {"name": "tm53", "cost": 1000}, {"name": "tm54", "cost": 1000}, {"name": "tm55", "cost": 10000}, {"name": "tm56", "cost": 1000}, {"name": "tm57", "cost": 1000}, {"name": "tm58", "cost": 1000}, {"name": "tm59", "cost": 100000}, {"name": "tm60", "cost": 30000}, {"name": "tm61", "cost": 30000}, {"name": "tm62", "cost": 30000}, {"name": "tm63", "cost": 50000}, {"name": "tm64", "cost": 1000}, {"name": "tm65", "cost": 1000}, {"name": "tm66", "cost": 30000}, {"name": "tm67", "cost": 30000}, {"name": "tm68", "cost": 30000}, {"name": "tm69", "cost": 1000}, {"name": "tm70", "cost": 50000}, {"name": "tm71", "cost": 50000}, {"name": "tm72", "cost": 50000}, {"name": "tm73", "cost": 1000}, {"name": "tm74", "cost": 1000}, {"name": "tm75", "cost": 1000}, {"name": "tm76", "cost": 10000}, {"name": "tm77", "cost": 1000}, {"name": "tm78", "cost": 1000}, {"name": "tm79", "cost": 1000}, {"name": "tm80", "cost": 1000}, {"name": "tm81", "cost": 1000}, {"name": "tm82", "cost": 1000}, {"name": "tm83", "cost": 100000}, {"name": "tm84", "cost": 1000}, {"name": "tm85", "cost": 1000}, {"name": "tm86", "cost": 1000}, {"name": "tm87", "cost": 1000}, {"name": "tm88", "cost": 20000}, {"name": "tm89", "cost": 20000}, {"name": "tm90", "cost": 20000}, {"name": "tm91", "cost": 20000}, {"name": "tm92", "cost": 100000}, {"name": "sportball", "cost": 300}, {"name": "redapricorn", "cost": 200}, {"name": "blueapricorn", "cost": 200}, {"name": "yellowapricorn", "cost": 200}, {"name": "greenapricorn", "cost": 200}, {"name": "pinkapricorn", "cost": 200}, {"name": "whiteapricorn", "cost": 200}, {"name": "blackapricorn", "cost": 200}, {"name": "ragecandybar", "cost": 350}, {"name": "orangemail", "cost": 50}, {"name": "harbormail", "cost": 50}, {"name": "glittermail", "cost": 50}, {"name": "mechmail", "cost": 50}, {"name": "woodmail", "cost": 50}, {"name": "wavemail", "cost": 50}, {"name": "beadmail", "cost": 50}, {"name": "shadowmail", "cost": 50}, {"name": "tropicmail", "cost": 50}, {"name": "dreammail", "cost": 50}, {"name": "fabmail", "cost": 50}, {"name": "sweetheart", "cost": 3000}, {"name": "greetmail", "cost": 50}, {"name": "favoredmail", "cost": 50}, {"name": "rsvpmail", "cost": 50}, {"name": "thanksmail", "cost": 50}, {"name": "inquirymail", "cost": 50}, {"name": "likemail", "cost": 50}, {"name": "replymail", "cost": 50}, {"name": "bridgemails", "cost": 50}, {"name": "bridgemaild", "cost": 50}, {"name": "bridge<PERSON>t", "cost": 50}, {"name": "bridgemailv", "cost": 50}, {"name": "bridgemailm", "cost": 50}, {"name": "prismscale", "cost": 2000}, {"name": "eviolite", "cost": 4000}, {"name": "floatstone", "cost": 4000}, {"name": "<PERSON><PERSON><PERSON>", "cost": 4000}, {"name": "airballoon", "cost": 4000}, {"name": "redcard", "cost": 4000}, {"name": "ringtarget", "cost": 4000}, {"name": "bindingband", "cost": 4000}, {"name": "absorbbulb", "cost": 4000}, {"name": "cellbattery", "cost": 4000}, {"name": "ejectbutton", "cost": 4000}, {"name": "firegem", "cost": 200}, {"name": "watergem", "cost": 200}, {"name": "electricgem", "cost": 200}, {"name": "grassgem", "cost": 200}, {"name": "icegem", "cost": 200}, {"name": "fightinggem", "cost": 200}, {"name": "poisongem", "cost": 200}, {"name": "groundgem", "cost": 200}, {"name": "flyinggem", "cost": 200}, {"name": "psychicgem", "cost": 200}, {"name": "buggem", "cost": 200}, {"name": "rockgem", "cost": 200}, {"name": "ghostgem", "cost": 200}, {"name": "darkgem", "cost": 200}, {"name": "steelgem", "cost": 200}, {"name": "healthwing", "cost": 300}, {"name": "musclewing", "cost": 300}, {"name": "resistwing", "cost": 300}, {"name": "geniuswing", "cost": 300}, {"name": "cleverwing", "cost": 300}, {"name": "swiftwing", "cost": 300}, {"name": "prettywing", "cost": 1000}, {"name": "coverfossil", "cost": 7000}, {"name": "plumefossil", "cost": 7000}, {"name": "passorb", "cost": 200}, {"name": "poketoy", "cost": 100}, {"name": "balmmushroom", "cost": 15000}, {"name": "bignugget", "cost": 40000}, {"name": "pearlstring", "cost": 20000}, {"name": "cometshard", "cost": 25000}, {"name": "relicgold", "cost": 60000}, {"name": "casteliacone", "cost": 350}, {"name": "tm93", "cost": 1000}, {"name": "tm94", "cost": 10000}, {"name": "tm95", "cost": 1000}, {"name": "dragongem", "cost": 200}, {"name": "normalgem", "cost": 4000}, {"name": "weaknesspolicy", "cost": 1000}, {"name": "assaultvest", "cost": 1000}, {"name": "pixie<PERSON>", "cost": 1000}, {"name": "abilitycapsule", "cost": 10000}, {"name": "whippeddream", "cost": 2000}, {"name": "sachet", "cost": 2000}, {"name": "luminousmoss", "cost": 4000}, {"name": "snowball", "cost": 4000}, {"name": "safetygoggles", "cost": 4000}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 200}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 200}, {"name": "boostmulch", "cost": 200}, {"name": "amazemulch", "cost": 200}, {"name": "roseliberry", "cost": 80}, {"name": "keeberry", "cost": 80}, {"name": "marangaberry", "cost": 80}, {"name": "discountcoupon", "cost": 20}, {"name": "strangesouvenir", "cost": 3000}, {"name": "lumiosegalette", "cost": 350}, {"name": "jawfossil", "cost": 7000}, {"name": "sailfossil", "cost": 7000}, {"name": "fairygem", "cost": 200}, {"name": "tm96", "cost": 1000}, {"name": "tm97", "cost": 1000}, {"name": "tm98", "cost": 1000}, {"name": "tm99", "cost": 1000}, {"name": "tm100", "cost": 5000}, {"name": "shaloursable", "cost": 350}, {"name": "bottlecap", "cost": 5000}, {"name": "goldbottlecap", "cost": 10000}, {"name": "festivalticket", "cost": 10}, {"name": "adrenalineorb", "cost": 4000}, {"name": "icestone", "cost": 3000}, {"name": "bigmalasada", "cost": 350}, {"name": "rednectar", "cost": 300}, {"name": "yellownectar", "cost": 300}, {"name": "pinknectar", "cost": 300}, {"name": "purplenectar", "cost": 300}, {"name": "terrainextender", "cost": 4000}, {"name": "protectivepads", "cost": 4000}, {"name": "electricseed", "cost": 4000}, {"name": "psychicseed", "cost": 4000}, {"name": "mistyseed", "cost": 4000}, {"name": "grassyseed", "cost": 4000}, {"name": "fightingmemory", "cost": 1000}, {"name": "flyingmemory", "cost": 1000}, {"name": "poisonmemory", "cost": 1000}, {"name": "groundmemory", "cost": 1000}, {"name": "rockmemory", "cost": 1000}, {"name": "bugmemory", "cost": 1000}, {"name": "ghostmemory", "cost": 1000}, {"name": "steelmemory", "cost": 1000}, {"name": "firememory", "cost": 1000}, {"name": "watermemory", "cost": 1000}, {"name": "grassmemory", "cost": 1000}, {"name": "electricmemory", "cost": 1000}, {"name": "psychicmemory", "cost": 1000}, {"name": "icememory", "cost": 1000}, {"name": "dragonmemory", "cost": 1000}, {"name": "darkmemory", "cost": 1000}, {"name": "fairymemory", "cost": 1000}, {"name": "silverrazzberry", "cost": 1000}, {"name": "goldenrazzberry", "cost": 5000}, {"name": "silvernanabberry", "cost": 1000}, {"name": "goldennanabberry", "cost": 5000}, {"name": "silverpinapberry", "cost": 1000}, {"name": "goldenpinapberry", "cost": 5000}, {"name": "stretchyspring", "cost": 20}, {"name": "chalkystone", "cost": 60}, {"name": "marble", "cost": 300}, {"name": "loneearring", "cost": 600}, {"name": "beachglass", "cost": 800}, {"name": "goldleaf", "cost": 1000}, {"name": "silverleaf", "cost": 1000}, {"name": "polishedmudball", "cost": 1200}, {"name": "tropicalshell", "cost": 2000}, {"name": "lure", "cost": 400}, {"name": "superlure", "cost": 700}, {"name": "maxlure", "cost": 900}, {"name": "pewtercrunchies", "cost": 250}, {"name": "healthcandy", "cost": 20}, {"name": "mighty<PERSON>dy", "cost": 20}, {"name": "toughcandy", "cost": 20}, {"name": "smartcandy", "cost": 20}, {"name": "couragecandy", "cost": 20}, {"name": "quickcandy", "cost": 20}, {"name": "healthcandyl", "cost": 20}, {"name": "mightycandyl", "cost": 20}, {"name": "toughcandyl", "cost": 20}, {"name": "smartcandyl", "cost": 20}, {"name": "couragecandyl", "cost": 20}, {"name": "quickcandyl", "cost": 20}, {"name": "healthcandyxl", "cost": 20}, {"name": "mightycandyxl", "cost": 20}, {"name": "toughcandyxl", "cost": 20}, {"name": "smartcandyxl", "cost": 20}, {"name": "couragecandyxl", "cost": 20}, {"name": "quickcandyxl", "cost": 20}, {"name": "bulbasaurcandy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON>dy", "cost": 20}, {"name": "squirtlecandy", "cost": 20}, {"name": "caterpiecandy", "cost": 20}, {"name": "weedlecandy", "cost": 20}, {"name": "pidgeycandy", "cost": 20}, {"name": "rat<PERSON><PERSON><PERSON>", "cost": 20}, {"name": "spearowcandy", "cost": 20}, {"name": "ekanscandy", "cost": 20}, {"name": "pikachucandy", "cost": 20}, {"name": "sandshrewcandy", "cost": 20}, {"name": "ni<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "nidoranmcandy", "cost": 20}, {"name": "clefairycandy", "cost": 20}, {"name": "vulpixcandy", "cost": 20}, {"name": "jigglypuffcandy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "oddishcandy", "cost": 20}, {"name": "parascandy", "cost": 20}, {"name": "venonatcandy", "cost": 20}, {"name": "diglettcandy", "cost": 20}, {"name": "meowthcandy", "cost": 20}, {"name": "psyduckcandy", "cost": 20}, {"name": "man<PERSON><PERSON>dy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "poliwagcandy", "cost": 20}, {"name": "abracandy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "bellsproutcandy", "cost": 20}, {"name": "tentacoolcandy", "cost": 20}, {"name": "geodudecandy", "cost": 20}, {"name": "pony<PERSON>can<PERSON>", "cost": 20}, {"name": "slowpokecandy", "cost": 20}, {"name": "magnemitecandy", "cost": 20}, {"name": "farfetchdcandy", "cost": 20}, {"name": "doduocandy", "cost": 20}, {"name": "seelcandy", "cost": 20}, {"name": "grimercandy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "gastlycandy", "cost": 20}, {"name": "onixcandy", "cost": 20}, {"name": "drow<PERSON><PERSON><PERSON>", "cost": 20}, {"name": "krabbycandy", "cost": 20}, {"name": "voltorbcandy", "cost": 20}, {"name": "exeggcutecandy", "cost": 20}, {"name": "cu<PERSON><PERSON>dy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "hitmonchancandy", "cost": 20}, {"name": "lickitungcandy", "cost": 20}, {"name": "koffingcandy", "cost": 20}, {"name": "rhy<PERSON><PERSON>dy", "cost": 20}, {"name": "chanseycandy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "kangaskhancandy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON>", "cost": 20}, {"name": "goldeencandy", "cost": 20}, {"name": "staryucandy", "cost": 20}, {"name": "mrmimecandy", "cost": 20}, {"name": "scythercandy", "cost": 20}, {"name": "jynxcandy", "cost": 20}, {"name": "electabuzzcandy", "cost": 20}, {"name": "pinsircandy", "cost": 20}, {"name": "tauroscandy", "cost": 20}, {"name": "magikarpcandy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "dittocandy", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 20}, {"name": "porygoncandy", "cost": 20}, {"name": "omanytecandy", "cost": 20}, {"name": "kabutocandy", "cost": 20}, {"name": "aerodactylcandy", "cost": 20}, {"name": "snor<PERSON><PERSON><PERSON>", "cost": 20}, {"name": "artic<PERSON><PERSON><PERSON>", "cost": 20}, {"name": "zapdoscandy", "cost": 20}, {"name": "moltrescandy", "cost": 20}, {"name": "dratinicandy", "cost": 20}, {"name": "mewtwocandy", "cost": 20}, {"name": "mewcandy", "cost": 20}, {"name": "meltancandy", "cost": 20}, {"name": "magmarcandy", "cost": 20}, {"name": "sausages", "cost": 400}, {"name": "bobs<PERSON><PERSON>", "cost": 950}, {"name": "bachsfoodtin", "cost": 950}, {"name": "tinofbeans", "cost": 400}, {"name": "bread", "cost": 150}, {"name": "pasta", "cost": 150}, {"name": "mixedmushrooms", "cost": 400}, {"name": "smokepoketail", "cost": 2200}, {"name": "largeleek", "cost": 2200}, {"name": "fancyapple", "cost": 2200}, {"name": "brittlebones", "cost": 950}, {"name": "packofpotatoes", "cost": 400}, {"name": "pungentroot", "cost": 950}, {"name": "saladmix", "cost": 400}, {"name": "friedfood", "cost": 150}, {"name": "<PERSON>egg", "cost": 2200}, {"name": "fossilizedbird", "cost": 5000}, {"name": "fossilizedfish", "cost": 5000}, {"name": "fossilizeddrake", "cost": 5000}, {"name": "fossilizeddino", "cost": 5000}, {"name": "strawberrysweet", "cost": 500}, {"name": "lovesweet", "cost": 500}, {"name": "berrysweet", "cost": 500}, {"name": "cloversweet", "cost": 500}, {"name": "flowersweet", "cost": 500}, {"name": "starsweet", "cost": 500}, {"name": "ribbonsweet", "cost": 500}, {"name": "sweetapple", "cost": 2200}, {"name": "tartapple", "cost": 2200}, {"name": "throatspray", "cost": 4000}, {"name": "ejectpack", "cost": 4000}, {"name": "heavydutyboots", "cost": 4000}, {"name": "blunderpolicy", "cost": 4000}, {"name": "roomservice", "cost": 4000}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 4000}, {"name": "expcandyxs", "cost": 20}, {"name": "expcandys", "cost": 240}, {"name": "expcandym", "cost": 1000}, {"name": "expcandyl", "cost": 3000}, {"name": "expcandyxl", "cost": 10000}, {"name": "tr00", "cost": 4000}, {"name": "tr01", "cost": 6000}, {"name": "tr02", "cost": 10000}, {"name": "tr03", "cost": 16000}, {"name": "tr04", "cost": 10000}, {"name": "tr05", "cost": 10000}, {"name": "tr06", "cost": 16000}, {"name": "tr07", "cost": 6000}, {"name": "tr08", "cost": 10000}, {"name": "tr09", "cost": 16000}, {"name": "tr10", "cost": 16000}, {"name": "tr11", "cost": 10000}, {"name": "tr12", "cost": 4000}, {"name": "tr13", "cost": 2000}, {"name": "tr14", "cost": 2000}, {"name": "tr15", "cost": 16000}, {"name": "tr16", "cost": 6000}, {"name": "tr17", "cost": 4000}, {"name": "tr18", "cost": 6000}, {"name": "tr19", "cost": 4000}, {"name": "tr20", "cost": 6000}, {"name": "tr21", "cost": 4000}, {"name": "tr22", "cost": 10000}, {"name": "tr23", "cost": 4000}, {"name": "tr24", "cost": 16000}, {"name": "tr25", "cost": 6000}, {"name": "tr26", "cost": 2000}, {"name": "tr27", "cost": 4000}, {"name": "tr28", "cost": 16000}, {"name": "tr29", "cost": 4000}, {"name": "tr30", "cost": 4000}, {"name": "tr31", "cost": 10000}, {"name": "tr32", "cost": 6000}, {"name": "tr33", "cost": 6000}, {"name": "tr34", "cost": 6000}, {"name": "tr35", "cost": 6000}, {"name": "tr36", "cost": 10000}, {"name": "tr37", "cost": 4000}, {"name": "tr38", "cost": 4000}, {"name": "tr39", "cost": 16000}, {"name": "tr40", "cost": 2000}, {"name": "tr41", "cost": 6000}, {"name": "tr42", "cost": 10000}, {"name": "tr43", "cost": 16000}, {"name": "tr44", "cost": 4000}, {"name": "tr45", "cost": 10000}, {"name": "tr46", "cost": 4000}, {"name": "tr47", "cost": 6000}, {"name": "tr48", "cost": 4000}, {"name": "tr49", "cost": 4000}, {"name": "tr50", "cost": 10000}, {"name": "tr51", "cost": 4000}, {"name": "tr52", "cost": 6000}, {"name": "tr53", "cost": 16000}, {"name": "tr54", "cost": 4000}, {"name": "tr55", "cost": 16000}, {"name": "tr56", "cost": 6000}, {"name": "tr57", "cost": 6000}, {"name": "tr58", "cost": 6000}, {"name": "tr59", "cost": 6000}, {"name": "tr60", "cost": 6000}, {"name": "tr61", "cost": 10000}, {"name": "tr62", "cost": 6000}, {"name": "tr63", "cost": 6000}, {"name": "tr64", "cost": 16000}, {"name": "tr65", "cost": 10000}, {"name": "tr66", "cost": 16000}, {"name": "tr67", "cost": 10000}, {"name": "tr68", "cost": 4000}, {"name": "tr69", "cost": 6000}, {"name": "tr70", "cost": 10000}, {"name": "tr71", "cost": 16000}, {"name": "tr72", "cost": 16000}, {"name": "tr73", "cost": 16000}, {"name": "tr74", "cost": 10000}, {"name": "tr75", "cost": 16000}, {"name": "tr76", "cost": 6000}, {"name": "tr77", "cost": 6000}, {"name": "tr78", "cost": 10000}, {"name": "tr79", "cost": 6000}, {"name": "tr80", "cost": 6000}, {"name": "tr81", "cost": 6000}, {"name": "tr82", "cost": 4000}, {"name": "tr83", "cost": 4000}, {"name": "tr84", "cost": 6000}, {"name": "tr85", "cost": 2000}, {"name": "tr86", "cost": 10000}, {"name": "tr87", "cost": 6000}, {"name": "tr88", "cost": 6000}, {"name": "tr89", "cost": 16000}, {"name": "tr90", "cost": 10000}, {"name": "tr91", "cost": 4000}, {"name": "tr92", "cost": 6000}, {"name": "tr93", "cost": 10000}, {"name": "tr94", "cost": 10000}, {"name": "tr95", "cost": 6000}, {"name": "tr96", "cost": 10000}, {"name": "tr97", "cost": 10000}, {"name": "tr98", "cost": 6000}, {"name": "tr99", "cost": 6000}, {"name": "tm00", "cost": 10000}, {"name": "lonelymint", "cost": 20}, {"name": "adamantmint", "cost": 20}, {"name": "naughtymint", "cost": 20}, {"name": "bravemint", "cost": 20}, {"name": "boldmint", "cost": 20}, {"name": "impishmint", "cost": 20}, {"name": "laxmint", "cost": 20}, {"name": "relaxedmint", "cost": 20}, {"name": "modestmint", "cost": 20}, {"name": "mildmint", "cost": 20}, {"name": "rashmint", "cost": 20}, {"name": "quietmint", "cost": 20}, {"name": "calmmint", "cost": 20}, {"name": "gentlemint", "cost": 20}, {"name": "carefulmint", "cost": 20}, {"name": "sassymint", "cost": 20}, {"name": "timidmint", "cost": 20}, {"name": "hastymint", "cost": 20}, {"name": "jollymint", "cost": 20}, {"name": "naivemint", "cost": 20}, {"name": "seriousmint", "cost": 20}, {"name": "wishingpiece", "cost": 20}, {"name": "crackedpot", "cost": 1600}, {"name": "chippedpot", "cost": 38000}, {"name": "fruitbunch", "cost": 2200}, {"name": "moomoo<PERSON><PERSON>", "cost": 2200}, {"name": "spicemix", "cost": 400}, {"name": "freshcream", "cost": 950}, {"name": "packagedcurry", "cost": 950}, {"name": "coconutmilk", "cost": 950}, {"name": "instantnoodles", "cost": 150}, {"name": "precookedburger", "cost": 150}, {"name": "gigantamix", "cost": 15000}, {"name": "dynamaxcrystaland458", "cost": 20}, {"name": "dynamaxcrystaland15", "cost": 20}, {"name": "dynamaxcrystaland337", "cost": 20}, {"name": "dynamaxcrystaland603", "cost": 20}, {"name": "dynamaxcrystaland390", "cost": 20}, {"name": "dynamaxcrystalsgr6879", "cost": 20}, {"name": "dynamaxcrystalsgr6859", "cost": 20}, {"name": "dynamaxcrystalsgr6913", "cost": 20}, {"name": "dynamaxcrystalsgr7348", "cost": 20}, {"name": "dynamaxcrystalsgr7121", "cost": 20}, {"name": "dynamaxcrystalsgr6746", "cost": 20}, {"name": "dynamaxcrystalsgr7194", "cost": 20}, {"name": "dynamaxcrystalsgr7337", "cost": 20}, {"name": "dynamaxcrystalsgr7343", "cost": 20}, {"name": "dynamaxcrystalsgr6812", "cost": 20}, {"name": "dynamaxcrystalsgr7116", "cost": 20}, {"name": "dynamaxcrystalsgr7264", "cost": 20}, {"name": "dynamaxcrystalsgr7597", "cost": 20}, {"name": "dynamaxcrystaldel7882", "cost": 20}, {"name": "dynamaxcrystaldel7906", "cost": 20}, {"name": "dynamaxcrystaldel7852", "cost": 20}, {"name": "dynamaxcrystalpsc596", "cost": 20}, {"name": "dynamaxcrystalpsc361", "cost": 20}, {"name": "dynamaxcrystalpsc510", "cost": 20}, {"name": "dynamaxcrystalpsc437", "cost": 20}, {"name": "dynamaxcrystalpsc8773", "cost": 20}, {"name": "dynamaxcrystallep1865", "cost": 20}, {"name": "dynamaxcrystallep1829", "cost": 20}, {"name": "dynamaxcrystalboo5340", "cost": 20}, {"name": "dynamaxcrystalboo5506", "cost": 20}, {"name": "dynamaxcrystalboo5435", "cost": 20}, {"name": "dynamaxcrystalboo5602", "cost": 20}, {"name": "dynamaxcrystalboo5733", "cost": 20}, {"name": "dynamaxcrystalboo5235", "cost": 20}, {"name": "dynamaxcrystalboo5351", "cost": 20}, {"name": "dynamaxcrystalhya3748", "cost": 20}, {"name": "dynamaxcrystalhya3903", "cost": 20}, {"name": "dynamaxcrystalhya3418", "cost": 20}, {"name": "dynamaxcrystalhya3482", "cost": 20}, {"name": "dynamaxcrystalhya3845", "cost": 20}, {"name": "dynamaxcrystaleri1084", "cost": 20}, {"name": "dynamaxcrystaleri472", "cost": 20}, {"name": "dynamaxcrystaleri1666", "cost": 20}, {"name": "dynamaxcrystaleri897", "cost": 20}, {"name": "dynamaxcrystaleri1231", "cost": 20}, {"name": "dynamaxcrystaleri874", "cost": 20}, {"name": "dynamaxcrystaleri1298", "cost": 20}, {"name": "dynamaxcrystaleri1325", "cost": 20}, {"name": "dynamaxcrystaleri984", "cost": 20}, {"name": "dynamaxcrystaleri1464", "cost": 20}, {"name": "dynamaxcrystaleri1393", "cost": 20}, {"name": "dynamaxcrystaleri850", "cost": 20}, {"name": "dynamaxcrystaltau1409", "cost": 20}, {"name": "dynamaxcrystaltau1457", "cost": 20}, {"name": "dynamaxcrystaltau1165", "cost": 20}, {"name": "dynamaxcrystaltau1791", "cost": 20}, {"name": "dynamaxcrystaltau1910", "cost": 20}, {"name": "dynamaxcrystaltau1346", "cost": 20}, {"name": "dynamaxcrystaltau1373", "cost": 20}, {"name": "dynamaxcrystaltau1412", "cost": 20}, {"name": "dynamaxcrystalcma2491", "cost": 20}, {"name": "dynamaxcrystalcma2693", "cost": 20}, {"name": "dynamaxcrystalcma2294", "cost": 20}, {"name": "dynamaxcrystalcma2827", "cost": 20}, {"name": "dynamaxcrystalcma2282", "cost": 20}, {"name": "dynamaxcrystalcma2618", "cost": 20}, {"name": "dynamaxcrystalcma2657", "cost": 20}, {"name": "dynamaxcrystalcma2646", "cost": 20}, {"name": "dynamaxcrystaluma4905", "cost": 20}, {"name": "dynamaxcrystaluma4301", "cost": 20}, {"name": "dynamaxcrystaluma5191", "cost": 20}, {"name": "dynamaxcrystaluma5054", "cost": 20}, {"name": "dynamaxcrystaluma4295", "cost": 20}, {"name": "dynamaxcrystaluma4660", "cost": 20}, {"name": "dynamaxcrystaluma4554", "cost": 20}, {"name": "dynamaxcrystaluma4069", "cost": 20}, {"name": "dynamaxcrystaluma3569", "cost": 20}, {"name": "dynamaxcrystaluma3323", "cost": 20}, {"name": "dynamaxcrystaluma4033", "cost": 20}, {"name": "dynamaxcrystaluma4377", "cost": 20}, {"name": "dynamaxcrystaluma4375", "cost": 20}, {"name": "dynamaxcrystaluma4518", "cost": 20}, {"name": "dynamaxcrystaluma3594", "cost": 20}, {"name": "dynamaxcrystalvir5056", "cost": 20}, {"name": "dynamaxcrystalvir4825", "cost": 20}, {"name": "dynamaxcrystalvir4932", "cost": 20}, {"name": "dynamaxcrystalvir4540", "cost": 20}, {"name": "dynamaxcrystalvir4689", "cost": 20}, {"name": "dynamaxcrystalvir5338", "cost": 20}, {"name": "dynamaxcrystalvir4910", "cost": 20}, {"name": "dynamaxcrystalvir5315", "cost": 20}, {"name": "dynamaxcrystalvir5359", "cost": 20}, {"name": "dynamaxcrystalvir5409", "cost": 20}, {"name": "dynamaxcrystalvir5107", "cost": 20}, {"name": "dynamaxcrystalari617", "cost": 20}, {"name": "dynamaxcrystalari553", "cost": 20}, {"name": "dynamaxcrystalari546", "cost": 20}, {"name": "dynamaxcrystalari951", "cost": 20}, {"name": "dynamaxcrystalori1713", "cost": 20}, {"name": "dynamaxcrystalori2061", "cost": 20}, {"name": "dynamaxcrystalori1790", "cost": 20}, {"name": "dynamaxcrystalori1903", "cost": 20}, {"name": "dynamaxcrystalori1948", "cost": 20}, {"name": "dynamaxcrystalori2004", "cost": 20}, {"name": "dynamaxcrystalori1852", "cost": 20}, {"name": "dynamaxcrystalori1879", "cost": 20}, {"name": "dynamaxcrystalori1899", "cost": 20}, {"name": "dynamaxcrystalori1543", "cost": 20}, {"name": "dynamaxcrystalcas21", "cost": 20}, {"name": "dynamaxcrystalcas168", "cost": 20}, {"name": "dynamaxcrystalcas403", "cost": 20}, {"name": "dynamaxcrystalcas153", "cost": 20}, {"name": "dynamaxcrystalcas542", "cost": 20}, {"name": "dynamaxcrystalcas219", "cost": 20}, {"name": "dynamaxcrystalcas265", "cost": 20}, {"name": "dynamaxcrystalcnc3572", "cost": 20}, {"name": "dynamaxcrystalcnc3208", "cost": 20}, {"name": "dynamaxcrystalcnc3461", "cost": 20}, {"name": "dynamaxcrystalcnc3449", "cost": 20}, {"name": "dynamaxcrystalcnc3429", "cost": 20}, {"name": "dynamaxcrystalcnc3627", "cost": 20}, {"name": "dynamaxcrystalcnc3268", "cost": 20}, {"name": "dynamaxcrystalcnc3249", "cost": 20}, {"name": "dynamaxcrystalcom4968", "cost": 20}, {"name": "dynamaxcrystalcrv4757", "cost": 20}, {"name": "dynamaxcrystalcrv4623", "cost": 20}, {"name": "dynamaxcrystalcrv4662", "cost": 20}, {"name": "dynamaxcrystalcrv4786", "cost": 20}, {"name": "dynamaxcrystalaur1708", "cost": 20}, {"name": "dynamaxcrystalaur2088", "cost": 20}, {"name": "dynamaxcrystalaur1605", "cost": 20}, {"name": "dynamaxcrystalaur2095", "cost": 20}, {"name": "dynamaxcrystalaur1577", "cost": 20}, {"name": "dynamaxcrystalaur1641", "cost": 20}, {"name": "dynamaxcrystalaur1612", "cost": 20}, {"name": "dynamaxcrystalpav7790", "cost": 20}, {"name": "dynamaxcrystalcet911", "cost": 20}, {"name": "dynamaxcrystalcet681", "cost": 20}, {"name": "dynamaxcrystalcet188", "cost": 20}, {"name": "dynamaxcrystalcet539", "cost": 20}, {"name": "dynamaxcrystalcet804", "cost": 20}, {"name": "dynamaxcrystalcep8974", "cost": 20}, {"name": "dynamaxcrystalcep8162", "cost": 20}, {"name": "dynamaxcrystalcep8238", "cost": 20}, {"name": "dynamaxcrystalcep8417", "cost": 20}, {"name": "dynamaxcrystalcen5267", "cost": 20}, {"name": "dynamaxcrystalcen5288", "cost": 20}, {"name": "dynamaxcrystalcen551", "cost": 20}, {"name": "dynamaxcrystalcen5459", "cost": 20}, {"name": "dynamaxcrystalcen5460", "cost": 20}, {"name": "dynamaxcrystalcmi2943", "cost": 20}, {"name": "dynamaxcrystalcmi2845", "cost": 20}, {"name": "dynamaxcrystalequ8131", "cost": 20}, {"name": "dynamaxcrystalvul7405", "cost": 20}, {"name": "dynamaxcrystalumi424", "cost": 20}, {"name": "dynamaxcrystalumi5563", "cost": 20}, {"name": "dynamaxcrystalumi5735", "cost": 20}, {"name": "dynamaxcrystalumi6789", "cost": 20}, {"name": "dynamaxcrystalcrt4287", "cost": 20}, {"name": "dynamaxcrystallyr7001", "cost": 20}, {"name": "dynamaxcrystallyr7178", "cost": 20}, {"name": "dynamaxcrystallyr7106", "cost": 20}, {"name": "dynamaxcrystallyr7298", "cost": 20}, {"name": "dynamaxcrystalara6585", "cost": 20}, {"name": "dynamaxcrystalsco6134", "cost": 20}, {"name": "dynamaxcrystalsco6527", "cost": 20}, {"name": "dynamaxcrystalsco6553", "cost": 20}, {"name": "dynamaxcrystalsco5953", "cost": 20}, {"name": "dynamaxcrystalsco5984", "cost": 20}, {"name": "dynamaxcrystalsco6508", "cost": 20}, {"name": "dynamaxcrystalsco6084", "cost": 20}, {"name": "dynamaxcrystalsco5944", "cost": 20}, {"name": "dynamaxcrystalsco6630", "cost": 20}, {"name": "dynamaxcrystalsco6027", "cost": 20}, {"name": "dynamaxcrystalsco6247", "cost": 20}, {"name": "dynamaxcrystalsco6252", "cost": 20}, {"name": "dynamaxcrystalsco5928", "cost": 20}, {"name": "dynamaxcrystalsco6241", "cost": 20}, {"name": "dynamaxcrystalsco6165", "cost": 20}, {"name": "dynamaxcrystaltri544", "cost": 20}, {"name": "dynamaxcrystalleo3982", "cost": 20}, {"name": "dynamaxcrystalleo4534", "cost": 20}, {"name": "dynamaxcrystalleo4357", "cost": 20}, {"name": "dynamaxcrystalleo4057", "cost": 20}, {"name": "dynamaxcrystalleo4359", "cost": 20}, {"name": "dynamaxcrystalleo4031", "cost": 20}, {"name": "dynamaxcrystalleo3852", "cost": 20}, {"name": "dynamaxcrystalleo3905", "cost": 20}, {"name": "dynamaxcrystalleo3773", "cost": 20}, {"name": "dynamaxcrystalgru8425", "cost": 20}, {"name": "dynamaxcrystalgru8636", "cost": 20}, {"name": "dynamaxcrystalgru8353", "cost": 20}, {"name": "dynamaxcrystallib5685", "cost": 20}, {"name": "dynamaxcrystallib5531", "cost": 20}, {"name": "dynamaxcrystallib5787", "cost": 20}, {"name": "dynamaxcrystallib5603", "cost": 20}, {"name": "dynamaxcrystalpup3165", "cost": 20}, {"name": "dynamaxcrystalpup3185", "cost": 20}, {"name": "dynamaxcrystalpup3045", "cost": 20}, {"name": "dynamaxcrystalcyg7924", "cost": 20}, {"name": "dynamaxcrystalcyg7417", "cost": 20}, {"name": "dynamaxcrystalcyg7796", "cost": 20}, {"name": "dynamaxcrystalcyg8301", "cost": 20}, {"name": "dynamaxcrystalcyg7949", "cost": 20}, {"name": "dynamaxcrystalcyg7528", "cost": 20}, {"name": "dynamaxcrystaloct7228", "cost": 20}, {"name": "dynamaxcrystalcol1956", "cost": 20}, {"name": "dynamaxcrystalcol2040", "cost": 20}, {"name": "dynamaxcrystalcol2177", "cost": 20}, {"name": "dynamaxcrystalgem2990", "cost": 20}, {"name": "dynamaxcrystalgem2891", "cost": 20}, {"name": "dynamaxcrystalgem2421", "cost": 20}, {"name": "dynamaxcrystalgem2473", "cost": 20}, {"name": "dynamaxcrystalgem2216", "cost": 20}, {"name": "dynamaxcrystalgem2777", "cost": 20}, {"name": "dynamaxcrystalgem2650", "cost": 20}, {"name": "dynamaxcrystalgem2286", "cost": 20}, {"name": "dynamaxcrystalgem2484", "cost": 20}, {"name": "dynamaxcrystalgem2930", "cost": 20}, {"name": "dynamaxcrystalpeg8775", "cost": 20}, {"name": "dynamaxcrystalpeg8781", "cost": 20}, {"name": "dynamaxcrystalpeg39", "cost": 20}, {"name": "dynamaxcrystalpeg8308", "cost": 20}, {"name": "dynamaxcrystalpeg8650", "cost": 20}, {"name": "dynamaxcrystalpeg8634", "cost": 20}, {"name": "dynamaxcrystalpeg8684", "cost": 20}, {"name": "dynamaxcrystalpeg8450", "cost": 20}, {"name": "dynamaxcrystalpeg8880", "cost": 20}, {"name": "dynamaxcrystalpeg8905", "cost": 20}, {"name": "dynamaxcrystaloph6556", "cost": 20}, {"name": "dynamaxcrystaloph6378", "cost": 20}, {"name": "dynamaxcrystaloph6603", "cost": 20}, {"name": "dynamaxcrystaloph6149", "cost": 20}, {"name": "dynamaxcrystaloph6056", "cost": 20}, {"name": "dynamaxcrystaloph6075", "cost": 20}, {"name": "dynamaxcrystalser5854", "cost": 20}, {"name": "dynamaxcrystalser7141", "cost": 20}, {"name": "dynamaxcrystalser5879", "cost": 20}, {"name": "dynamaxcrystalher6406", "cost": 20}, {"name": "dynamaxcrystalher6148", "cost": 20}, {"name": "dynamaxcrystalher6410", "cost": 20}, {"name": "dynamaxcrystalher6526", "cost": 20}, {"name": "dynamaxcrystalher6117", "cost": 20}, {"name": "dynamaxcrystalher6008", "cost": 20}, {"name": "dynamaxcrystalper936", "cost": 20}, {"name": "dynamaxcrystalper1017", "cost": 20}, {"name": "dynamaxcrystalper1131", "cost": 20}, {"name": "dynamaxcrystalper1228", "cost": 20}, {"name": "dynamaxcrystalper834", "cost": 20}, {"name": "dynamaxcrystalper941", "cost": 20}, {"name": "dynamaxcrystalphe99", "cost": 20}, {"name": "dynamaxcrystalphe338", "cost": 20}, {"name": "dynamaxcrystalvel3634", "cost": 20}, {"name": "dynamaxcrystalvel3485", "cost": 20}, {"name": "dynamaxcrystalvel3734", "cost": 20}, {"name": "dynamaxcrystalaqr8232", "cost": 20}, {"name": "dynamaxcrystalaqr8414", "cost": 20}, {"name": "dynamaxcrystalaqr8709", "cost": 20}, {"name": "dynamaxcrystalaqr8518", "cost": 20}, {"name": "dynamaxcrystalaqr7950", "cost": 20}, {"name": "dynamaxcrystalaqr8499", "cost": 20}, {"name": "dynamaxcrystalaqr8610", "cost": 20}, {"name": "dynamaxcrystalaqr8264", "cost": 20}, {"name": "dynamaxcrystalcru4853", "cost": 20}, {"name": "dynamaxcrystalcru4730", "cost": 20}, {"name": "dynamaxcrystalcru4763", "cost": 20}, {"name": "dynamaxcrystalcru4700", "cost": 20}, {"name": "dynamaxcrystalcru4656", "cost": 20}, {"name": "dynamaxcrystalpsa8728", "cost": 20}, {"name": "dynamaxcrystaltra6217", "cost": 20}, {"name": "dynamaxcrystalcap7776", "cost": 20}, {"name": "dynamaxcrystalcap7754", "cost": 20}, {"name": "dynamaxcrystalcap8278", "cost": 20}, {"name": "dynamaxcrystalcap8322", "cost": 20}, {"name": "dynamaxcrystalcap7773", "cost": 20}, {"name": "dynamaxcrystalsge7479", "cost": 20}, {"name": "dynamaxcrystalcar2326", "cost": 20}, {"name": "dynamaxcrystalcar3685", "cost": 20}, {"name": "dynamaxcrystalcar3307", "cost": 20}, {"name": "dynamaxcrystalcar3699", "cost": 20}, {"name": "dynamaxcrystaldra5744", "cost": 20}, {"name": "dynamaxcrystaldra5291", "cost": 20}, {"name": "dynamaxcrystaldra6705", "cost": 20}, {"name": "dynamaxcrystaldra6536", "cost": 20}, {"name": "dynamaxcrystaldra7310", "cost": 20}, {"name": "dynamaxcrystaldra6688", "cost": 20}, {"name": "dynamaxcrystaldra4434", "cost": 20}, {"name": "dynamaxcrystaldra6370", "cost": 20}, {"name": "dynamaxcrystaldra7462", "cost": 20}, {"name": "dynamaxcrystaldra6396", "cost": 20}, {"name": "dynamaxcrystaldra6132", "cost": 20}, {"name": "dynamaxcrystaldra6636", "cost": 20}, {"name": "dynamaxcrystalcvn4915", "cost": 20}, {"name": "dynamaxcrystalcvn4785", "cost": 20}, {"name": "dynamaxcrystalcvn4846", "cost": 20}, {"name": "dynamaxcrystalaql7595", "cost": 20}, {"name": "dynamaxcrystalaql7557", "cost": 20}, {"name": "dynamaxcrystalaql7525", "cost": 20}, {"name": "dynamaxcrystalaql7602", "cost": 20}, {"name": "dynamaxcrystalaql7235", "cost": 20}, {"name": "max<PERSON>y", "cost": 4000}, {"name": "maxmushrooms", "cost": 6000}, {"name": "galari<PERSON><PERSON>", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 3000}, {"name": "armoriteore", "cost": 20}, {"name": "galaricawreath", "cost": 3000}, {"name": "dynite<PERSON>", "cost": 20}, {"name": "carrotseeds", "cost": 20}, {"name": "abilitypatch", "cost": 20}, {"name": "tinybambooshoot", "cost": 375}, {"name": "bigbambooshoot", "cost": 1500}, {"name": "malicious<PERSON>or", "cost": 750}, {"name": "normalterashard", "cost": 500}, {"name": "fireterashard", "cost": 500}, {"name": "waterterashard", "cost": 500}, {"name": "electricterashard", "cost": 500}, {"name": "grassterashard", "cost": 500}, {"name": "iceterashard", "cost": 500}, {"name": "fightingterashard", "cost": 500}, {"name": "poisonterashard", "cost": 500}, {"name": "groundterashard", "cost": 500}, {"name": "flyingterashard", "cost": 500}, {"name": "psychicterashard", "cost": 500}, {"name": "bugterashard", "cost": 500}, {"name": "rockterashard", "cost": 500}, {"name": "ghostterashard", "cost": 500}, {"name": "dragonterashard", "cost": 500}, {"name": "darkterashard", "cost": 500}, {"name": "steelterashard", "cost": 500}, {"name": "fairy<PERSON>shard", "cost": 500}, {"name": "abilityshield", "cost": 20000}, {"name": "<PERSON><PERSON><PERSON>", "cost": 30000}, {"name": "<PERSON><PERSON><PERSON>", "cost": 30000}, {"name": "punchingglove", "cost": 15000}, {"name": "covertcloak", "cost": 20000}, {"name": "loadeddice", "cost": 20000}, {"name": "mayonnaise", "cost": 120}, {"name": "ketchup", "cost": 110}, {"name": "mustard", "cost": 130}, {"name": "butter", "cost": 250}, {"name": "peanutbutter", "cost": 300}, {"name": "chilisauce", "cost": 320}, {"name": "salt", "cost": 90}, {"name": "pepper", "cost": 100}, {"name": "yogurt", "cost": 140}, {"name": "whippedcream", "cost": 200}, {"name": "creamcheese", "cost": 280}, {"name": "jam", "cost": 120}, {"name": "marmalade", "cost": 260}, {"name": "oliveoil", "cost": 300}, {"name": "vinegar", "cost": 300}, {"name": "lettuce", "cost": 90}, {"name": "tomato", "cost": 100}, {"name": "cherrytomatoes", "cost": 120}, {"name": "cucumber", "cost": 130}, {"name": "pickle", "cost": 90}, {"name": "onion", "cost": 130}, {"name": "redonion", "cost": 230}, {"name": "greenbellpepper", "cost": 230}, {"name": "redbellpepper", "cost": 240}, {"name": "yellowbell<PERSON>pper", "cost": 240}, {"name": "avocado", "cost": 180}, {"name": "bacon", "cost": 150}, {"name": "ham", "cost": 170}, {"name": "prosciutto", "cost": 200}, {"name": "chorizo", "cost": 150}, {"name": "herbedsausage", "cost": 400}, {"name": "hamburger", "cost": 380}, {"name": "klawfstick", "cost": 500}, {"name": "smokedfillet", "cost": 330}, {"name": "friedfillet", "cost": 360}, {"name": "egg", "cost": 80}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 250}, {"name": "tofu", "cost": 260}, {"name": "rice", "cost": 280}, {"name": "noodles", "cost": 280}, {"name": "potatosalad", "cost": 110}, {"name": "cheese", "cost": 120}, {"name": "banana", "cost": 80}, {"name": "strawberry", "cost": 140}, {"name": "apple", "cost": 130}, {"name": "kiwi", "cost": 180}, {"name": "pineapple", "cost": 250}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 220}, {"name": "horseradish", "cost": 410}, {"name": "currypowder", "cost": 450}, {"name": "wasabi", "cost": 330}, {"name": "watercress", "cost": 270}, {"name": "basil", "cost": 280}, {"name": "venonatfang", "cost": 80}, {"name": "<PERSON><PERSON>dir<PERSON>", "cost": 80}, {"name": "meowthfur", "cost": 80}, {"name": "psyduckdown", "cost": 60}, {"name": "mankeyfur", "cost": 80}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "slowpokeclaw", "cost": 80}, {"name": "magnemitescrew", "cost": 80}, {"name": "grimertoxin", "cost": 80}, {"name": "<PERSON><PERSON><PERSON>l", "cost": 140}, {"name": "gastlygas", "cost": 80}, {"name": "drow<PERSON><PERSON><PERSON>", "cost": 60}, {"name": "voltorbsparks", "cost": 80}, {"name": "scytherclaw", "cost": 100}, {"name": "tauro<PERSON><PERSON>", "cost": 100}, {"name": "magikarpscales", "cost": 40}, {"name": "dittogoo", "cost": 300}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 200}, {"name": "dratiniscales", "cost": 200}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 100}, {"name": "igg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 60}, {"name": "mareepwool", "cost": 60}, {"name": "hoppipleaf", "cost": 60}, {"name": "sunkernleaf", "cost": 60}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 100}, {"name": "misdreavustears", "cost": 80}, {"name": "girafarigfur", "cost": 100}, {"name": "pinecohusk", "cost": 80}, {"name": "dunsparcescales", "cost": 120}, {"name": "qwilfishspines", "cost": 80}, {"name": "heracrossclaw", "cost": 100}, {"name": "sneasel<PERSON><PERSON>", "cost": 80}, {"name": "teddiursaclaw", "cost": 80}, {"name": "delibirdparcel", "cost": 240}, {"name": "houndourfang", "cost": 80}, {"name": "phan<PERSON><PERSON>", "cost": 80}, {"name": "s<PERSON><PERSON><PERSON><PERSON>", "cost": 120}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 200}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 60}, {"name": "raltsdust", "cost": 80}, {"name": "surskitsyrup", "cost": 60}, {"name": "shroomishspores", "cost": 80}, {"name": "slakothfur", "cost": 60}, {"name": "makuhitasweat", "cost": 80}, {"name": "azurillfur", "cost": 60}, {"name": "sableyegem", "cost": 200}, {"name": "medititesweat", "cost": 80}, {"name": "gulpinmu<PERSON>", "cost": 80}, {"name": "numellava", "cost": 80}, {"name": "torkoalcoal", "cost": 140}, {"name": "spoink<PERSON>l", "cost": 140}, {"name": "cac<PERSON>needle", "cost": 80}, {"name": "s<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 100}, {"name": "zangooseclaw", "cost": 120}, {"name": "seviperfang", "cost": 120}, {"name": "barboachslime", "cost": 60}, {"name": "shuppetscrap", "cost": 80}, {"name": "tropiusleaf", "cost": 140}, {"name": "snor<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "luvdiscscales", "cost": 160}, {"name": "bagonscales", "cost": 200}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 60}, {"name": "kricketotshell", "cost": 40}, {"name": "shinxfang", "cost": 60}, {"name": "combee<PERSON>y", "cost": 100}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 100}, {"name": "buiz<PERSON><PERSON>", "cost": 80}, {"name": "shellosmu<PERSON>", "cost": 80}, {"name": "drifloongas", "cost": 80}, {"name": "stunkyfur", "cost": 80}, {"name": "bronzorfragment", "cost": 80}, {"name": "bonslytears", "cost": 80}, {"name": "happin<PERSON>ust", "cost": 300}, {"name": "spiritombfragment", "cost": 240}, {"name": "gible<PERSON>les", "cost": 200}, {"name": "r<PERSON><PERSON><PERSON>", "cost": 160}, {"name": "hippopotassand", "cost": 80}, {"name": "croagun<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "finneonscales", "cost": 80}, {"name": "snoverberries", "cost": 60}, {"name": "rotomsparks", "cost": 200}, {"name": "petililleaf", "cost": 60}, {"name": "basculin<PERSON>g", "cost": 80}, {"name": "sandile<PERSON><PERSON>", "cost": 80}, {"name": "zoruafur", "cost": 140}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 60}, {"name": "foon<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "alomomolam<PERSON>us", "cost": 120}, {"name": "tynamoslime", "cost": 80}, {"name": "axewscales", "cost": 200}, {"name": "cubcho<PERSON>ur", "cost": 80}, {"name": "cryogonalice", "cost": 100}, {"name": "pawniardblade", "cost": 80}, {"name": "ruff<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "deinoscales", "cost": 200}, {"name": "larvestafuzz", "cost": 200}, {"name": "flet<PERSON><PERSON><PERSON><PERSON>", "cost": 60}, {"name": "scatterbugpowder", "cost": 40}, {"name": "litleotuft", "cost": 80}, {"name": "flabébé<PERSON>len", "cost": 80}, {"name": "skiddoleaf", "cost": 80}, {"name": "skrelpkelp", "cost": 100}, {"name": "claunch<PERSON><PERSON><PERSON>", "cost": 100}, {"name": "hawluchadown", "cost": 100}, {"name": "dedennefur", "cost": 100}, {"name": "goomygoo", "cost": 140}, {"name": "klef<PERSON><PERSON>", "cost": 100}, {"name": "bergmiteice", "cost": 80}, {"name": "noibatfur", "cost": 80}, {"name": "yungoosfur", "cost": 60}, {"name": "crabrawlershell", "cost": 100}, {"name": "oricoriofeather", "cost": 100}, {"name": "rockruffrock", "cost": 80}, {"name": "mareaniespike", "cost": 80}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "fomantisleaf", "cost": 80}, {"name": "salanditgas", "cost": 80}, {"name": "bounsweetsweat", "cost": 80}, {"name": "orangurufur", "cost": 120}, {"name": "passimianfur", "cost": 120}, {"name": "sandygastsand", "cost": 80}, {"name": "komalaclaw", "cost": 100}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 40}, {"name": "bruxishtooth", "cost": 100}, {"name": "chewtleclaw", "cost": 80}, {"name": "skwovetfur", "cost": 60}, {"name": "arrokudascales", "cost": 80}, {"name": "rook<PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "toxelsparks", "cost": 80}, {"name": "falinkssweat", "cost": 120}, {"name": "cufanttarnish", "cost": 80}, {"name": "rolycolycoal", "cost": 80}, {"name": "silicobrasand", "cost": 80}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 120}, {"name": "pincurchinspines", "cost": 100}, {"name": "snomthread", "cost": 80}, {"name": "impid<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "applinjuice", "cost": 100}, {"name": "sinisteachip", "cost": 80}, {"name": "hatennadust", "cost": 80}, {"name": "stonjournerstone", "cost": 120}, {"name": "eiscuedown", "cost": 120}, {"name": "dreepypowder", "cost": 200}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 60}, {"name": "tarou<PERSON><PERSON><PERSON><PERSON>", "cost": 60}, {"name": "nymbleclaw", "cost": 60}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "grea<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "flittledown", "cost": 80}, {"name": "wiglettsand", "cost": 80}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 160}, {"name": "veluzafillet", "cost": 140}, {"name": "finizenmucus", "cost": 100}, {"name": "smolivoil", "cost": 80}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "tadbulbmucus", "cost": 80}, {"name": "varoomfume", "cost": 80}, {"name": "orthwormtarnish", "cost": 200}, {"name": "tandemausfur", "cost": 80}, {"name": "cetoddlegrease", "cost": 80}, {"name": "frigiba<PERSON><PERSON><PERSON>", "cost": 200}, {"name": "tatsugiri<PERSON><PERSON>", "cost": 200}, {"name": "cyclizarscales", "cost": 120}, {"name": "paw<PERSON><PERSON>r", "cost": 100}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 120}, {"name": "bomb<PERSON><PERSON><PERSON>ather", "cost": 200}, {"name": "<PERSON>ua<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "flamigodown", "cost": 80}, {"name": "klawfclaw", "cost": 200}, {"name": "naclisalt", "cost": 80}, {"name": "glimmetcrystal", "cost": 120}, {"name": "<PERSON>hr<PERSON><PERSON><PERSON>", "cost": 80}, {"name": "fi<PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "maschifffang", "cost": 80}, {"name": "bramb<PERSON><PERSON>", "cost": 80}, {"name": "gimmighoulcoin", "cost": 800}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 80}, {"name": "char<PERSON><PERSON><PERSON><PERSON>", "cost": 160}, {"name": "toedscoolflaps", "cost": 100}, {"name": "wooperslime", "cost": 60}, {"name": "polkadotbottle", "cost": 1000}, {"name": "stripedbottle", "cost": 1000}, {"name": "diamondbottle", "cost": 1000}, {"name": "stripedcup", "cost": 800}, {"name": "polkadotcup", "cost": 800}, {"name": "flowerpa<PERSON><PERSON><PERSON>", "cost": 800}, {"name": "whimsicaltablecloth", "cost": 2000}, {"name": "leafytablecloth", "cost": 2000}, {"name": "spookytablecloth", "cost": 4000}, {"name": "marillball", "cost": 2000}, {"name": "yarnball", "cost": 1000}, {"name": "cyberball", "cost": 2000}, {"name": "goldpick", "cost": 400}, {"name": "silverpick", "cost": 40}, {"name": "redflagpick", "cost": 120}, {"name": "blueflagpick", "cost": 120}, {"name": "pikapikapick", "cost": 480}, {"name": "winkingpikapic<PERSON>", "cost": 1200}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 480}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 1200}, {"name": "bluepokéballpick", "cost": 200}, {"name": "auspiciousarmor", "cost": 3000}, {"name": "leader’screst", "cost": 3000}, {"name": "pinkbottle", "cost": 2000}, {"name": "bluebottle", "cost": 2000}, {"name": "yellowbottle", "cost": 2000}, {"name": "steelbottle(r)", "cost": 1500}, {"name": "steelbottle(y)", "cost": 1500}, {"name": "steelbottle(b)", "cost": 1500}, {"name": "silverbottle", "cost": 20000}, {"name": "barredcup", "cost": 800}, {"name": "diamond<PERSON><PERSON><PERSON><PERSON>", "cost": 800}, {"name": "firepatterncup", "cost": 800}, {"name": "pinkcup", "cost": 1500}, {"name": "bluecup", "cost": 1500}, {"name": "yellowcup", "cost": 1500}, {"name": "pikachucup", "cost": 2000}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 2000}, {"name": "slowpokecup", "cost": 2000}, {"name": "silvercup", "cost": 20000}, {"name": "exerciseball", "cost": 2000}, {"name": "plaidtablecloth(y)", "cost": 3000}, {"name": "plaidtablecloth(b)", "cost": 3000}, {"name": "plaidtablecloth(r)", "cost": 3000}, {"name": "battletablecloth", "cost": 4000}, {"name": "monstroustablecloth", "cost": 2000}, {"name": "stripedtablecloth", "cost": 1500}, {"name": "diamondtablecloth", "cost": 1500}, {"name": "polkadottablecloth", "cost": 1500}, {"name": "lilactablecloth", "cost": 1000}, {"name": "minttablecloth", "cost": 1000}, {"name": "peachtablecloth", "cost": 1000}, {"name": "yellowtablecloth", "cost": 5000}, {"name": "bluetablecloth", "cost": 5000}, {"name": "pinktablecloth", "cost": 5000}, {"name": "goldbottle", "cost": 30000}, {"name": "bronzebottle", "cost": 10000}, {"name": "goldcup", "cost": 15000}, {"name": "bronzecup", "cost": 5000}, {"name": "greenpokéballpick", "cost": 200}, {"name": "redpokéballpick", "cost": 200}, {"name": "partysparklerpick", "cost": 1600}, {"name": "heroicswordpick", "cost": 2000}, {"name": "magicalstarpick", "cost": 600}, {"name": "magicalheartpick", "cost": 600}, {"name": "parasolpick", "cost": 800}, {"name": "blueskyflowerpick", "cost": 1000}, {"name": "sunsetflowerpick", "cost": 1000}, {"name": "sunriseflowerpick", "cost": 1000}, {"name": "fairyfeather", "cost": 750}, {"name": "syrupyapple", "cost": 500}, {"name": "unremarkable<PERSON><PERSON>up", "cost": 400}, {"name": "masterpieceteacup", "cost": 9500}, {"name": "healthmochi", "cost": 125}, {"name": "<PERSON><PERSON><PERSON>", "cost": 125}, {"name": "resistmochi", "cost": 125}, {"name": "<PERSON><PERSON><PERSON>", "cost": 125}, {"name": "<PERSON><PERSON><PERSON>", "cost": 125}, {"name": "swift<PERSON><PERSON>", "cost": 125}, {"name": "freshstartmochi", "cost": 75}, {"name": "ekansfang", "cost": 15}, {"name": "sandshrewclaw", "cost": 10}, {"name": "cle<PERSON><PERSON><PERSON>", "cost": 25}, {"name": "vulpixfur", "cost": 35}, {"name": "poliwagslime", "cost": 35}, {"name": "bellsproutvine", "cost": 25}, {"name": "geodudefragment", "cost": 30}, {"name": "koffinggas", "cost": 25}, {"name": "munch<PERSON>fang", "cost": 40}, {"name": "sentretfur", "cost": 10}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 15}, {"name": "spinarakthread", "cost": 10}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 30}, {"name": "yanmaspike", "cost": 35}, {"name": "gligarfang", "cost": 40}, {"name": "slugmalava", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 35}, {"name": "poochy<PERSON><PERSON>g", "cost": 15}, {"name": "lotadleaf", "cost": 20}, {"name": "seedotstem", "cost": 20}, {"name": "nosepassfragment", "cost": 40}, {"name": "volbeatfluid", "cost": 10}, {"name": "illumi<PERSON><PERSON><PERSON>", "cost": 10}, {"name": "corphis<PERSON><PERSON>", "cost": 10}, {"name": "feebasscales", "cost": 35}, {"name": "duskullfragment", "cost": 40}, {"name": "chinglingfragment", "cost": 20}, {"name": "timburrsweat", "cost": 30}, {"name": "sewaddle<PERSON>", "cost": 20}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "cost": 15}, {"name": "litwicksoot", "cost": 30}, {"name": "<PERSON>en<PERSON><PERSON><PERSON>", "cost": 40}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 35}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cost": 50}, {"name": "phan<PERSON><PERSON><PERSON>", "cost": 20}, {"name": "grubb<PERSON>hread", "cost": 25}, {"name": "cutieflypowder", "cost": 25}, {"name": "jangmooscales", "cost": 50}, {"name": "cramorantdown", "cost": 30}, {"name": "morpekosnack", "cost": 15}, {"name": "poltchageistpowder", "cost": 30}, {"name": "linkingcord", "cost": 8000}]}
package poke

import (
	"go-nakama-poke/proto/MainServer"
	"log"
	"os"

	"google.golang.org/protobuf/encoding/protojson"
)

// // 全局变量存储所有 Pokémon 数据
// var pokemonInfos map[string]PokemonInfo
var pokeCateLevelInfos *MainServer.PokeAllCateLevelInfo

// LoadPokeCateLevels 加载 Pokedex 数据到全局变量 pokeCateLevelInfos
func LoadPokeCateLevels() {
	// 读取 JSON 文件
	data, err := os.ReadFile("/nakama/data/pokeLevels.json")
	if err != nil {
		log.Fatalf("Failed to read pokeLevels.json file: %v", err)
	}
	pokeCateLevelInfos = &MainServer.PokeAllCateLevelInfo{}
	if err := protojson.Unmarshal(data, pokeCateLevelInfos); err != nil {
		log.Fatalf("Failed to parse pokeLevels.json: %v", err)
	}
	log.Printf("Successfully loaded pokeLevels.json")
}

func GetPokeCateLevelBy(nameId string) (*MainServer.PokeCateLevelInfo, bool) {
	// 检查 name 是否在 map 中
	pokeCateLevel, exists := pokeCateLevelInfos.PokeToLevel[nameId]
	if !exists {
		return nil, exists
	}
	return pokeCateLevel, true
}

// func GetAllPokemonInfo() map[string]*MainServer.PSPokemonData {
// 	pokemonData := pokemonInfos
// 	return pokemonData
// }

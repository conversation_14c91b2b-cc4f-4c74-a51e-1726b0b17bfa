package poke

import (
	"encoding/json"
	"errors"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"strconv"

	// "go-nakama-poke/proto/GameManager"

	"log"
	"math/rand"
	"os"
)

type PokeDetail struct {
	MinLevel     int    `json:"min_level"`
	MaxLevel     int    `json:"max_level"`
	MaxChance    int    `json:"max_chance"`
	MethodString string `json:"method_string"`
	Method       int    `json:"method"`
	Version      string `json:"version"`
}

type EncounterMethodData struct {
	Pokes     map[string]PokeDetail `json:"pokes"`
	MaxChance int                   `json:"max_chance"`
}

type RegionMap = map[string]map[string]map[MainServer.EncounterMethod]EncounterMethodData

var regionMap RegionMap

func LoadRegionMap() {
	// 读取 JSON
	data, err := os.ReadFile("/nakama/data/region.json")
	if err != nil {
		log.Fatalf("❌ Failed to read region.json: %v", err)
	}

	// 暂时接收原始 JSON 数据
	var raw map[string][]map[string]map[string]EncounterMethodData
	if err := json.Unmarshal(data, &raw); err != nil {
		log.Fatalf("❌ Failed to parse region.json: %v", err)
	}

	regionMap = make(RegionMap)

	for regionKey, regionList := range raw {
		if _, ok := regionMap[regionKey]; !ok {
			regionMap[regionKey] = make(map[string]map[MainServer.EncounterMethod]EncounterMethodData)
		}

		for _, areaMap := range regionList {
			for areaKey, methodMap := range areaMap {
				if _, ok := regionMap[regionKey][areaKey]; !ok {
					regionMap[regionKey][areaKey] = make(map[MainServer.EncounterMethod]EncounterMethodData)
				}

				for methodStr, data := range methodMap {
					methodInt, err := strconv.Atoi(methodStr)
					if err != nil {
						log.Printf("⚠️ Invalid method key: %s", methodStr)
						continue
					}
					method := MainServer.EncounterMethod(methodInt)
					regionMap[regionKey][areaKey][method] = data
				}
			}
		}
	}

	log.Printf("✅ RegionMap loaded: %d regions", len(regionMap))
}

type PokemonResult struct {
	Name   string
	Level  int
	Detail PokeDetail
}

func GetPokemonByChance(regionKey, areaKey string, method MainServer.EncounterMethod) (*PokemonResult, error) {
	areaMap, ok := regionMap[regionKey]
	if !ok {
		return nil, fmt.Errorf("region not found: %s", regionKey)
	}

	methodMap, ok := areaMap[areaKey]
	if !ok {
		return nil, fmt.Errorf("area not found: %s", areaKey)
	}

	data, ok := methodMap[method]
	if !ok {
		return nil, fmt.Errorf("method not found: %v", method)
	}

	if len(data.Pokes) == 0 || data.MaxChance == 0 {
		return nil, fmt.Errorf("no pokémon data available")
	}

	// 随机选择
	// rand.Seed(time.Now().UnixNano())
	r := rand.Intn(data.MaxChance) + 1

	for name, poke := range data.Pokes {
		r -= poke.MaxChance
		if r <= 0 {
			level := poke.MinLevel
			if poke.MaxLevel > poke.MinLevel {
				level = rand.Intn(poke.MaxLevel-poke.MinLevel+1) + poke.MinLevel
			}
			return &PokemonResult{
				Name:   name,
				Level:  level,
				Detail: poke,
			}, nil
		}
	}

	return nil, errors.New("failed to select pokémon, check data integrity")
}

// 数据结构定义// Pokemon 数据结构
// type PokemonChance struct {
// 	Name      string         `json:"name"`
// 	MaxChance int            `json:"max_chance"`
// 	Level     map[string]int `json:"level"`
// }

// // Area 数据结构，包含 Pokemon 列表和区域最大概率
// type Area struct {
// 	Pokes     []PokemonChance `json:"pokes"`
// 	MaxChance int             `json:"max_chance"`
// }

// // Region 数据结构，表示多个区域
// type Region map[string]Area

// // Regions 数据结构，表示多个区域的映射
// type Regions map[string][]Region

// var regionMap Regions

// // var regionMap map[string]map[string]PokemonChance

// func LoadRegionMap() {
// 	// 读取 JSON 文件
// 	data, err := os.ReadFile("/nakama/data/region.json")
// 	if err != nil {
// 		log.Fatalf("Failed to read region.json file: %v", err)
// 	}

// 	// 解析 JSON 数据直接到全局变量
// 	if err := json.Unmarshal(data, &regionMap); err != nil {
// 		log.Fatalf("Failed to parse region.json: %v", err)
// 	}

// 	log.Printf("Successfully loaded regions: %d entries.", len(regionMap))
// }

// // GetPokemonByChance 根据 key1 和 key2 返回一个随机 Pokémon
// func GetPokemonByChance(reginKey string, areaKey string) (*PokemonChance, error) {
// 	// 检查 key1 是否存在
// 	regionData, exists := regionMap[reginKey]
// 	if !exists {
// 		return nil, errors.New("region not found")
// 	}

// 	// 查找 key2 区域
// 	var area *Area
// 	for _, region := range regionData {
// 		if a, ok := region[areaKey]; ok {
// 			area = &a
// 			break
// 		}
// 	}

// 	if area == nil {
// 		return nil, errors.New("area not found")
// 	}

// 	// 初始化随机数种子
// 	rand.Seed(time.Now().UnixNano())

// 	// 在 1 到 MaxChance 范围内生成随机数
// 	randomNum := rand.Intn(area.MaxChance) + 1

// 	// 遍历 Pokémon，逐步减去其 MaxChance
// 	for _, poke := range area.Pokes {
// 		randomNum -= poke.MaxChance
// 		if randomNum <= 0 {
// 			return &poke, nil
// 		}
// 	}

// 	return nil, errors.New("no Pokémon selected, data might be invalid")
// }

// // DoesPokemonExist 判断指定 key1、key2 和 Pokémon 名称是否存在
// func DoesPokemonExist(reginKey string, areaKey string, pokeName string) (bool, error) {
// 	// 检查 key1 是否存在
// 	regionData, exists := regionMap[reginKey]
// 	if !exists {
// 		return false, errors.New("region not found")
// 	}

// 	// 查找 key2 区域
// 	var area *Area
// 	for _, region := range regionData {
// 		if a, ok := region[areaKey]; ok {
// 			area = &a
// 			break
// 		}
// 	}

// 	if area == nil {
// 		return false, errors.New("area not found")
// 	}

// 	// 遍历 Pokémon 检查名称是否存在
// 	for _, poke := range area.Pokes {
// 		if poke.Name == pokeName {
// 			return true, nil
// 		}
// 	}

// 	return false, nil
// }

// 为了方便测试先保存到数据库中
// func SavePokemonDistribution(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	userID, ok := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
// 	if !ok {
// 		return "", runtime.NewError("无法获取用户ID", 500)
// 	}

// 	// 检查用户是否是Data管理员
// 	isData, err := user.IsDataServer(ctx, nk, userID)
// 	if err != nil || !isData {
// 		return "", runtime.NewError("没有权限修改数据", 403)
// 	}

// 	bytePayload, err := base64.StdEncoding.DecodeString(payload)
// 	if err != nil {
// 		return "", runtime.NewError("Base64 解码失败", 400)
// 	}
// 	// 解析输入的区域和宝可梦分布数据
// 	var regionList GameManager.RegionList
// 	err = proto.Unmarshal(bytePayload, &regionList)
// 	if err != nil {
// 		return "", runtime.NewError("Protobuf 解析失败", 400)
// 	}

// 	// 用于存储批量写入的操作
// 	storageWrites := make([]*runtime.StorageWrite, 0)

// 	// 遍历每个区域并创建对应的 StorageWrite
// 	for _, region := range regionList.Regions {
// 		// 将 region 序列化为 JSON 字符串
// 		regionJSON, err := json.Marshal(region)
// 		if err != nil {
// 			return "", runtime.NewError("区域数据序列化失败", 500)
// 		}

// 		// 将区域ID作为存储键值
// 		storageWrite := &runtime.StorageWrite{
// 			Collection:      "pokemon_map",
// 			Key:             region.Id,          // 使用每个区域的ID作为键
// 			Value:           string(regionJSON), // 存储序列化后的 JSON 字符串
// 			UserID:          "",                 // 公共存储
// 			PermissionRead:  2,                  // 公开可读
// 			PermissionWrite: 0,                  // 不允许其他用户修改
// 		}

// 		// 将该 StorageWrite 添加到批量操作列表中
// 		storageWrites = append(storageWrites, storageWrite)
// 	}

// 	// 批量写入存储
// 	_, err = nk.StorageWrite(ctx, storageWrites)
// 	if err != nil {
// 		return "", runtime.NewError("存储失败", 500)
// 	}

// 	return "宝可梦分布已保存", nil
// }
// func GetRegionById(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, regionId string) (*GameManager.Region, error) {
// 	// 定义从存储中读取的存储键
// 	storageRead := &runtime.StorageRead{
// 		Collection: "pokemon_map",
// 		Key:        regionId,
// 		UserID:     "", // 公共存储
// 	}

// 	// 执行读取操作
// 	storageObjects, err := nk.StorageRead(ctx, []*runtime.StorageRead{storageRead})
// 	if err != nil || len(storageObjects) == 0 {
// 		logger.Error("未找到区域数据，区域ID: %s", regionId)
// 		return nil, runtime.NewError("未找到区域数据", 404)
// 	}

// 	// 获取存储的JSON数据
// 	regionJSON := storageObjects[0].Value

// 	// 反序列化JSON为Region结构
// 	var region GameManager.Region
// 	err = json.Unmarshal([]byte(regionJSON), &region)
// 	if err != nil {
// 		logger.Error("区域数据反序列化失败: %v", err)
// 		return nil, runtime.NewError("区域数据反序列化失败", 500)
// 	}

// 	// 返回反序列化的 Protobuf Region 对象
// 	return &region, nil
// }

// func getFilteredRegions(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
// 	filter := payload
// 	pageSize := 10  // 每页数据大小
// 	page, _ := strconv.Atoi(ctx.Value("page").(string)) // 当前页码，从请求上下文获取

// 	// 从存储中读取所有区域的宝可梦分布数据
// 	query := &runtime.StorageListQuery{
// 		Collection: "pokemon_distribution",
// 		Limit:      pageSize,
// 		Offset:     page * pageSize,
// 	}

// 	objects, _, err := nk.StorageList(ctx, query)
// 	if err != nil {
// 		return "", runtime.NewError("存储查询失败", 500)
// 	}

// 	var matchedRegions []Region
// 	for _, obj := range objects {
// 		if strings.Contains(obj.Key, filter) {
// 			var region Region
// 			if err := json.Unmarshal([]byte(obj.Value), &region); err != nil {
// 				logger.Error("无法解析存储的区域数据：%v", err)
// 				continue
// 			}
// 			matchedRegions = append(matchedRegions, region)
// 		}
// 	}

// 	matchedRegionsJSON, err := json.Marshal(matchedRegions)
// 	if err != nil {
// 		return "", runtime.NewError("无法序列化区域数据", 500)
// 	}

// 	return string(matchedRegionsJSON), nil
// }

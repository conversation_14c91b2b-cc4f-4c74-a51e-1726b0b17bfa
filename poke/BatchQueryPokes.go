package poke

import (
	"context"
	"database/sql"
	"go-nakama-poke/proto/MainServer"
)

// BatchQueryPokesByIds 批量查询宝可梦信息
// 这个函数是 QueryPokesByIdsAndUpdateTs 的包装器，用于批量查询宝可梦
func BatchQueryPokesByIds(ctx context.Context, tx *sql.Tx, tid int64, pokeIds []int64) ([]*MainServer.Poke, error) {
	// 使用现有的 QueryPokesByIdsAndUpdateTs 函数，updateTs 设为 0 表示不按时间戳过滤
	return QueryPokesByIdsAndUpdateTs(ctx, tx, tid, pokeIds, 0)
}

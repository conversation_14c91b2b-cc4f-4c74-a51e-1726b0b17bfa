package poke

import (
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"log"
	"os"
	"regexp"
	"strconv"
)

// MoveFilter 结构体用于传递筛选条件，包含是否只获取最大等级的选项
type MoveFilter struct {
	PokemonName  string                       // 宝可梦名称
	Gen          int32                        // 世代编号
	Level        int32                        // 必填: 等级
	Methods      []MainServer.MoveLearnMethod // 学习方式筛选（可选，支持多个）
	MaxLevelOnly bool                         // 是否只获取最高等级的技能
	IsInit bool
}

// 全局变量存储 Learnsets
var moveLearnsets = make(map[string]map[int32]map[string]*MainServer.GenerationMove)

// GetFilteredMoves 根据传入的 MoveFilter 过滤并返回符合条件的技能名称列表
func GetFilteredMoves(filter MoveFilter) []string {
	var result []string
	var maxLevelMove string
	var maxLevel int32 = -1
	var foundLevelMove bool = false

	// 检查是否存在对应的宝可梦
	if gens, ok := moveLearnsets[filter.PokemonName]; ok {
		var moves map[string]*MainServer.GenerationMove
		genFound := false
		var checkedGens []int32

		// 优先检查是否存在对应的世代
		if specificMoves, ok := gens[filter.Gen]; ok {
			moves = specificMoves
			genFound = true
			checkedGens = append(checkedGens, filter.Gen)
		} else {
			// 如果指定世代不存在，则从 gens[9] 开始向下查找
			for gen := 9; gen >= 1; gen-- { // 假设支持 9 世代到 1 世代
				if fallbackMoves, ok := gens[int32(gen)]; ok {
					moves = fallbackMoves
					genFound = true
					checkedGens = append(checkedGens, int32(gen))
					
					// 如果不是初始化模式，找到第一个就可以直接break
					if !filter.IsInit {
						break
					}
					
					// 在初始化模式下，需要确认是否找到了Level技能
					if filter.IsInit {
						// 检查当前世代是否有符合条件的Level技能
						for _, move := range fallbackMoves {
							if move.Level != nil && *move.Level <= filter.Level {
								if len(filter.Methods) == 0 || containsMethod(filter.Methods, move.Method) {
									foundLevelMove = true
									break
								}
							}
						}
						
						// 如果找到了符合条件的Level技能，那么可以跳出循环
						if foundLevelMove {
							break
						}
					}
				}
			}
		}
		
		// 如果是初始化模式，但没有找到符合条件的Level技能，且至少找到了一个世代的技能
		if filter.IsInit && !foundLevelMove && len(checkedGens) > 0 {
			// 从已检查的世代中随机选择一个技能
			for _, genID := range checkedGens {
				if genMoves, ok := gens[genID]; ok && len(genMoves) > 0 {
					// 只要有技能就添加到结果中，不再过滤Level和Method
					for moveName := range genMoves {
						result = append(result, moveName)
						break // 只需要一个技能
					}
					if len(result) > 0 {
						break // 找到技能了，可以退出循环
					}
				}
			}
			
			// 如果已经找到了随机技能，直接返回
			if len(result) > 0 {
				return result
			}
		}
		
		// 检查是否存在对应的世代
		if genFound {
			// 遍历每一个技能，按条件过滤
			for moveName, move := range moves {
				// 如果技能的等级大于给定等级，跳过
				if move.Level == nil || *move.Level > filter.Level {
					continue
				}

				// 如果提供了学习方式筛选条件
				if len(filter.Methods) > 0 {
					match := false
					// 遍历学习方式，检查是否符合任意一个方式
					for _, method := range filter.Methods {
						if move.Method == method {
							match = true
							break
						}
					}
					// 如果不符合任何指定的学习方式，跳过
					if !match {
						continue
					}
				}

				// 如果只获取最高等级技能名称
				if filter.MaxLevelOnly {
					if *move.Level > maxLevel {
						maxLevel = *move.Level
						maxLevelMove = moveName
					}
				} else {
					// 如果不限制，只要符合条件，加入结果
					result = append(result, moveName)
				}
			}
		}
	}

	// 如果 MaxLevelOnly 为 true，返回最高等级技能名称
	if filter.MaxLevelOnly && maxLevelMove != "" {
		return []string{maxLevelMove}
	}

	return result
}

// containsMethod 检查学习方式是否在给定列表中
func containsMethod(methods []MainServer.MoveLearnMethod, method MainServer.MoveLearnMethod) bool {
	for _, m := range methods {
		if m == method {
			return true
		}
	}
	return false
}

// CanLearnMove 判断指定 Pokémon 是否能在指定的等级通过指定的学习方式（如果提供）学会某个技能
func CanLearnMove(pokemonName string, moveName string, gen int32, level int32, method MainServer.MoveLearnMethod) bool {
	// 查找宝可梦是否存在
	if gens, ok := moveLearnsets[pokemonName]; ok {
		// 查找该世代是否存在
		if moves, ok := gens[gen]; ok {
			// 查找技能是否存在
			if move, ok := moves[moveName]; ok {
				// 检查等级条件
				if move.Level != nil && *move.Level <= level {
					// 如果方法为 nil，忽略学习方式的检查，直接返回 true
					// if method == nil {
					// 	return true
					// }

					// 如果提供了学习方式，检查是否匹配
					if move.Method == method {
						return true
					}
				}
			}
		}
	}
	// 如果未满足条件，返回 false
	return false
}

// LoadLearnsets 加载并解析 JSON 文件，将结果存储到全局变量中
func LoadLearnsets() {
	// 从文件读取 JSON 数据
	jsonFile, err := os.ReadFile("/nakama/data/learnsets.json")
	if err != nil {
		log.Fatalf("Failed to read file: %v", err)
	}

	// 解析 JSON 数据并存储到全局变量中
	err = parse(string(jsonFile))
	if err != nil {
		log.Fatalf("Error parsing JSON: %v", err)
	}

	fmt.Printf("Loaded Moves for %d Pokémon\n", len(moveLearnsets))
}

// Parse 函数将 JSON 字符串解析为全局变量 moveLearnsets
func parse(jsonString string) error {
	// 定义 JSON 对象结构: map[PokeName] -> map[MoveName] -> []GenerationInfo
	var jsonObject map[string]map[string][]string
	// 解析 JSON 字符串
	err := json.Unmarshal([]byte(jsonString), &jsonObject)
	if err != nil {
		return err
	}

	// 正则表达式，用于匹配世代编号和学习方式
	re := regexp.MustCompile(`(\d+)([A-Za-z]+)(\d+)?`) // 捕获世代编号、学习方式以及可选的等级

	for pokemonName, moves := range jsonObject {
		if _, ok := moveLearnsets[pokemonName]; !ok {
			moveLearnsets[pokemonName] = make(map[int32]map[string]*MainServer.GenerationMove)
		}

		for moveName, generations := range moves {
			for _, gen := range generations {
				match := re.FindStringSubmatch(gen)
				if len(match) > 0 {
					generation, _ := strconv.Atoi(match[1]) // 世代编号
					methodStr := match[2]                   // 学习方式字符串
					var level int32 = 0                     // 初始化等级为0
					if len(match) > 3 && match[3] != "" {   // 如果有等级
						lvl, _ := strconv.Atoi(match[3])
						level = int32(lvl) // 学习等级
					}

					// 将字符串映射为对应的枚举值
					var method MainServer.MoveLearnMethod
					switch methodStr {
					case "M":
						method = MainServer.MoveLearnMethod_MACHINE
					case "E":
						method = MainServer.MoveLearnMethod_EGG_MOVE
					case "T":
						method = MainServer.MoveLearnMethod_TUTOR
					case "S":
						method = MainServer.MoveLearnMethod_EVENT_S
					case "L":
						method = MainServer.MoveLearnMethod_LEVEL_UP
					case "V":
						method = MainServer.MoveLearnMethod_VIRTUAL
					default:
						method = MainServer.MoveLearnMethod_OTHER
					}

					// 创建 GenerationMove 消息并添加到全局 map 中
					generationMove := &MainServer.GenerationMove{
						Generation: int32(generation),
						Method:     method,
						Level:      &level, // 等级可以是 0 或指定等级
					}

					// 初始化世代中的技能 map
					if moveLearnsets[pokemonName][int32(generation)] == nil {
						moveLearnsets[pokemonName][int32(generation)] = make(map[string]*MainServer.GenerationMove)
					}

					// 将每个技能学习方式存入 map 中
					moveLearnsets[pokemonName][int32(generation)][moveName] = generationMove
				}
			}
		}
	}

	return nil
}
